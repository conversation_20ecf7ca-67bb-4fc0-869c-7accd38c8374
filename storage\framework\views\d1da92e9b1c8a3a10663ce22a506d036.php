<?php $__env->startSection('title', 'Valuasi Inventori'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-boxes me-2"></i>
                        Valuasi Inventori
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5>Bahan Baku</h5>
                                    <h3>Rp <?php echo e(number_format($rawTotal, 0, ',', '.')); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5>Produk Jadi</h5>
                                    <h3>Rp <?php echo e(number_format($processedTotal, 0, ',', '.')); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5>Produk Lain</h5>
                                    <h3>Rp <?php echo e(number_format($otherTotal, 0, ',', '.')); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-dark text-white">
                                <div class="card-body">
                                    <h5>Total Inventori</h5>
                                    <h3>Rp <?php echo e(number_format($grandTotal, 0, ',', '.')); ?></h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Raw Inventory -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Bahan Baku</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Nama</th>
                                            <th>Batch</th>
                                            <th class="text-end">Stok (kg)</th>
                                            <th class="text-end">Harga/kg</th>
                                            <th class="text-end">Total Nilai</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__empty_1 = true; $__currentLoopData = $rawInventory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td><?php echo e($item->name); ?></td>
                                            <td><?php echo e($item->batch_number); ?></td>
                                            <td class="text-end"><?php echo e(number_format($item->current_stock, 2)); ?></td>
                                            <td class="text-end">Rp <?php echo e(number_format($item->cost_per_kg, 0, ',', '.')); ?></td>
                                            <td class="text-end">Rp <?php echo e(number_format($item->total_value, 0, ',', '.')); ?></td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="5" class="text-center">Tidak ada data bahan baku</td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr class="fw-bold">
                                            <td colspan="4">Total Bahan Baku</td>
                                            <td class="text-end">Rp <?php echo e(number_format($rawTotal, 0, ',', '.')); ?></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Processed Inventory -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Produk Jadi</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Nama</th>
                                            <th>Batch</th>
                                            <th class="text-end">Stok (unit)</th>
                                            <th class="text-end">Harga/unit</th>
                                            <th class="text-end">Total Nilai</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__empty_1 = true; $__currentLoopData = $processedInventory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td><?php echo e($item->name); ?></td>
                                            <td><?php echo e($item->batch_number); ?></td>
                                            <td class="text-end"><?php echo e(number_format($item->current_stock)); ?></td>
                                            <td class="text-end">Rp <?php echo e(number_format($item->cost_per_unit, 0, ',', '.')); ?></td>
                                            <td class="text-end">Rp <?php echo e(number_format($item->total_value, 0, ',', '.')); ?></td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="5" class="text-center">Tidak ada data produk jadi</td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr class="fw-bold">
                                            <td colspan="4">Total Produk Jadi</td>
                                            <td class="text-end">Rp <?php echo e(number_format($processedTotal, 0, ',', '.')); ?></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Other Products -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Produk Lain</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Nama</th>
                                            <th class="text-end">Stok (unit)</th>
                                            <th class="text-end">Harga Beli</th>
                                            <th class="text-end">Total Nilai</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__empty_1 = true; $__currentLoopData = $otherProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td><?php echo e($item->name); ?></td>
                                            <td class="text-end"><?php echo e(number_format($item->current_stock)); ?></td>
                                            <td class="text-end">Rp <?php echo e(number_format($item->purchase_price, 0, ',', '.')); ?></td>
                                            <td class="text-end">Rp <?php echo e(number_format($item->total_value, 0, ',', '.')); ?></td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="4" class="text-center">Tidak ada data produk lain</td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr class="fw-bold">
                                            <td colspan="3">Total Produk Lain</td>
                                            <td class="text-end">Rp <?php echo e(number_format($otherTotal, 0, ',', '.')); ?></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Grand Total -->
                    <div class="card mt-4 bg-dark text-white">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h4 class="mb-0">TOTAL NILAI INVENTORI</h4>
                                    <small>Per tanggal: <?php echo e(date('d M Y')); ?></small>
                                </div>
                                <div class="col-md-4 text-end">
                                    <h2 class="mb-0">Rp <?php echo e(number_format($grandTotal, 0, ',', '.')); ?></h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/reports/inventory_valuation.blade.php ENDPATH**/ ?>