<!DOCTYPE html>
<html>
<head>
    <title>User Management - Direct Access</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .alert { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
        .btn { padding: 5px 10px; margin: 2px; text-decoration: none; border-radius: 3px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <h1>👥 Manajemen User - Direct Access</h1>
    
    <div class='alert alert-success'>
        ✅ Database connection successful! Module is working properly.
    </div>
    
    <div class='alert alert-info'>
        📊 Statistics: 6 total users, 5 active, 2 admin, 4 employee
    </div>
    
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Email</th>
                <th>Role</th>
                <th>Created</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody><tr>
            <td>9</td>
            <td>mikel</td>
            <td><EMAIL></td>
            <td>Employee</td>
            <td>2025-07-19 05:08:31</td>
            <td>
                <a href='#' class='btn btn-primary'>View</a>
                <a href='#' class='btn btn-warning'>Edit</a>
                <a href='#' class='btn btn-danger'>Delete</a>
            </td>
        </tr><tr>
            <td>8</td>
            <td>Super Administrator</td>
            <td><EMAIL></td>
            <td>Admin</td>
            <td>2025-07-19 05:03:38</td>
            <td>
                <a href='#' class='btn btn-primary'>View</a>
                <a href='#' class='btn btn-warning'>Edit</a>
                <a href='#' class='btn btn-danger'>Delete</a>
            </td>
        </tr><tr>
            <td>7</td>
            <td>mikel</td>
            <td><EMAIL></td>
            <td>Employee</td>
            <td>2025-07-19 03:08:37</td>
            <td>
                <a href='#' class='btn btn-primary'>View</a>
                <a href='#' class='btn btn-warning'>Edit</a>
                <a href='#' class='btn btn-danger'>Delete</a>
            </td>
        </tr><tr>
            <td>3</td>
            <td>Karyawan Toko</td>
            <td><EMAIL></td>
            <td>Employee</td>
            <td>2025-05-14 05:27:27</td>
            <td>
                <a href='#' class='btn btn-primary'>View</a>
                <a href='#' class='btn btn-warning'>Edit</a>
                <a href='#' class='btn btn-danger'>Delete</a>
            </td>
        </tr><tr>
            <td>2</td>
            <td>Admin</td>
            <td><EMAIL></td>
            <td>Admin</td>
            <td>2025-05-13 18:40:51</td>
            <td>
                <a href='#' class='btn btn-primary'>View</a>
                <a href='#' class='btn btn-warning'>Edit</a>
                <a href='#' class='btn btn-danger'>Delete</a>
            </td>
        </tr></tbody>
    </table>
    
    <div class='alert alert-info'>
        💡 This is a direct database access test. The main /users route should work similarly.
        <br>If you see this page, it means the database and user data are accessible.
        <br>Try refreshing the main /users page or clearing your browser cache.
    </div>
    
</body>
</html>