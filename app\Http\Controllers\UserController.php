<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin'); // Only admin can access user management
        // Note: timeout prevention will be handled in the methods directly
    }

    /**
     * Display a listing of users.
     */
    public function index(Request $request)
    {
        // FORCE CLEAR ANY SESSION ERRORS
        session()->forget('error');
        session()->forget('errors');
        session()->forget('message');

        try {
            // Set timeout prevention
            set_time_limit(0);
            ini_set('max_execution_time', 0);

            $query = User::query();

            // Search functionality
            if ($request->has('search') && $request->get('search')) {
                $search = $request->get('search');
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            }

            // Role filter
            if ($request->has('role') && $request->get('role')) {
                $query->where('role', $request->get('role'));
            }

            // Status filter (active/inactive based on deleted_at)
            if ($request->has('status')) {
                if ($request->get('status') === 'active') {
                    $query->whereNull('deleted_at');
                } elseif ($request->get('status') === 'inactive') {
                    $query->onlyTrashed();
                }
            } else {
                // Default: show only active users
                $query->whereNull('deleted_at');
            }

            $users = $query->orderBy('created_at', 'desc')->paginate(10);

            // Get role statistics with optimized queries
            $roleStats = [
                'total' => DB::table('users')->count(),
                'admin' => DB::table('users')->where('role', User::ROLE_ADMIN)->count(),
                'employee' => DB::table('users')->where('role', User::ROLE_EMPLOYEE)->count(),
                'active' => DB::table('users')->whereNull('deleted_at')->count(),
                'inactive' => DB::table('users')->whereNotNull('deleted_at')->count(),
            ];

            return view('admin.users.index', compact('users', 'roleStats'));

        } catch (\Exception $e) {
            \Log::error('User index failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            // Fallback: simple user list without complex queries
            try {
                $users = DB::table('users')
                    ->whereNull('deleted_at')
                    ->orderBy('created_at', 'desc')
                    ->paginate(10);

                $roleStats = [
                    'total' => 0,
                    'admin' => 0,
                    'employee' => 0,
                    'active' => 0,
                    'inactive' => 0,
                ];

                return view('admin.users.index', compact('users', 'roleStats'))
                    ->with('warning', 'Beberapa fitur mungkin tidak tersedia sementara.');

            } catch (\Exception $fallbackError) {
                return redirect()->route('dashboard')
                    ->with('error', 'Tidak dapat mengakses manajemen user saat ini. Silakan coba lagi nanti.');
            }
        }
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        $roles = User::getRoles();
        return view('admin.users.create', compact('roles'));
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => ['required', Rule::in(array_keys(User::getRoles()))],
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            return redirect()->route('users.index')
                ->with('success', 'User berhasil dibuat!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan saat membuat user: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        // Load user with related data
        $user->load(['transactions' => function($query) {
            $query->latest()->take(10);
        }]);

        // Get user statistics
        $stats = [
            'total_transactions' => $user->transactions()->count(),
            'total_sales' => $user->transactions()->where('status', 'completed')->sum('total_amount'),
            'last_login' => $user->last_activity,
            'account_age' => $user->created_at->diffForHumans(),
        ];

        return view('admin.users.show', compact('user', 'stats'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        $roles = User::getRoles();
        return view('admin.users.edit', compact('user', 'roles'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, User $user)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'role' => ['required', Rule::in(array_keys(User::getRoles()))],
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $updateData = [
                'name' => $request->name,
                'email' => $request->email,
                'role' => $request->role,
                'updated_at' => now(),
            ];

            // Only update password if provided
            if ($request->filled('password')) {
                $updateData['password'] = Hash::make($request->password);
            }

            $user->update($updateData);

            return redirect()->route('users.index')
                ->with('success', 'User berhasil diperbarui!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan saat memperbarui user: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified user from storage (soft delete).
     */
    public function destroy(User $user)
    {
        // Prevent timeout by setting unlimited execution time for this operation
        set_time_limit(0);
        ini_set('max_execution_time', 0);

        try {
            // Prevent admin from deleting themselves
            if ($user->id === auth()->id()) {
                return redirect()->back()
                    ->with('error', 'Anda tidak dapat menghapus akun Anda sendiri!');
            }

            // Prevent deleting the last admin
            if ($user->isAdmin() && User::where('role', User::ROLE_ADMIN)->count() <= 1) {
                return redirect()->back()
                    ->with('error', 'Tidak dapat menghapus admin terakhir!');
            }

            // Ultra-fast approach: Raw SQL update without any overhead
            $result = DB::update(
                "UPDATE users SET deleted_at = NOW(), updated_at = NOW() WHERE id = ?",
                [$user->id]
            );

            if ($result) {
                return redirect()->route('users.index')
                    ->with('success', 'User berhasil dihapus!');
            } else {
                return redirect()->back()
                    ->with('error', 'Gagal menghapus user. User mungkin sudah tidak ada.');
            }

        } catch (\Exception $e) {
            \Log::error('User deletion failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'deleted_by' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            // Check for specific error types
            if (strpos($e->getMessage(), 'Maximum execution time') !== false) {
                return redirect()->back()
                    ->with('error', 'Operasi timeout. Silakan refresh halaman dan coba lagi.');
            }

            if (strpos($e->getMessage(), 'Lock wait timeout') !== false) {
                return redirect()->back()
                    ->with('error', 'Database sedang sibuk. Tunggu 30 detik dan coba lagi.');
            }

            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Restore a soft deleted user.
     */
    public function restore($id)
    {
        try {
            $user = User::onlyTrashed()->findOrFail($id);
            $user->restore();

            return redirect()->route('users.index')
                ->with('success', 'User berhasil dipulihkan!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan saat memulihkan user: ' . $e->getMessage());
        }
    }

    /**
     * Permanently delete a user.
     */
    public function forceDelete($id)
    {
        // Prevent timeout
        set_time_limit(0);
        ini_set('max_execution_time', 0);

        try {
            // Check if user exists in trash
            $userExists = DB::selectOne(
                "SELECT id FROM users WHERE id = ? AND deleted_at IS NOT NULL",
                [$id]
            );

            if (!$userExists) {
                return redirect()->back()
                    ->with('error', 'User tidak ditemukan atau belum dihapus.');
            }

            // Prevent force deleting the current admin
            if ($id == auth()->id()) {
                return redirect()->back()
                    ->with('error', 'Anda tidak dapat menghapus permanen akun Anda sendiri!');
            }

            // Ultra-fast permanent deletion with raw SQL
            $result = DB::delete("DELETE FROM users WHERE id = ?", [$id]);

            if ($result) {
                return redirect()->route('users.index')
                    ->with('success', 'User berhasil dihapus permanen!');
            } else {
                return redirect()->back()
                    ->with('error', 'Gagal menghapus user secara permanen.');
            }

        } catch (\Exception $e) {
            \Log::error('User force deletion failed', [
                'user_id' => $id,
                'error' => $e->getMessage(),
                'deleted_by' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            if (strpos($e->getMessage(), 'Maximum execution time') !== false) {
                return redirect()->back()
                    ->with('error', 'Operasi timeout. Silakan refresh halaman dan coba lagi.');
            }

            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }
}
