<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * EMERGENCY BYPASS CONTROLLER FOR DATABASE LOCK ISSUES
     * This controller uses file-based temporary storage to bypass database locks
     */
    
    private $tempDir;
    
    public function __construct()
    {
        $this->tempDir = storage_path('app/temp_users');
        if (!File::exists($this->tempDir)) {
            File::makeDirectory($this->tempDir, 0755, true);
        }
    }
    
    /**
     * Store user data temporarily in file system
     */
    private function storeUserToFile($userData)
    {
        $filename = 'pending_user_' . time() . '_' . uniqid() . '.json';
        $filepath = $this->tempDir . '/' . $filename;
        
        File::put($filepath, json_encode($userData, JSON_PRETTY_PRINT));
        
        return $filename;
    }
    
    /**
     * Process pending users from file system to database
     */
    private function processPendingUsers()
    {
        $files = File::files($this->tempDir);
        $processed = 0;
        
        foreach ($files as $file) {
            if (str_contains($file->getFilename(), 'pending_user_')) {
                try {
                    $userData = json_decode(File::get($file->getPathname()), true);
                    
                    // Try to insert to database
                    $result = DB::statement(
                        "INSERT IGNORE INTO users (name, email, password, role, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
                        [
                            $userData['name'],
                            $userData['email'],
                            $userData['password'],
                            $userData['role'],
                            $userData['created_at'],
                            $userData['updated_at']
                        ]
                    );
                    
                    if ($result) {
                        File::delete($file->getPathname());
                        $processed++;
                    }
                    
                } catch (\Exception $e) {
                    // Keep file for later processing
                    Log::warning('Failed to process pending user: ' . $e->getMessage());
                }
            }
        }
        
        return $processed;
    }
    
    /**
     * Store a newly created user (with file backup)
     */
    public function store(Request $request)
    {
        try {
            // Validate input
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users,email',
                'password' => 'required|string|min:6|confirmed',
                'role' => ['required', Rule::in(['admin', 'employee'])],
            ]);
            
            $userData = [
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'role' => $validated['role'],
                'created_at' => now()->format('Y-m-d H:i:s'),
                'updated_at' => now()->format('Y-m-d H:i:s'),
            ];
            
            // Try database first
            try {
                DB::statement('SET SESSION innodb_lock_wait_timeout = 1');
                
                $result = DB::statement(
                    "INSERT INTO users (name, email, password, role, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
                    array_values($userData)
                );
                
                if ($result) {
                    return redirect()->route('users.index')
                        ->with('success', 'User berhasil dibuat!');
                }
                
            } catch (\Exception $e) {
                // Database failed, store to file
                $filename = $this->storeUserToFile($userData);
                
                return redirect()->route('users.index')
                    ->with('warning', 'User disimpan sementara karena masalah database. Data akan diproses otomatis. File: ' . $filename);
            }
            
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            Log::error('User creation failed: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Gagal membuat user: ' . $e->getMessage())
                ->withInput();
        }
    }
    
    /**
     * Display a listing of users (with pending file processing)
     */
    public function index(Request $request)
    {
        try {
            // Try to process pending users first
            $processed = $this->processPendingUsers();
            
            if ($processed > 0) {
                session()->flash('info', "Berhasil memproses $processed user yang tertunda.");
            }
            
            // Get search and filter parameters
            $search = $request->get('search');
            $role = $request->get('role');
            $status = $request->get('status', 'active');
            
            // Build query
            $query = DB::table('users');
            
            if ($status === 'active') {
                $query->whereNull('deleted_at');
            } elseif ($status === 'deleted') {
                $query->whereNotNull('deleted_at');
            }
            
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('email', 'LIKE', "%{$search}%");
                });
            }
            
            if ($role && in_array($role, ['admin', 'employee'])) {
                $query->where('role', $role);
            }
            
            $users = $query->select('id', 'name', 'email', 'role', 'created_at', 'updated_at', 'deleted_at')
                          ->orderBy('created_at', 'desc')
                          ->paginate(15);
            
            // Get statistics
            $roleStats = [
                'total' => DB::table('users')->count(),
                'admin' => DB::table('users')->where('role', 'admin')->whereNull('deleted_at')->count(),
                'employee' => DB::table('users')->where('role', 'employee')->whereNull('deleted_at')->count(),
                'active' => DB::table('users')->whereNull('deleted_at')->count(),
                'deleted' => DB::table('users')->whereNotNull('deleted_at')->count(),
            ];
            
            // Check for pending files
            $pendingFiles = count(File::files($this->tempDir));
            if ($pendingFiles > 0) {
                session()->flash('warning', "Ada $pendingFiles user yang menunggu diproses karena masalah database.");
            }
            
            return view('admin.users.index', compact('users', 'roleStats', 'search', 'role', 'status'));
            
        } catch (\Exception $e) {
            Log::error('User index failed: ' . $e->getMessage());
            
            $users = collect()->paginate(15);
            $roleStats = ['total' => 0, 'admin' => 0, 'employee' => 0, 'active' => 0, 'deleted' => 0];
            
            return view('admin.users.index', compact('users', 'roleStats'))
                ->with('error', 'Terjadi kesalahan saat memuat data user.');
        }
    }
    
    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        $roles = User::getRoles();
        return view('admin.users.create', compact('roles'));
    }
    
    /**
     * Display the specified user
     */
    public function show(string $id)
    {
        try {
            $user = DB::table('users')
                ->select('id', 'name', 'email', 'role', 'created_at', 'updated_at', 'deleted_at')
                ->where('id', $id)
                ->first();
            
            if (!$user) {
                return redirect()->route('users.index')
                    ->with('error', 'User tidak ditemukan.');
            }
            
            return view('admin.users.show', compact('user'));
            
        } catch (\Exception $e) {
            Log::error('User show failed: ' . $e->getMessage());
            return redirect()->route('users.index')
                ->with('error', 'Terjadi kesalahan saat memuat data user.');
        }
    }
    
    /**
     * Show the form for editing the specified user
     */
    public function edit(string $id)
    {
        try {
            $user = DB::table('users')
                ->select('id', 'name', 'email', 'role', 'created_at', 'updated_at', 'deleted_at')
                ->where('id', $id)
                ->first();
            
            if (!$user) {
                return redirect()->route('users.index')
                    ->with('error', 'User tidak ditemukan.');
            }
            
            $roles = User::getRoles();
            return view('admin.users.edit', compact('user', 'roles'));
            
        } catch (\Exception $e) {
            Log::error('User edit failed: ' . $e->getMessage());
            return redirect()->route('users.index')
                ->with('error', 'Terjadi kesalahan saat memuat data user.');
        }
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, string $id)
    {
        try {
            $user = DB::table('users')->where('id', $id)->first();
            if (!$user) {
                return redirect()->route('users.index')
                    ->with('error', 'User tidak ditemukan.');
            }

            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($id)],
                'password' => 'nullable|string|min:6|confirmed',
                'role' => ['required', Rule::in(['admin', 'employee'])],
            ]);

            $updateData = [
                'name' => $validated['name'],
                'email' => $validated['email'],
                'role' => $validated['role'],
                'updated_at' => now()->format('Y-m-d H:i:s'),
            ];

            if (!empty($validated['password'])) {
                $updateData['password'] = Hash::make($validated['password']);
            }

            try {
                DB::statement('SET SESSION innodb_lock_wait_timeout = 1');

                $sql = "UPDATE users SET " . implode(' = ?, ', array_keys($updateData)) . " = ? WHERE id = ?";
                $values = array_merge(array_values($updateData), [$id]);

                $result = DB::statement($sql, $values);

                if ($result) {
                    return redirect()->route('users.index')
                        ->with('success', 'User berhasil diperbarui!');
                } else {
                    throw new \Exception('Update failed');
                }

            } catch (\Exception $e) {
                return redirect()->back()
                    ->with('error', 'Gagal memperbarui user karena masalah database: ' . $e->getMessage())
                    ->withInput();
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            Log::error('User update failed: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Gagal memperbarui user: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Soft delete the specified user
     */
    public function destroy(string $id)
    {
        try {
            $user = DB::selectOne('SELECT * FROM users WHERE id = ?', [$id]);

            if (!$user) {
                return redirect()->back()
                    ->with('error', 'User tidak ditemukan.');
            }

            if ($user->deleted_at) {
                return redirect()->back()
                    ->with('error', 'User sudah dihapus sebelumnya.');
            }

            if ($user->id == auth()->id()) {
                return redirect()->back()
                    ->with('error', 'Anda tidak dapat menghapus akun sendiri.');
            }

            if ($user->role === 'admin') {
                $adminCount = DB::selectOne('SELECT COUNT(*) as count FROM users WHERE role = ? AND deleted_at IS NULL', ['admin']);

                if ($adminCount->count <= 1) {
                    return redirect()->back()
                        ->with('error', 'Tidak dapat menghapus admin terakhir.');
                }
            }

            try {
                DB::statement('SET SESSION innodb_lock_wait_timeout = 1');

                $sql = "UPDATE users SET deleted_at = ?, updated_at = ? WHERE id = ? AND deleted_at IS NULL";
                $now = now()->format('Y-m-d H:i:s');

                $result = DB::statement($sql, [$now, $now, $id]);

                if ($result) {
                    return redirect()->route('users.index')
                        ->with('success', 'User berhasil dihapus!');
                } else {
                    throw new \Exception('Delete failed');
                }

            } catch (\Exception $e) {
                return redirect()->back()
                    ->with('error', 'Gagal menghapus user karena masalah database: ' . $e->getMessage());
            }

        } catch (\Exception $e) {
            Log::error('User deletion failed: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Gagal menghapus user. Silakan coba lagi.');
        }
    }

    /**
     * Restore a soft deleted user
     */
    public function restore(string $id)
    {
        try {
            $user = DB::selectOne('SELECT * FROM users WHERE id = ?', [$id]);

            if (!$user) {
                return redirect()->back()
                    ->with('error', 'User tidak ditemukan.');
            }

            if (!$user->deleted_at) {
                return redirect()->back()
                    ->with('error', 'User tidak dalam status terhapus.');
            }

            try {
                DB::statement('SET SESSION innodb_lock_wait_timeout = 1');

                $sql = "UPDATE users SET deleted_at = NULL, updated_at = ? WHERE id = ?";
                $now = now()->format('Y-m-d H:i:s');

                $result = DB::statement($sql, [$now, $id]);

                if ($result) {
                    return redirect()->route('users.index')
                        ->with('success', 'User berhasil dipulihkan!');
                } else {
                    throw new \Exception('Restore failed');
                }

            } catch (\Exception $e) {
                return redirect()->back()
                    ->with('error', 'Gagal memulihkan user karena masalah database: ' . $e->getMessage());
            }

        } catch (\Exception $e) {
            Log::error('User restore failed: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Gagal memulihkan user. Silakan coba lagi.');
        }
    }

    /**
     * Permanently delete a user
     */
    public function forceDelete(string $id)
    {
        try {
            $user = DB::selectOne('SELECT * FROM users WHERE id = ?', [$id]);

            if (!$user) {
                return redirect()->back()
                    ->with('error', 'User tidak ditemukan.');
            }

            if ($user->id == auth()->id()) {
                return redirect()->back()
                    ->with('error', 'Anda tidak dapat menghapus akun sendiri secara permanen.');
            }

            if ($user->role === 'admin') {
                return redirect()->back()
                    ->with('error', 'Admin tidak dapat dihapus secara permanen.');
            }

            try {
                DB::statement('SET SESSION innodb_lock_wait_timeout = 1');

                $sql = "DELETE FROM users WHERE id = ?";

                $result = DB::statement($sql, [$id]);

                if ($result) {
                    return redirect()->route('users.index')
                        ->with('success', 'User berhasil dihapus secara permanen!');
                } else {
                    throw new \Exception('Force delete failed');
                }

            } catch (\Exception $e) {
                return redirect()->back()
                    ->with('error', 'Gagal menghapus user secara permanen karena masalah database: ' . $e->getMessage());
            }

        } catch (\Exception $e) {
            Log::error('User force delete failed: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Gagal menghapus user secara permanen. Silakan coba lagi.');
        }
    }
}
