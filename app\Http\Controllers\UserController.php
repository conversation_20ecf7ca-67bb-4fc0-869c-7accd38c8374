<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Constructor - Apply timeout prevention globally
     */
    public function __construct()
    {
        // AGGRESSIVE TIMEOUT PREVENTION
        set_time_limit(0);
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '512M');
        
        // OPTIMIZE DATABASE SETTINGS
        try {
            DB::statement('SET SESSION wait_timeout = 300');
            DB::statement('SET SESSION interactive_timeout = 300');
            DB::statement('SET SESSION innodb_lock_wait_timeout = 10');
            DB::statement('SET SESSION lock_wait_timeout = 10');
        } catch (\Exception $e) {
            Log::warning('Could not optimize database settings: ' . $e->getMessage());
        }
    }

    /**
     * Display a listing of users with optimized queries
     */
    public function index(Request $request)
    {
        try {
            // Clear any session errors
            session()->forget(['error', 'errors', 'message']);
            
            // Get search and filter parameters
            $search = $request->get('search');
            $role = $request->get('role');
            $status = $request->get('status', 'active'); // active, deleted, all
            
            // Build optimized query using Query Builder for speed
            $query = DB::table('users');
            
            // Apply status filter
            if ($status === 'active') {
                $query->whereNull('deleted_at');
            } elseif ($status === 'deleted') {
                $query->whereNotNull('deleted_at');
            }
            // 'all' shows both active and deleted
            
            // Apply search filter
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('email', 'LIKE', "%{$search}%");
                });
            }
            
            // Apply role filter
            if ($role && in_array($role, ['admin', 'employee'])) {
                $query->where('role', $role);
            }
            
            // Get paginated results with limit for performance
            $users = $query->select('id', 'name', 'email', 'role', 'created_at', 'updated_at', 'deleted_at')
                          ->orderBy('created_at', 'desc')
                          ->paginate(15);
            
            // Get role statistics with optimized queries
            $roleStats = [
                'total' => DB::table('users')->count(),
                'admin' => DB::table('users')->where('role', 'admin')->whereNull('deleted_at')->count(),
                'employee' => DB::table('users')->where('role', 'employee')->whereNull('deleted_at')->count(),
                'active' => DB::table('users')->whereNull('deleted_at')->count(),
                'deleted' => DB::table('users')->whereNotNull('deleted_at')->count(),
            ];

            // Debug information (optional)
            if (config('app.debug')) {
                Log::info('User index debug', [
                    'status_filter' => $status,
                    'search_filter' => $search,
                    'role_filter' => $role,
                    'users_count' => $users->total(),
                    'stats' => $roleStats
                ]);
            }
            
            return view('admin.users.index', compact('users', 'roleStats', 'search', 'role', 'status'));
            
        } catch (\Exception $e) {
            Log::error('User index failed: ' . $e->getMessage());
            
            // Fallback with minimal data
            $users = collect()->paginate(15);
            $roleStats = ['total' => 0, 'admin' => 0, 'employee' => 0, 'active' => 0, 'deleted' => 0];
            
            return view('admin.users.index', compact('users', 'roleStats'))
                ->with('error', 'Terjadi kesalahan saat memuat data user. Silakan refresh halaman.');
        }
    }

    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        $roles = User::getRoles();
        return view('admin.users.create', compact('roles'));
    }

    /**
     * Store a newly created user with optimized validation
     */
    public function store(Request $request)
    {
        try {
            // Validate input
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users,email',
                'password' => 'required|string|min:6|confirmed',
                'role' => ['required', Rule::in(['admin', 'employee'])],
            ]);
            
            // Create user with direct DB insert for speed
            $userId = DB::table('users')->insertGetId([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'role' => $validated['role'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            if ($userId) {
                return redirect()->route('users.index')
                    ->with('success', 'User berhasil dibuat!');
            } else {
                throw new \Exception('Failed to create user');
            }
            
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            Log::error('User creation failed: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Gagal membuat user: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified user
     */
    public function show(string $id)
    {
        try {
            $user = DB::table('users')
                ->select('id', 'name', 'email', 'role', 'created_at', 'updated_at', 'deleted_at')
                ->where('id', $id)
                ->first();
            
            if (!$user) {
                return redirect()->route('users.index')
                    ->with('error', 'User tidak ditemukan.');
            }
            
            return view('admin.users.show', compact('user'));
            
        } catch (\Exception $e) {
            Log::error('User show failed: ' . $e->getMessage());
            return redirect()->route('users.index')
                ->with('error', 'Terjadi kesalahan saat memuat data user.');
        }
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(string $id)
    {
        try {
            $user = DB::table('users')
                ->select('id', 'name', 'email', 'role', 'created_at', 'updated_at', 'deleted_at')
                ->where('id', $id)
                ->first();
            
            if (!$user) {
                return redirect()->route('users.index')
                    ->with('error', 'User tidak ditemukan.');
            }
            
            $roles = User::getRoles();
            return view('admin.users.edit', compact('user', 'roles'));
            
        } catch (\Exception $e) {
            Log::error('User edit failed: ' . $e->getMessage());
            return redirect()->route('users.index')
                ->with('error', 'Terjadi kesalahan saat memuat data user.');
        }
    }

    /**
     * Update the specified user with optimized queries
     */
    public function update(Request $request, string $id)
    {
        try {
            // Check if user exists
            $user = DB::table('users')->where('id', $id)->first();
            if (!$user) {
                return redirect()->route('users.index')
                    ->with('error', 'User tidak ditemukan.');
            }
            
            // Validate input
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($id)],
                'password' => 'nullable|string|min:6|confirmed',
                'role' => ['required', Rule::in(['admin', 'employee'])],
            ]);
            
            // Prepare update data
            $updateData = [
                'name' => $validated['name'],
                'email' => $validated['email'],
                'role' => $validated['role'],
                'updated_at' => now(),
            ];
            
            // Add password if provided
            if (!empty($validated['password'])) {
                $updateData['password'] = Hash::make($validated['password']);
            }
            
            // Update with direct DB query for speed
            $updated = DB::table('users')
                ->where('id', $id)
                ->update($updateData);
            
            if ($updated) {
                return redirect()->route('users.index')
                    ->with('success', 'User berhasil diperbarui!');
            } else {
                throw new \Exception('No changes made or update failed');
            }
            
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            Log::error('User update failed: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Gagal memperbarui user: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Soft delete the specified user with multiple strategies
     */
    public function destroy(string $id)
    {
        try {
            // Get user data first
            $user = DB::table('users')
                ->select('id', 'name', 'email', 'role', 'deleted_at')
                ->where('id', $id)
                ->first();
            
            if (!$user) {
                return redirect()->back()
                    ->with('error', 'User tidak ditemukan.');
            }
            
            if ($user->deleted_at) {
                return redirect()->back()
                    ->with('error', 'User sudah dihapus sebelumnya.');
            }
            
            // Prevent self-deletion
            if ($user->id == auth()->id()) {
                return redirect()->back()
                    ->with('error', 'Anda tidak dapat menghapus akun sendiri.');
            }
            
            // Prevent deleting last admin
            if ($user->role === 'admin') {
                $adminCount = DB::table('users')
                    ->where('role', 'admin')
                    ->whereNull('deleted_at')
                    ->count();
                
                if ($adminCount <= 1) {
                    return redirect()->back()
                        ->with('error', 'Tidak dapat menghapus admin terakhir.');
                }
            }
            
            // Perform soft delete with optimized query
            $deleted = DB::table('users')
                ->where('id', $id)
                ->whereNull('deleted_at')
                ->update([
                    'deleted_at' => now(),
                    'updated_at' => now()
                ]);
            
            if ($deleted) {
                return redirect()->route('users.index')
                    ->with('success', 'User berhasil dihapus!');
            } else {
                throw new \Exception('Delete operation failed');
            }
            
        } catch (\Exception $e) {
            Log::error('User deletion failed: ' . $e->getMessage(), [
                'user_id' => $id,
                'deleted_by' => auth()->id()
            ]);
            
            return redirect()->back()
                ->with('error', 'Gagal menghapus user. Silakan coba lagi.');
        }
    }

    /**
     * Restore a soft deleted user
     */
    public function restore(string $id)
    {
        try {
            // Check if user exists and is deleted
            $user = DB::table('users')
                ->select('id', 'name', 'email', 'deleted_at')
                ->where('id', $id)
                ->first();

            if (!$user) {
                return redirect()->back()
                    ->with('error', 'User tidak ditemukan.');
            }

            if (!$user->deleted_at) {
                return redirect()->back()
                    ->with('error', 'User tidak dalam status terhapus.');
            }

            // Restore user
            $restored = DB::table('users')
                ->where('id', $id)
                ->update([
                    'deleted_at' => null,
                    'updated_at' => now()
                ]);

            if ($restored) {
                return redirect()->route('users.index')
                    ->with('success', 'User berhasil dipulihkan!');
            } else {
                throw new \Exception('Restore operation failed');
            }

        } catch (\Exception $e) {
            Log::error('User restore failed: ' . $e->getMessage(), [
                'user_id' => $id,
                'restored_by' => auth()->id()
            ]);

            return redirect()->back()
                ->with('error', 'Gagal memulihkan user. Silakan coba lagi.');
        }
    }

    /**
     * Permanently delete a user (force delete)
     */
    public function forceDelete(string $id)
    {
        try {
            // Get user data first
            $user = DB::table('users')
                ->select('id', 'name', 'email', 'role')
                ->where('id', $id)
                ->first();

            if (!$user) {
                return redirect()->back()
                    ->with('error', 'User tidak ditemukan.');
            }

            // Prevent self-deletion
            if ($user->id == auth()->id()) {
                return redirect()->back()
                    ->with('error', 'Anda tidak dapat menghapus akun sendiri secara permanen.');
            }

            // Prevent deleting admin permanently
            if ($user->role === 'admin') {
                return redirect()->back()
                    ->with('error', 'Admin tidak dapat dihapus secara permanen.');
            }

            // Perform permanent deletion
            $deleted = DB::table('users')
                ->where('id', $id)
                ->delete();

            if ($deleted) {
                return redirect()->route('users.index')
                    ->with('success', 'User berhasil dihapus secara permanen!');
            } else {
                throw new \Exception('Force delete operation failed');
            }

        } catch (\Exception $e) {
            Log::error('User force delete failed: ' . $e->getMessage(), [
                'user_id' => $id,
                'deleted_by' => auth()->id()
            ]);

            return redirect()->back()
                ->with('error', 'Gagal menghapus user secara permanen. Silakan coba lagi.');
        }
    }
}
