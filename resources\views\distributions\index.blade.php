@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-truck me-2"></i>Daftar Distribusi
                        </h4>
                        <div>
                            <a href="{{ route('distributions.export') }}" class="btn btn-outline-light btn-sm me-2">
                                <i class="fas fa-download me-1"></i>Export CSV
                            </a>
                            <a href="{{ route('distributions.create') }}" class="btn btn-light btn-sm">
                                <i class="fas fa-plus me-1"></i>Distribusi Baru
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- Filter & Search -->
                    <form method="GET" action="{{ route('distributions.index') }}" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" name="search" placeholder="Cari distribusi..." value="{{ request('search') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" name="start_date" placeholder="Dari tanggal" value="{{ request('start_date') }}">
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" name="end_date" placeholder="Sampai tanggal" value="{{ request('end_date') }}">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                            </div>
                            <div class="col-md-1">
                                <a href="{{ route('distributions.index') }}" class="btn btn-secondary w-100">Reset</a>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-12 text-end">
                                <span class="badge bg-primary fs-6 px-3 py-2">
                                    Total: {{ $distributions->total() }} Distribusi
                                </span>
                            </div>
                        </div>
                    </form>

                    <!-- Distributions Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th width="10%">#</th>
                                    <th width="25%">No. Distribusi</th>
                                    <th width="25%">Tanggal</th>
                                    <th width="25%">Tujuan</th>
                                    <th width="15%">Total Item</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($distributions as $index => $distribution)
                                <tr>
                                    <td>{{ $distributions->firstItem() + $index }}</td>
                                    <td>
                                        <strong>{{ $distribution->distribution_number ?? 'DIST-' . $distribution->id }}</strong>
                                    </td>
                                    <td>
                                        {{ $distribution->distribution_date->format('d/m/Y') }}
                                        <br>
                                        <small class="text-muted">{{ $distribution->distribution_date->format('H:i') }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ $distribution->market_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $distribution->user->name ?? 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-success fs-6">{{ $distribution->items->count() }} item</span>
                                        <br>
                                        <small class="text-muted">{{ $distribution->items->sum('quantity') }} qty</small>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center py-5">
                                        <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Belum ada distribusi yang tercatat</p>
                                        <a href="{{ route('distributions.create') }}" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i>Buat Distribusi Pertama
                                        </a>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($distributions->hasPages())
                    <div class="d-flex justify-content-center mt-4">
                        {{ $distributions->links('custom.pagination') }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>


@endsection
