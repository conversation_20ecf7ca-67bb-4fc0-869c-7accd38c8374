

<?php
use Illuminate\Support\Facades\Auth;
?>

<?php $__env->startPush('styles'); ?>
<style>
    .pagination-sm .page-link {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
    }

    .pagination-sm .page-item:first-child .page-link,
    .pagination-sm .page-item:last-child .page-link {
        border-radius: 0.2rem;
    }

    .pagination-sm .page-link i {
        font-size: 0.75rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-cash-register"></i>
        <span>Daftar Transaksi</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Riwayat Transaksi</span>
                    <div>
                        <a href="<?php echo e(route('pos')); ?>" class="btn btn-primary">
                            <i class="fas fa-shopping-cart"></i> Buat Transaksi Baru
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <form action="<?php echo e(route('transactions.index')); ?>" method="GET" class="row">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">Dari</span>
                                    <input type="date" class="form-control" name="start_date" value="<?php echo e(request('start_date')); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">Sampai</span>
                                    <input type="date" class="form-control" name="end_date" value="<?php echo e(request('end_date')); ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <a href="<?php echo e(route('transactions.index')); ?>" class="btn btn-secondary w-100">Reset</a>
                            </div>
                            <div class="col-md-2">
                                <a href="<?php echo e(route('transactions.export')); ?>?<?php echo e(http_build_query(request()->query())); ?>" class="btn btn-success w-100">
                                    <i class="fas fa-file-excel"></i> Export
                                </a>
                            </div>
                        </form>
                    </div>

                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>No. Invoice</th>
                                    <th>Tanggal</th>
                                    <th>Jumlah Item</th>
                                    <th>Total</th>
                                    <th>Kasir</th>
                                    <th>Metode Pembayaran</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($transaction->invoice_number); ?></td>
                                    <td><?php echo e($transaction->created_at->format('d M Y H:i')); ?></td>
                                    <td><?php echo e($transaction->items_count); ?> item</td>
                                    <td>Rp <?php echo e(number_format($transaction->total_amount, 0, ',', '.')); ?></td>
                                    <td><?php echo e($transaction->user->name ?? 'Admin'); ?></td>
                                    <td>
                                        <?php if($transaction->payment_method === 'gateway'): ?>
                                            <span class="badge bg-primary">Gateway</span>
                                            <small class="text-muted d-block">Midtrans</small>
                                        <?php else: ?>
                                            <span class="badge bg-success">Manual</span>
                                            <small class="text-muted d-block"><?php echo e(ucfirst($transaction->payment_method)); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                            $paymentStatus = $transaction->payment_status ?? 'completed';
                                            $transactionStatus = $transaction->status ?? 'completed';

                                            if ($transaction->payment_method === 'gateway') {
                                                switch ($paymentStatus) {
                                                    case 'paid':
                                                        echo '<span class="badge bg-success"><i class="fas fa-check me-1"></i>Selesai</span>';
                                                        break;
                                                    case 'pending':
                                                        echo '<span class="badge bg-warning"><i class="fas fa-clock me-1"></i>Menunggu</span>';
                                                        break;
                                                    case 'failed':
                                                        echo '<span class="badge bg-danger"><i class="fas fa-times me-1"></i>Gagal</span>';
                                                        break;
                                                    case 'cancelled':
                                                        echo '<span class="badge bg-secondary"><i class="fas fa-ban me-1"></i>Batal</span>';
                                                        break;
                                                    case 'expired':
                                                        echo '<span class="badge bg-dark"><i class="fas fa-hourglass-end me-1"></i>Kedaluwarsa</span>';
                                                        break;
                                                    default:
                                                        echo '<span class="badge bg-info">'.ucfirst($paymentStatus).'</span>';
                                                }
                                            } else {
                                                switch ($transactionStatus) {
                                                    case 'completed':
                                                        echo '<span class="badge bg-success"><i class="fas fa-check me-1"></i>Selesai</span>';
                                                        break;
                                                    case 'pending':
                                                        echo '<span class="badge bg-warning"><i class="fas fa-clock me-1"></i>Pending</span>';
                                                        break;
                                                    case 'cancelled':
                                                        echo '<span class="badge bg-secondary"><i class="fas fa-ban me-1"></i>Batal</span>';
                                                        break;
                                                    default:
                                                        echo '<span class="badge bg-info">'.ucfirst($transactionStatus).'</span>';
                                                }
                                            }
                                        ?>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="<?php echo e(route('transactions.show', $transaction)); ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('transactions.print', $transaction)); ?>" class="btn btn-sm btn-secondary" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <?php if(Auth::user()->isAdmin()): ?>
                                            <form action="<?php echo e(route('transactions.destroy', $transaction)); ?>" method="POST" class="d-inline" onsubmit="return confirm('Yakin ingin menghapus transaksi ini?');">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="8" class="text-center">Tidak ada data transaksi</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        <?php echo e($transactions->appends(request()->query())->links('custom.pagination')); ?>

                    </div>

                    <div class="card mt-4">
                        <div class="card-body">
                            <h5 class="card-title">Ringkasan</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="border rounded p-3 mb-3">
                                        <h6>Total Transaksi</h6>
                                        <h3><?php echo e($transactionSummary['count']); ?></h3>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="border rounded p-3 mb-3">
                                        <h6>Total Pendapatan</h6>
                                        <h3>Rp <?php echo e(number_format($transactionSummary['total'], 0, ',', '.')); ?></h3>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="border rounded p-3 mb-3">
                                        <h6>Rata-rata per Transaksi</h6>
                                        <h3>Rp <?php echo e(number_format($transactionSummary['average'], 0, ',', '.')); ?></h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/transactions/index.blade.php ENDPATH**/ ?>