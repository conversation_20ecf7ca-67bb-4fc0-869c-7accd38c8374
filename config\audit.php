<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Audit Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the audit logging system.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Audit Logging Enabled
    |--------------------------------------------------------------------------
    |
    | This option controls whether audit logging is enabled or disabled.
    | When disabled, no audit logs will be created.
    |
    */
    'enabled' => env('AUDIT_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Audit Logging Disabled
    |--------------------------------------------------------------------------
    |
    | This option can be used to temporarily disable audit logging for
    | performance critical operations.
    |
    */
    'disabled' => env('AUDIT_DISABLED', false),

    /*
    |--------------------------------------------------------------------------
    | Audit Log Retention
    |--------------------------------------------------------------------------
    |
    | This option controls how long audit logs are kept in the database.
    | Logs older than this number of days will be automatically deleted.
    |
    */
    'retention_days' => env('AUDIT_RETENTION_DAYS', 365),

    /*
    |--------------------------------------------------------------------------
    | Excluded Actions
    |--------------------------------------------------------------------------
    |
    | These actions will not be logged to the audit log.
    |
    */
    'excluded_actions' => [
        // 'update', // Uncomment to disable update logging
        // 'create', // Uncomment to disable create logging
        // 'delete', // Uncomment to disable delete logging
    ],

    /*
    |--------------------------------------------------------------------------
    | Excluded Models
    |--------------------------------------------------------------------------
    |
    | These models will not be logged to the audit log.
    |
    */
    'excluded_models' => [
        // App\Models\AuditLog::class, // Don't audit the audit log itself
    ],

    /*
    |--------------------------------------------------------------------------
    | Excluded Attributes
    |--------------------------------------------------------------------------
    |
    | These attributes will not be logged in the audit log.
    |
    */
    'excluded_attributes' => [
        'created_at',
        'updated_at',
        'deleted_at',
        'password',
        'remember_token',
        'email_verified_at',
    ],

    /*
    |--------------------------------------------------------------------------
    | User Model
    |--------------------------------------------------------------------------
    |
    | This is the User model used by the audit system.
    |
    */
    'user_model' => App\Models\User::class,

    /*
    |--------------------------------------------------------------------------
    | User Primary Key
    |--------------------------------------------------------------------------
    |
    | This is the primary key of the User model.
    |
    */
    'user_primary_key' => 'id',

    /*
    |--------------------------------------------------------------------------
    | Audit Log Model
    |--------------------------------------------------------------------------
    |
    | This is the AuditLog model used by the audit system.
    |
    */
    'audit_model' => App\Models\AuditLog::class,

    /*
    |--------------------------------------------------------------------------
    | Queue Audit Logs
    |--------------------------------------------------------------------------
    |
    | When enabled, audit logs will be processed in the background using
    | Laravel's queue system for better performance.
    |
    */
    'queue' => env('AUDIT_QUEUE', false),

    /*
    |--------------------------------------------------------------------------
    | Queue Connection
    |--------------------------------------------------------------------------
    |
    | The queue connection to use for processing audit logs.
    |
    */
    'queue_connection' => env('AUDIT_QUEUE_CONNECTION', 'default'),
];
