<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update user role enum to only include admin and employee
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'employee') DEFAULT 'employee'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to include all roles
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'employee', 'cashier', 'warehouse') DEFAULT 'employee'");
    }
};
