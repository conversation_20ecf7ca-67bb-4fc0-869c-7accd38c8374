<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ClearDatabaseLocks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:clear-locks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear database locks and show current processes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('🔍 COMPREHENSIVE DATABASE LOCK ANALYSIS');
            $this->newLine();

            // 1. Check current processes
            $this->checkCurrentProcesses();

            // 2. Check InnoDB status
            $this->checkInnoDBStatus();

            // 3. Check lock waits
            $this->checkLockWaits();

            // 4. Optimize database settings
            $this->optimizeDatabaseSettings();

            // 5. Clear query cache if needed
            $this->clearQueryCache();

            $this->newLine();
            $this->info('✅ Database lock analysis and optimization completed.');

        } catch (\Exception $e) {
            $this->error('❌ Error during database lock analysis: ' . $e->getMessage());
        }
    }

    private function checkCurrentProcesses()
    {
        $this->info('📊 Checking current database processes...');

        try {
            $processes = DB::select('SHOW PROCESSLIST');

            if (empty($processes)) {
                $this->info('✅ No active processes found.');
                return;
            }

            $this->table(
                ['Id', 'User', 'Host', 'DB', 'Command', 'Time', 'State', 'Info'],
                collect($processes)->map(function ($process) {
                    return [
                        $process->Id,
                        $process->User,
                        $process->Host,
                        $process->db ?? 'NULL',
                        $process->Command,
                        $process->Time . 's',
                        $process->State ?? 'NULL',
                        substr($process->Info ?? '', 0, 50) . (strlen($process->Info ?? '') > 50 ? '...' : '')
                    ];
                })->toArray()
            );

            // Check for problematic processes
            $longRunning = collect($processes)->filter(function ($process) {
                return $process->Time > 10 && $process->Command !== 'Sleep';
            });

            $lockingProcesses = collect($processes)->filter(function ($process) {
                return stripos($process->State ?? '', 'lock') !== false ||
                       stripos($process->Info ?? '', 'LOCK') !== false;
            });

            if ($longRunning->isNotEmpty()) {
                $this->warn("⚠️  Found {$longRunning->count()} long-running processes (>10 seconds)");

                if ($this->confirm('🔧 Kill long-running processes?')) {
                    foreach ($longRunning as $process) {
                        try {
                            DB::statement("KILL {$process->Id}");
                            $this->info("✅ Killed process {$process->Id}");
                        } catch (\Exception $e) {
                            $this->error("❌ Failed to kill process {$process->Id}: " . $e->getMessage());
                        }
                    }
                }
            }

            if ($lockingProcesses->isNotEmpty()) {
                $this->warn("🔒 Found {$lockingProcesses->count()} processes with lock-related states");
            }

        } catch (\Exception $e) {
            $this->error('❌ Error checking processes: ' . $e->getMessage());
        }
    }

    private function checkInnoDBStatus()
    {
        $this->info('🔍 Checking InnoDB status...');

        try {
            $status = DB::select('SHOW ENGINE INNODB STATUS');

            if (!empty($status)) {
                $statusText = $status[0]->Status;

                // Check for deadlocks
                if (strpos($statusText, 'LATEST DETECTED DEADLOCK') !== false) {
                    $this->error('💀 DEADLOCKS DETECTED in InnoDB status!');

                    // Extract deadlock information
                    $deadlockStart = strpos($statusText, 'LATEST DETECTED DEADLOCK');
                    $deadlockEnd = strpos($statusText, '------------', $deadlockStart + 100);
                    if ($deadlockStart && $deadlockEnd) {
                        $deadlockInfo = substr($statusText, $deadlockStart, $deadlockEnd - $deadlockStart);
                        $this->warn('Deadlock details: ' . substr($deadlockInfo, 0, 200) . '...');
                    }
                } else {
                    $this->info('✅ No recent deadlocks detected');
                }

                // Check for lock waits
                if (strpos($statusText, 'LOCK WAIT') !== false) {
                    $this->warn('⏳ LOCK WAITS detected in InnoDB status!');
                } else {
                    $this->info('✅ No lock waits detected');
                }

                // Check transaction list
                if (strpos($statusText, 'LIST OF TRANSACTIONS') !== false) {
                    $transactionCount = substr_count($statusText, 'TRANSACTION');
                    $this->info("📋 Active transactions: {$transactionCount}");
                }
            }

        } catch (\Exception $e) {
            $this->error('❌ Error checking InnoDB status: ' . $e->getMessage());
        }
    }

    private function checkLockWaits()
    {
        $this->info('🔒 Checking for lock waits...');

        try {
            // Check information_schema for lock waits
            $lockWaits = DB::select("
                SELECT
                    r.trx_id waiting_trx_id,
                    r.trx_mysql_thread_id waiting_thread,
                    r.trx_query waiting_query,
                    b.trx_id blocking_trx_id,
                    b.trx_mysql_thread_id blocking_thread,
                    b.trx_query blocking_query
                FROM information_schema.innodb_lock_waits w
                INNER JOIN information_schema.innodb_trx b ON b.trx_id = w.blocking_trx_id
                INNER JOIN information_schema.innodb_trx r ON r.trx_id = w.requesting_trx_id
            ");

            if (empty($lockWaits)) {
                $this->info('✅ No lock waits detected');
            } else {
                $this->warn("⚠️  Found {count($lockWaits)} lock waits");
                foreach ($lockWaits as $wait) {
                    $this->warn("Waiting thread {$wait->waiting_thread} blocked by thread {$wait->blocking_thread}");
                }
            }

        } catch (\Exception $e) {
            // This might fail on older MySQL versions
            $this->warn('⚠️  Could not check lock waits (MySQL version might not support this): ' . $e->getMessage());
        }
    }

    private function optimizeDatabaseSettings()
    {
        $this->info('⚙️  Optimizing database settings...');

        try {
            // Apply optimized settings for current session
            $optimizations = [
                "SET SESSION innodb_lock_wait_timeout = 5",
                "SET SESSION lock_wait_timeout = 5",
                "SET SESSION autocommit = 1",
                "SET SESSION transaction_isolation = 'READ-COMMITTED'",
                "SET SESSION innodb_deadlock_detect = ON",
                "SET SESSION max_execution_time = 30000"
            ];

            foreach ($optimizations as $sql) {
                try {
                    DB::statement($sql);
                    $this->info("✅ Applied: " . substr($sql, 12)); // Remove "SET SESSION "
                } catch (\Exception $e) {
                    $this->warn("⚠️  Could not apply: {$sql} - " . $e->getMessage());
                }
            }

        } catch (\Exception $e) {
            $this->error('❌ Error optimizing database settings: ' . $e->getMessage());
        }
    }

    private function clearQueryCache()
    {
        $this->info('🧹 Clearing query cache...');

        try {
            // Clear query cache if enabled
            DB::statement('RESET QUERY CACHE');
            $this->info('✅ Query cache cleared');

            // Flush tables to release locks
            DB::statement('FLUSH TABLES');
            $this->info('✅ Tables flushed');

        } catch (\Exception $e) {
            $this->warn('⚠️  Could not clear query cache: ' . $e->getMessage());
        }
    }
}
