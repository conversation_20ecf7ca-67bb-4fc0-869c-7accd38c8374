<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ClearDatabaseLocks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:clear-locks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear database locks and show current processes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Checking current database processes...');
            
            // Show current processes
            $processes = DB::select('SHOW PROCESSLIST');
            
            if (empty($processes)) {
                $this->info('No active processes found.');
                return;
            }
            
            $this->table(
                ['Id', 'User', 'Host', 'DB', 'Command', 'Time', 'State', 'Info'],
                collect($processes)->map(function ($process) {
                    return [
                        $process->Id,
                        $process->User,
                        $process->Host,
                        $process->db,
                        $process->Command,
                        $process->Time,
                        $process->State,
                        substr($process->Info ?? '', 0, 50) . (strlen($process->Info ?? '') > 50 ? '...' : '')
                    ];
                })->toArray()
            );
            
            // Check for long-running transactions
            $longRunning = collect($processes)->filter(function ($process) {
                return $process->Time > 30 && $process->Command !== 'Sleep';
            });
            
            if ($longRunning->isNotEmpty()) {
                $this->warn('Found ' . $longRunning->count() . ' long-running processes (>30 seconds)');
                
                if ($this->confirm('Do you want to kill long-running processes?')) {
                    foreach ($longRunning as $process) {
                        try {
                            DB::statement("KILL {$process->Id}");
                            $this->info("Killed process {$process->Id}");
                        } catch (\Exception $e) {
                            $this->error("Failed to kill process {$process->Id}: " . $e->getMessage());
                        }
                    }
                }
            }
            
            // Show innodb status
            $this->info('Checking InnoDB status...');
            $status = DB::select('SHOW ENGINE INNODB STATUS');
            
            if (!empty($status)) {
                $statusText = $status[0]->Status;
                
                // Look for deadlocks
                if (strpos($statusText, 'LATEST DETECTED DEADLOCK') !== false) {
                    $this->warn('Deadlocks detected in InnoDB status!');
                }
                
                // Look for lock waits
                if (strpos($statusText, 'LOCK WAIT') !== false) {
                    $this->warn('Lock waits detected in InnoDB status!');
                }
            }
            
            $this->info('Database lock check completed.');
            
        } catch (\Exception $e) {
            $this->error('Error checking database locks: ' . $e->getMessage());
        }
    }
}
