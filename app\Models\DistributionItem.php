<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\Auditable;

class DistributionItem extends Model
{
    use HasFactory, SoftDeletes, Auditable;

    protected $fillable = [
        'distribution_id',
        'processed_inventory_id',
        'quantity',
        'price_per_item',
        'total_price'
    ];

    /**
     * Get the distribution this item belongs to.
     */
    public function distribution(): BelongsTo
    {
        return $this->belongsTo(Distribution::class);
    }

    /**
     * Get the processed inventory associated with this item.
     */
    public function processedInventory(): BelongsTo
    {
        return $this->belongsTo(ProcessedInventory::class);
    }

    /**
     * Get the product name from processed inventory.
     */
    public function getProductNameAttribute()
    {
        return $this->processedInventory->name ?? 'Unknown Product';
    }
}
