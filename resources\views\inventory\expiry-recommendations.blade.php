@extends('layouts.app')

@section('title', 'Rekomendasi Ubi <PERSON>')

@push('styles')
<style>
    .priority-high {
        background-color: rgba(255, 0, 0, 0.1);
        border-left: 4px solid #dc3545;
    }
    
    .priority-medium {
        background-color: rgba(255, 193, 7, 0.1);
        border-left: 4px solid #ffc107;
    }
    
    .priority-low {
        background-color: rgba(40, 167, 69, 0.1);
        border-left: 4px solid #28a745;
    }
    
    .stat-card {
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }
    
    .stat-card.high-priority {
        background: linear-gradient(135deg, #fff5f5 0%, #ffe0e0 100%);
        border-left: 5px solid #dc3545;
    }
    
    .stat-card.medium-priority {
        background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
        border-left: 5px solid #ffc107;
    }
    
    .stat-card.low-priority {
        background: linear-gradient(135deg, #f0fff4 0%, #d4edda 100%);
        border-left: 5px solid #28a745;
    }
    
    .stat-card.total-loss {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-left: 5px solid #6c757d;
    }
    
    .btn-set-market {
        white-space: nowrap;
    }
    
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .badge-priority {
        padding: 5px 10px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 12px;
    }
    
    .badge-high {
        background-color: #dc3545;
        color: white;
    }
    
    .badge-medium {
        background-color: #ffc107;
        color: #212529;
    }
    
    .badge-low {
        background-color: #28a745;
        color: white;
    }
    
    .days-indicator {
        font-weight: bold;
    }
    
    .days-indicator.danger {
        color: #dc3545;
    }
    
    .days-indicator.warning {
        color: #ffc107;
    }
    
    .days-indicator.success {
        color: #28a745;
    }
</style>
@endpush

@section('content')
<div class="container-fluid px-4">
    <h1 class="mt-4">Rekomendasi Ubi Matang untuk Segera Dijual</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item active">Rekomendasi Ubi Matang</li>
    </ol>
    
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-info-circle me-1"></i>
                    Tentang Fitur Ini
                </div>
                <div class="card-body">
                    <p>Fitur ini membantu Anda mengidentifikasi stok ubi matang yang perlu segera dijual berdasarkan tanggal kadaluarsa. Ubi akan dikategorikan berdasarkan prioritas:</p>
                    <ul>
                        <li><strong>Prioritas Tinggi:</strong> Ubi yang akan kadaluarsa dalam 3 hari atau kurang</li>
                        <li><strong>Prioritas Sedang:</strong> Ubi yang akan kadaluarsa dalam 4-7 hari</li>
                        <li><strong>Prioritas Rendah:</strong> Ubi yang akan kadaluarsa dalam lebih dari 7 hari</li>
                    </ul>
                    <p>Sistem juga memberikan rekomendasi pasar untuk distribusi berdasarkan data historis transaksi.</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stat-card high-priority">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-0">Prioritas Tinggi</h6>
                        <h2 class="my-2">{{ $highPriorityCount }}</h2>
                        <p class="mb-0 text-danger"><i class="fas fa-exclamation-triangle me-1"></i> Segera dijual!</p>
                    </div>
                    <div class="icon-bg rounded-circle d-flex align-items-center justify-content-center">
                        <i class="fas fa-fire-alt fa-2x text-danger"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stat-card medium-priority">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-0">Prioritas Sedang</h6>
                        <h2 class="my-2">{{ $mediumPriorityCount }}</h2>
                        <p class="mb-0 text-warning"><i class="fas fa-clock me-1"></i> Pantau</p>
                    </div>
                    <div class="icon-bg rounded-circle d-flex align-items-center justify-content-center">
                        <i class="fas fa-hourglass-half fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stat-card low-priority">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-0">Prioritas Rendah</h6>
                        <h2 class="my-2">{{ $lowPriorityCount }}</h2>
                        <p class="mb-0 text-success"><i class="fas fa-check-circle me-1"></i> Aman</p>
                    </div>
                    <div class="icon-bg rounded-circle d-flex align-items-center justify-content-center">
                        <i class="fas fa-leaf fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stat-card total-loss">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-0">Potensi Kerugian</h6>
                        <h2 class="my-2">Rp {{ number_format($totalPotentialLoss, 0, ',', '.') }}</h2>
                        <p class="mb-0 text-secondary"><i class="fas fa-chart-line me-1"></i> Prioritas Tinggi</p>
                    </div>
                    <div class="icon-bg rounded-circle d-flex align-items-center justify-content-center">
                        <i class="fas fa-money-bill-wave fa-2x text-secondary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-table me-1"></i>
                Daftar Ubi Matang yang Perlu Segera Dijual
            </div>
            <div class="d-flex">
                <a href="{{ route('distributions.index') }}" class="btn btn-sm btn-success me-2">
                    <i class="fas fa-list me-1"></i> Kelola Distribusi
                </a>
                <a href="{{ route('expiry-recommendations.update-all') }}" class="btn btn-sm btn-primary me-2">
                    <i class="fas fa-sync-alt me-1"></i> Perbarui Semua
                </a>
                <div class="dropdown">
                    <button class="btn btn-sm btn-success dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-file-export me-1"></i> Export
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                        <li><a class="dropdown-item" href="{{ route('expiry-recommendations.export-excel') }}"><i class="fas fa-file-excel me-1"></i> Excel</a></li>
                        <li><a class="dropdown-item" href="{{ route('expiry-recommendations.export-pdf') }}"><i class="fas fa-file-pdf me-1"></i> PDF</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Nama Produk</th>
                            <th>Batch</th>
                            <th>Tgl Produksi</th>
                            <th>Tgl Kadaluarsa</th>
                            <th>Sisa Hari</th>
                            <th>Stok</th>
                            <th>Nilai Total</th>
                            <th>Prioritas</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($recommendedItems as $item)
                            @php
                                $priorityClass = '';
                                $badgeClass = '';
                                $daysClass = '';
                                
                                if ($item->priority_level === 'Tinggi') {
                                    $priorityClass = 'priority-high';
                                    $badgeClass = 'badge-high';
                                    $daysClass = 'danger';
                                } elseif ($item->priority_level === 'Sedang') {
                                    $priorityClass = 'priority-medium';
                                    $badgeClass = 'badge-medium';
                                    $daysClass = 'warning';
                                } else {
                                    $priorityClass = 'priority-low';
                                    $badgeClass = 'badge-low';
                                    $daysClass = 'success';
                                }
                                
                                $totalValue = $item->current_stock * $item->cost_per_unit;
                            @endphp
                            <tr class="{{ $priorityClass }}">
                                <td>
                                    <strong>{{ $item->name }}</strong><br>
                                    <small class="text-muted">{{ $item->product_type }}</small>
                                </td>
                                <td>{{ $item->batch_number }}</td>
                                <td>{{ $item->production_date ? $item->production_date->format('d/m/Y') : '-' }}</td>
                                <td>{{ $item->expiry_date ? $item->expiry_date->format('d/m/Y') : '-' }}</td>
                                <td>
                                    <span class="days-indicator {{ $daysClass }}">
                                        {{ $item->days_until_expiry >= 0 ? $item->days_until_expiry : 'Kadaluarsa' }}
                                    </span>
                                </td>
                                <td>{{ $item->current_stock }}</td>
                                <td>Rp {{ number_format($totalValue, 0, ',', '.') }}</td>
                                <td>
                                    <span class="badge badge-priority {{ $badgeClass }}">
                                        {{ $item->priority_level }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex">
                                        <a href="{{ route('expiry-recommendations.update', $item->id) }}" class="btn btn-sm btn-info me-1" title="Perbarui Status">
                                            <i class="fas fa-sync-alt"></i>
                                        </a>

                                        <button type="button" class="btn btn-sm btn-warning me-1"
                                            onclick="openDistributionModal({
                                                id: {{ $item->id }},
                                                name: '{{ $item->name }}',
                                                batch_number: '{{ $item->batch_number }}',
                                                current_stock: {{ $item->current_stock }},
                                                expiry_date: '{{ $item->expiry_date ? $item->expiry_date->format('d/m/Y') : '-' }}',
                                                priority_level: '{{ $item->priority_level ?? 'Sedang' }}'
                                            })"
                                            title="Distribusi ke Pasar">
                                            <i class="fas fa-truck-loading me-1"></i> Distribusi
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center">Tidak ada data rekomendasi ubi matang.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>


<!-- Include Modal Distribusi Baru -->
@include('inventory.distribution-modal')

@endsection
