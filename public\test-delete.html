<!DOCTYPE html>
<html>
<head>
    <title>Test User Delete</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .alert { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
        .btn { padding: 8px 15px; margin: 2px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-primary { background: #007bff; color: white; }
        .btn:hover { opacity: 0.8; }
        .status { padding: 3px 8px; border-radius: 3px; font-size: 12px; }
        .status-active { background: #d4edda; color: #155724; }
        .status-deleted { background: #f8d7da; color: #721c24; }
        #result { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test User Delete Functionality</h1>
        
        <div class="alert alert-info">
            <strong>Purpose:</strong> Test the user deletion functionality to ensure it works properly.
            <br><strong>Note:</strong> Only employee users can be safely deleted for testing.
        </div>
        
        <div id="result"></div>
        
        <h2>📋 Current Users</h2>
        <table id="usersTable">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr><td colspan="6">Loading users...</td></tr>
            </tbody>
        </table>
        
        <div class="alert alert-info">
            <h4>🔧 Manual Test Options:</h4>
            <p><strong>Test Delete User ID:</strong> 
                <input type="number" id="testUserId" placeholder="Enter user ID" style="padding: 5px;">
                <button class="btn btn-danger" onclick="testDeleteUser()">Test Delete</button>
            </p>
            <p><strong>Refresh Users:</strong> 
                <button class="btn btn-primary" onclick="loadUsers()">Reload Users</button>
            </p>
        </div>
    </div>

    <script>
        // Load users on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
        });

        function loadUsers() {
            showResult('Loading users...', 'info');
            
            fetch('/show-users')
                .then(response => response.text())
                .then(html => {
                    // Parse the HTML response to extract user data
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const table = doc.querySelector('table');
                    
                    if (table) {
                        const tbody = document.querySelector('#usersTable tbody');
                        tbody.innerHTML = '';
                        
                        const rows = table.querySelectorAll('tr');
                        for (let i = 1; i < rows.length; i++) { // Skip header
                            const cells = rows[i].querySelectorAll('td');
                            if (cells.length >= 4) {
                                const id = cells[0].textContent;
                                const name = cells[1].textContent;
                                const email = cells[2].textContent;
                                const role = cells[3].textContent;
                                
                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>${id}</td>
                                    <td>${name}</td>
                                    <td>${email}</td>
                                    <td>${role}</td>
                                    <td><span class="status status-active">Active</span></td>
                                    <td>
                                        ${role.toLowerCase() === 'employee' ? 
                                            `<button class="btn btn-danger" onclick="deleteUser(${id}, '${name}')">Delete</button>` :
                                            '<span style="color: #666;">Protected</span>'
                                        }
                                    </td>
                                `;
                                tbody.appendChild(row);
                            }
                        }
                        
                        showResult('Users loaded successfully!', 'success');
                    } else {
                        showResult('Failed to parse user data', 'error');
                    }
                })
                .catch(error => {
                    showResult('Error loading users: ' + error.message, 'error');
                });
        }

        function deleteUser(userId, userName) {
            if (!confirm(`Are you sure you want to delete user "${userName}" (ID: ${userId})?`)) {
                return;
            }
            
            showResult(`Deleting user ${userName}...`, 'info');
            
            fetch(`/test-delete-user/${userId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': getCsrfToken()
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult(`✅ User "${userName}" deleted successfully! Deleted at: ${data.deleted_at}`, 'success');
                    loadUsers(); // Reload users to show updated list
                } else {
                    showResult(`❌ Failed to delete user: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showResult(`❌ Error deleting user: ${error.message}`, 'error');
            });
        }

        function testDeleteUser() {
            const userId = document.getElementById('testUserId').value;
            if (!userId) {
                showResult('Please enter a user ID', 'error');
                return;
            }
            
            deleteUser(userId, `User ${userId}`);
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        }

        function getCsrfToken() {
            // Try to get CSRF token from meta tag or cookie
            const metaToken = document.querySelector('meta[name="csrf-token"]');
            if (metaToken) {
                return metaToken.getAttribute('content');
            }
            
            // Fallback: try to get from cookie
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'XSRF-TOKEN') {
                    return decodeURIComponent(value);
                }
            }
            
            return ''; // Return empty if no token found
        }
    </script>
</body>
</html>
