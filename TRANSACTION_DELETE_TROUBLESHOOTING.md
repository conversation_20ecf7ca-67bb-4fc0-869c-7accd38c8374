# Panduan Mengatasi Error Hapus Transaksi

## Masalah yang Te<PERSON><PERSON>di
Error "Lock wait timeout exceeded" saat admin mencoba menghapus transaksi.

## Penyebab Masalah
1. **Database Transaction Lock** - Proses database yang terlalu lama
2. **Concurrent Access** - Beberapa user mengakses data yang sama
3. **Audit Logging** - Proses audit log yang memakan waktu
4. **Complex Operations** - Operasi restore stock yang kompleks

## Solusi yang Telah Diterapkan

### 1. Optimasi Method Delete
- **Bulk Updates** - Menggunakan bulk update untuk restore stock
- **Minimal Transaction Scope** - Mengurangi waktu transaction lock
- **Eager Loading** - Menghindari N+1 query problem
- **Temporary Audit Disable** - Menonaktifkan audit sementara

### 2. Database Configuration
- **Connection Timeout** - Ditambahkan timeout 30 detik
- **Buffered Query** - Menggunakan buffered query untuk performa

### 3. Permission & Validation
- **Admin Only** - Hanya admin yang bisa hapus transaksi
- **Age Limit** - Transaksi >30 hari tidak bisa dihapus
- **Error Logging** - Log error untuk debugging

## Cara Menggunakan

### Hapus Transaksi Normal
1. Login sebagai **Admin**
2. Masuk ke **Modul Transaksi**
3. Klik tombol **Hapus** pada transaksi yang diinginkan
4. Konfirmasi penghapusan

### Jika Masih Error
1. Jalankan command pembersihan database:
   ```bash
   php artisan db:clear-locks
   ```

2. Coba hapus transaksi lagi

3. Jika masih gagal, gunakan **Force Delete** (emergency only):
   - Hubungi developer untuk akses force delete
   - Force delete tidak akan restore stock

## Pencegahan Error

### 1. Hindari Operasi Bersamaan
- Jangan hapus transaksi bersamaan dengan user lain
- Tunggu proses selesai sebelum operasi lain

### 2. Hapus Transaksi Secara Berkala
- Jangan menumpuk terlalu banyak transaksi
- Hapus transaksi lama secara berkala

### 3. Monitor Database
- Jalankan `php artisan db:clear-locks` secara berkala
- Monitor performa database

## Command Berguna

### Cek Database Locks
```bash
php artisan db:clear-locks
```

### Cek Log Error
```bash
tail -f storage/logs/laravel.log
```

### Restart Database Connection
```bash
php artisan config:clear
php artisan cache:clear
```

## Kontak Support
Jika masalah masih berlanjut, hubungi developer dengan informasi:
1. **Waktu error terjadi**
2. **ID transaksi yang ingin dihapus**
3. **Screenshot error message**
4. **Log file** (storage/logs/laravel.log)
