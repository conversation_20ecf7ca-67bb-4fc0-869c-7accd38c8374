<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use Carbon\Carbon;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing users (optional)
        User::truncate();

        // Create Admin User
        User::create([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'role' => User::ROLE_ADMIN,
            'password' => Hash::make('admin123'),
            'email_verified_at' => Carbon::now(),
            'last_activity' => Carbon::now(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);

        // Create Employee User
        User::create([
            'name' => 'Karyawan <PERSON>',
            'email' => '<EMAIL>',
            'role' => User::ROLE_EMPLOYEE,
            'password' => Hash::make('karyawan123'),
            'email_verified_at' => Carbon::now(),
            'last_activity' => Carbon::now(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);

        // Create Additional Test Users
        User::create([
            'name' => 'Siti Nurhaliza',
            'email' => '<EMAIL>',
            'role' => User::ROLE_EMPLOYEE,
            'password' => Hash::make('password123'),
            'email_verified_at' => Carbon::now(),
            'last_activity' => Carbon::now()->subDays(2),
            'created_at' => Carbon::now()->subDays(30),
            'updated_at' => Carbon::now()->subDays(2),
        ]);

        User::create([
            'name' => 'Ahmad Fauzi',
            'email' => '<EMAIL>',
            'role' => User::ROLE_EMPLOYEE,
            'password' => Hash::make('password123'),
            'email_verified_at' => Carbon::now(),
            'last_activity' => Carbon::now()->subDays(1),
            'created_at' => Carbon::now()->subDays(20),
            'updated_at' => Carbon::now()->subDays(1),
        ]);

        User::create([
            'name' => 'Rina Marlina',
            'email' => '<EMAIL>',
            'role' => User::ROLE_ADMIN,
            'password' => Hash::make('password123'),
            'email_verified_at' => Carbon::now(),
            'last_activity' => Carbon::now()->subHours(5),
            'created_at' => Carbon::now()->subDays(15),
            'updated_at' => Carbon::now()->subHours(5),
        ]);

        $this->command->info('Users seeded successfully!');
        $this->command->info('Admin: <EMAIL> / admin123');
        $this->command->info('Employee: <EMAIL> / karyawan123');
    }
}
