<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Optimize users table for better performance and prevent timeouts

        // 1. Add indexes for better query performance
        Schema::table('users', function (Blueprint $table) {
            // Add index on email for faster login queries
            if (!$this->indexExists('users', 'users_email_index')) {
                $table->index('email', 'users_email_index');
            }

            // Add index on role for faster role-based queries
            if (!$this->indexExists('users', 'users_role_index')) {
                $table->index('role', 'users_role_index');
            }

            // Add index on deleted_at for faster soft delete queries
            if (!$this->indexExists('users', 'users_deleted_at_index')) {
                $table->index('deleted_at', 'users_deleted_at_index');
            }

            // Add composite index for active users by role
            if (!$this->indexExists('users', 'users_role_deleted_at_index')) {
                $table->index(['role', 'deleted_at'], 'users_role_deleted_at_index');
            }
        });

        // 2. Optimize database settings for users table
        DB::statement('ALTER TABLE users ENGINE=InnoDB');
        DB::statement('ALTER TABLE users ROW_FORMAT=DYNAMIC');

        // 3. Ensure role enum is properly set
        $roleInfo = DB::select("SHOW COLUMNS FROM users WHERE Field = 'role'");
        if (!empty($roleInfo)) {
            $currentType = $roleInfo[0]->Type;
            if (strpos($currentType, 'admin') === false || strpos($currentType, 'employee') === false) {
                DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'employee') NOT NULL DEFAULT 'employee'");
            }
        }

        // 4. Update any existing invalid role values
        DB::statement("UPDATE users SET role = 'employee' WHERE role NOT IN ('admin', 'employee')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Remove indexes
            $table->dropIndex('users_email_index');
            $table->dropIndex('users_role_index');
            $table->dropIndex('users_deleted_at_index');
            $table->dropIndex('users_role_deleted_at_index');
        });
    }

    /**
     * Check if index exists
     */
    private function indexExists($table, $index): bool
    {
        $indexes = DB::select("SHOW INDEX FROM {$table} WHERE Key_name = '{$index}'");
        return !empty($indexes);
    }
};
