<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pembayaran Berhasil - Ubi Bakar Cilembu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .success-card {
            background: white;
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
            animation: popIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            max-width: 500px;
            width: 100%;
        }
        
        @keyframes popIn {
            0% {
                opacity: 0;
                transform: scale(0.8) translateY(40px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }
        
        .success-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .success-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0%, 100% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
        }
        
        .success-icon {
            font-size: 4rem;
            margin-bottom: 15px;
            animation: bounce 1.5s ease-in-out infinite;
            position: relative;
            z-index: 1;
        }
        
        @keyframes bounce {
            0%, 20%, 60%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-15px);
            }
            80% {
                transform: translateY(-7px);
            }
        }
        
        .success-title {
            font-size: 2rem;
            font-weight: bold;
            margin: 0;
            position: relative;
            z-index: 1;
        }
        
        .success-body {
            padding: 40px 30px;
        }
        
        .info-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .info-row:last-child {
            margin-bottom: 0;
        }
        
        .info-label {
            color: #6c757d;
            font-weight: 500;
        }
        
        .info-value {
            color: #212529;
            font-weight: bold;
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 15px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }
        
        .btn-custom::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-custom:hover::before {
            left: 100%;
        }
        
        .btn-custom:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        
        .btn-primary-custom {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }
        
        .btn-secondary-custom {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }
        
        .countdown-text {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 20px;
            text-align: center;
        }
        
        .checkmark {
            display: inline-block;
            width: 22px;
            height: 22px;
            border-radius: 50%;
            background: #28a745;
            color: white;
            text-align: center;
            line-height: 22px;
            font-size: 14px;
            margin-right: 10px;
        }
        
        @media (max-width: 576px) {
            .success-header {
                padding: 25px 20px;
            }
            
            .success-body {
                padding: 30px 20px;
            }
            
            .success-icon {
                font-size: 3rem;
            }
            
            .success-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="success-card mx-auto">
                    <div class="success-header">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h1 class="success-title">Pembayaran Berhasil!</h1>
                        <p class="mb-0 mt-2">Transaksi telah diproses dengan sukses</p>
                    </div>
                    
                    <div class="success-body">
                        <div class="info-card">
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-receipt me-2"></i>Invoice
                                </span>
                                <span class="info-value"><?php echo e($invoice_number); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-money-bill-wave me-2"></i>Total Bayar
                                </span>
                                <span class="info-value">Rp <?php echo e(number_format($total_amount, 0, ',', '.')); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-clock me-2"></i>Waktu
                                </span>
                                <span class="info-value"><?php echo e(now()->format('d/m/Y H:i')); ?></span>
                            </div>
                        </div>
                        
                        <div class="text-center mb-4">
                            <p class="text-muted mb-3">
                                <span class="checkmark">
                                    <i class="fas fa-check"></i>
                                </span>
                                Pembayaran berhasil diverifikasi
                            </p>
                            <p class="text-muted mb-0">
                                <span class="checkmark">
                                    <i class="fas fa-check"></i>
                                </span>
                                Data transaksi telah tersimpan
                            </p>
                        </div>
                        
                        <div class="d-grid gap-3">
                            <a href="<?php echo e(route('pos')); ?>" class="btn btn-custom btn-primary-custom">
                                <i class="fas fa-cash-register me-2"></i>
                                Kembali ke POS
                            </a>
                            <a href="<?php echo e(route('transactions.index')); ?>" class="btn btn-custom btn-secondary-custom">
                                <i class="fas fa-list me-2"></i>
                                Lihat Semua Transaksi
                            </a>
                        </div>
                        
                        <div class="countdown-text" id="countdownText">
                            Otomatis kembali ke POS dalam <span id="countdown">8</span> detik
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto redirect countdown
        let countdown = 8;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = "<?php echo e(route('pos')); ?>";
            }
        }, 1000);
        
        // Add click handlers for immediate navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                window.location.href = "<?php echo e(route('pos')); ?>";
            }
        });
    </script>
</body>
</html>
<?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/payment/success.blade.php ENDPATH**/ ?>