<?php $__env->startSection('title', 'Catatan Aktivitas'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .custom-pagination .page-item .page-link {
        padding: 0.5rem 1rem;
        border-radius: 4px;
        margin: 0 3px;
        color: #8B4513;
        background-color: #fff;
        border-color: #ddd;
        transition: all 0.2s ease;
    }
    
    .custom-pagination .page-item.active .page-link {
        background-color: #8B4513;
        border-color: #8B4513;
        color: white;
    }
    
    .custom-pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
        border-color: #ddd;
        box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .custom-pagination .page-item.disabled .page-link {
        color: #6c757d;
        pointer-events: none;
        background-color: #fff;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mt-4 mb-4">
        <div>
            <h1 class="mb-0">Catatan Aktivitas</h1>
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                <li class="breadcrumb-item active">Catatan Aktivitas</li>
            </ol>
        </div>
        <div>
            <a href="<?php echo e(route('admin.audit-logs.export')); ?><?php echo e(request()->getQueryString() ? '?' . request()->getQueryString() : ''); ?>"
               class="btn btn-success">
                <i class="fas fa-download me-1"></i>Export CSV
            </a>
        </div>
    </div>

    <!-- Statistics Dashboard -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">Total Catatan</div>
                            <div class="text-lg fw-bold"><?php echo e(number_format($stats['total_logs'])); ?></div>
                        </div>
                        <i class="fas fa-database fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">Hari Ini</div>
                            <div class="text-lg fw-bold"><?php echo e(number_format($stats['today_logs'])); ?></div>
                        </div>
                        <i class="fas fa-calendar-day fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">Minggu Ini</div>
                            <div class="text-lg fw-bold"><?php echo e(number_format($stats['this_week_logs'])); ?></div>
                        </div>
                        <i class="fas fa-calendar-week fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">Bulan Ini</div>
                            <div class="text-lg fw-bold"><?php echo e(number_format($stats['this_month_logs'])); ?></div>
                        </div>
                        <i class="fas fa-calendar-alt fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Summary -->
    <div class="row mb-4">
        <div class="col-xl-8">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-clock me-1"></i>
                    Aktivitas Terbaru
                </div>
                <div class="card-body">
                    <?php if($recentActivity->count() > 0): ?>
                        <div class="list-group list-group-flush">
                            <?php $__currentLoopData = $recentActivity; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><?php echo e($activity->user->name ?? 'System'); ?></strong>
                                    <span class="badge bg-<?php echo e($activity->action == 'create' ? 'success' : ($activity->action == 'update' ? 'warning' : 'danger')); ?> ms-2">
                                        <?php echo e(ucfirst($activity->action)); ?>

                                    </span>
                                    <span class="text-muted"><?php echo e(class_basename($activity->model_type)); ?></span>
                                </div>
                                <small class="text-muted"><?php echo e($activity->created_at->diffForHumans()); ?></small>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted mb-0">Belum ada aktivitas terbaru.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-xl-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-users me-1"></i>
                    Pengguna Paling Aktif (30 Hari)
                </div>
                <div class="card-body">
                    <?php if($activeUsers->count() > 0): ?>
                        <div class="list-group list-group-flush">
                            <?php $__currentLoopData = $activeUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $userActivity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><?php echo e($userActivity->user->name ?? 'Unknown'); ?></strong>
                                    <br>
                                    <small class="text-muted"><?php echo e($userActivity->user->role ?? 'N/A'); ?></small>
                                </div>
                                <span class="badge bg-primary rounded-pill"><?php echo e($userActivity->activity_count); ?></span>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted mb-0">Belum ada data aktivitas pengguna.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Filter Catatan Aktivitas
        </div>
        <div class="card-body">
            <form action="<?php echo e(route('admin.audit-logs.index')); ?>" method="GET" class="row g-3">
                <div class="col-md-2">
                    <label for="action" class="form-label">Aksi</label>
                    <select class="form-select" id="action" name="action">
                        <option value="">Semua Aksi</option>
                        <?php $__currentLoopData = $actions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $act): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($act); ?>" <?php echo e($action == $act ? 'selected' : ''); ?>>
                                <?php echo e(ucfirst($act)); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="model_type" class="form-label">Jenis Aktivitas</label>
                    <select class="form-select" id="model_type" name="model_type">
                        <option value="">Semua Jenis</option>
                        <?php $__currentLoopData = $modelTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($type); ?>" <?php echo e($modelType == $type ? 'selected' : ''); ?>>
                                <?php if(class_basename($type) == 'User'): ?>
                                    Pengguna
                                <?php elseif(class_basename($type) == 'RawInventory'): ?>
                                    Stok Ubi Mentah
                                <?php elseif(class_basename($type) == 'ProcessedInventory'): ?>
                                    Stok Ubi Bakar
                                <?php elseif(class_basename($type) == 'OtherProduct'): ?>
                                    Produk Lainnya
                                <?php elseif(class_basename($type) == 'Transaction'): ?>
                                    Transaksi
                                <?php elseif(class_basename($type) == 'ProductionLog'): ?>
                                    Log Produksi
                                <?php else: ?>
                                    <?php echo e(class_basename($type)); ?>

                                <?php endif; ?>
                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="user_id" class="form-label">Pengguna</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">Semua Pengguna</option>
                        <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($user->id); ?>" <?php echo e($userId == $user->id ? 'selected' : ''); ?>>
                                <?php echo e($user->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="date_from" class="form-label">Dari Tanggal</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo e($dateFrom); ?>">
                </div>

                <div class="col-md-2">
                    <label for="date_to" class="form-label">Sampai Tanggal</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo e($dateTo); ?>">
                </div>

                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                </div>
            </form>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Daftar Aktivitas Pengguna
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Pengguna</th>
                            <th>Aksi</th>
                            <th>Deskripsi Aktivitas</th>
                            <th>Waktu</th>
                            <th>Detail</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $auditLogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($log->id); ?></td>
                                <td><?php echo e($log->user ? $log->user->name : 'Sistem'); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo e($log->action == 'create' ? 'success' : ($log->action == 'update' ? 'primary' : ($log->action == 'delete' ? 'danger' : 'warning'))); ?>">
                                        <?php if($log->action == 'create'): ?>
                                            Tambah
                                        <?php elseif($log->action == 'update'): ?>
                                            Ubah
                                        <?php elseif($log->action == 'delete'): ?>
                                            Hapus
                                        <?php else: ?>
                                            <?php echo e(ucfirst($log->action)); ?>

                                        <?php endif; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if(class_basename($log->model_type) == 'User'): ?>
                                        <?php if($log->action == 'create'): ?>
                                            Menambahkan pengguna baru
                                        <?php elseif($log->action == 'update'): ?>
                                            Mengubah data pengguna
                                        <?php elseif($log->action == 'delete'): ?>
                                            Menghapus pengguna
                                        <?php endif; ?>
                                    <?php elseif(class_basename($log->model_type) == 'RawInventory'): ?>
                                        <?php if($log->action == 'create'): ?>
                                            Menambahkan stok ubi mentah baru
                                        <?php elseif($log->action == 'update'): ?>
                                            Mengubah data stok ubi mentah
                                        <?php elseif($log->action == 'delete'): ?>
                                            Menghapus stok ubi mentah
                                        <?php endif; ?>
                                    <?php elseif(class_basename($log->model_type) == 'ProcessedInventory'): ?>
                                        <?php if($log->action == 'create'): ?>
                                            Menambahkan stok ubi bakar baru
                                        <?php elseif($log->action == 'update'): ?>
                                            Mengubah data stok ubi bakar
                                        <?php elseif($log->action == 'delete'): ?>
                                            Menghapus stok ubi bakar
                                        <?php endif; ?>
                                    <?php elseif(class_basename($log->model_type) == 'OtherProduct'): ?>
                                        <?php if($log->action == 'create'): ?>
                                            Menambahkan produk baru
                                        <?php elseif($log->action == 'update'): ?>
                                            Mengubah data produk
                                        <?php elseif($log->action == 'delete'): ?>
                                            Menghapus produk
                                        <?php endif; ?>
                                    <?php elseif(class_basename($log->model_type) == 'Transaction'): ?>
                                        <?php if($log->action == 'create'): ?>
                                            Membuat transaksi baru
                                        <?php elseif($log->action == 'update'): ?>
                                            Mengubah data transaksi
                                        <?php elseif($log->action == 'delete'): ?>
                                            Menghapus transaksi
                                        <?php endif; ?>
                                    <?php elseif(class_basename($log->model_type) == 'ProductionLog'): ?>
                                        <?php if($log->action == 'create'): ?>
                                            Mencatat produksi baru
                                        <?php elseif($log->action == 'update'): ?>
                                            Mengubah data produksi
                                        <?php elseif($log->action == 'delete'): ?>
                                            Menghapus catatan produksi
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <?php echo e(class_basename($log->model_type)); ?> - <?php echo e($log->action); ?>

                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($log->created_at->format('d M Y H:i:s')); ?></td>
                                <td>
                                    <a href="<?php echo e(route('admin.audit-logs.show', $log)); ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> Detail
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="6" class="text-center">Tidak ada data aktivitas</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center mt-4">
                <div class="pagination-wrapper">
                    <nav>
                        <ul class="pagination custom-pagination">
                            <!-- Previous Page Link -->
                            <?php if($auditLogs->onFirstPage()): ?>
                                <li class="page-item disabled">
                                    <span class="page-link"><i class="fas fa-chevron-left me-1"></i> Previous</span>
                                </li>
                            <?php else: ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo e($auditLogs->previousPageUrl()); ?>" rel="prev"><i class="fas fa-chevron-left me-1"></i> Previous</a>
                                </li>
                            <?php endif; ?>

                            <!-- Pagination Elements -->
                            <?php $__currentLoopData = $auditLogs->links()->elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <!-- "Three Dots" Separator -->
                                <?php if(is_string($element)): ?>
                                    <li class="page-item disabled"><span class="page-link"><?php echo e($element); ?></span></li>
                                <?php endif; ?>

                                <!-- Array Of Links -->
                                <?php if(is_array($element)): ?>
                                    <?php $__currentLoopData = $element; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($page == $auditLogs->currentPage()): ?>
                                            <li class="page-item active"><span class="page-link"><?php echo e($page); ?></span></li>
                                        <?php else: ?>
                                            <li class="page-item"><a class="page-link" href="<?php echo e($url); ?>"><?php echo e($page); ?></a></li>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <!-- Next Page Link -->
                            <?php if($auditLogs->hasMorePages()): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo e($auditLogs->nextPageUrl()); ?>" rel="next">Next <i class="fas fa-chevron-right ms-1"></i></a>
                                </li>
                            <?php else: ?>
                                <li class="page-item disabled">
                                    <span class="page-link">Next <i class="fas fa-chevron-right ms-1"></i></span>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
            </div>
            
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="pagination-info small text-muted">
                    Showing <?php echo e($auditLogs->firstItem() ?? 0); ?> to <?php echo e($auditLogs->lastItem() ?? 0); ?> of <?php echo e($auditLogs->total()); ?> results
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/admin/audit-logs/index.blade.php ENDPATH**/ ?>