<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-receipt"></i>
        <span>Detail Transaksi</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Detail Transaksi #<?php echo e($transaction->invoice_number); ?></span>
                    <div>
                        <a href="<?php echo e(route('transactions.print', $transaction)); ?>" class="btn btn-primary me-2" target="_blank">
                            <i class="fas fa-print"></i> Cetak
                        </a>
                        <div class="btn-group me-2" role="group">
                            <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-download"></i> Export PDF
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo e(route('transactions.export-pdf', $transaction)); ?>">
                                    <i class="fas fa-file-pdf"></i> Download PDF (Server)
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('transactions.export-html', $transaction)); ?>" target="_blank">
                                    <i class="fas fa-globe"></i> Save as PDF (Browser)
                                </a></li>
                            </ul>
                        </div>
                        <a href="<?php echo e(route('transactions.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php
                        // Determine payment status
                        $paymentStatus = $transaction->payment_status ?? 'completed';
                        $transactionStatus = $transaction->status ?? 'completed';

                        // For gateway payments, use payment_status
                        if ($transaction->payment_method === 'gateway') {
                            $statusClass = '';
                            $statusText = '';
                            $statusIcon = '';

                            switch ($paymentStatus) {
                                case 'paid':
                                    $statusClass = 'success';
                                    $statusText = 'Pembayaran Selesai';
                                    $statusIcon = 'fas fa-check-circle';
                                    break;
                                case 'pending':
                                    $statusClass = 'warning';
                                    $statusText = 'Menunggu Pembayaran';
                                    $statusIcon = 'fas fa-clock';
                                    break;
                                case 'failed':
                                    $statusClass = 'danger';
                                    $statusText = 'Pembayaran Gagal';
                                    $statusIcon = 'fas fa-times-circle';
                                    break;
                                case 'cancelled':
                                    $statusClass = 'secondary';
                                    $statusText = 'Pembayaran Dibatalkan';
                                    $statusIcon = 'fas fa-ban';
                                    break;
                                case 'expired':
                                    $statusClass = 'dark';
                                    $statusText = 'Pembayaran Kedaluwarsa';
                                    $statusIcon = 'fas fa-hourglass-end';
                                    break;
                                default:
                                    $statusClass = 'info';
                                    $statusText = ucfirst($paymentStatus);
                                    $statusIcon = 'fas fa-info-circle';
                            }
                        } else {
                            // For manual payments, use transaction status
                            switch ($transactionStatus) {
                                case 'completed':
                                    $statusClass = 'success';
                                    $statusText = 'Transaksi Selesai';
                                    $statusIcon = 'fas fa-check-circle';
                                    break;
                                case 'pending':
                                    $statusClass = 'warning';
                                    $statusText = 'Transaksi Pending';
                                    $statusIcon = 'fas fa-clock';
                                    break;
                                case 'cancelled':
                                    $statusClass = 'secondary';
                                    $statusText = 'Transaksi Dibatalkan';
                                    $statusIcon = 'fas fa-ban';
                                    break;
                                default:
                                    $statusClass = 'info';
                                    $statusText = ucfirst($transactionStatus);
                                    $statusIcon = 'fas fa-info-circle';
                            }
                        }
                    ?>

                    <!-- Status Alert -->
                    <div class="alert alert-<?php echo e($statusClass); ?> d-flex align-items-center mb-4" role="alert">
                        <i class="<?php echo e($statusIcon); ?> me-3 fa-lg"></i>
                        <div>
                            <strong><?php echo e($statusText); ?></strong>
                            <?php if($transaction->payment_method === 'gateway' && $paymentStatus === 'pending'): ?>
                                <br><small>Silakan selesaikan pembayaran melalui Midtrans</small>
                            <?php endif; ?>
                        </div>
                        <?php if($transaction->payment_method === 'gateway' && $paymentStatus === 'pending' && $transaction->snap_redirect_url): ?>
                            <div class="ms-auto">
                                <a href="<?php echo e($transaction->snap_redirect_url); ?>" class="btn btn-sm btn-primary" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i> Bayar Sekarang
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Informasi Transaksi</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 40%">No. Invoice</th>
                                    <td><?php echo e($transaction->invoice_number); ?></td>
                                </tr>
                                <tr>
                                    <th>Tanggal & Waktu</th>
                                    <td><?php echo e($transaction->created_at->format('d M Y H:i:s')); ?></td>
                                </tr>
                                <tr>
                                    <th>Kasir</th>
                                    <td><?php echo e($transaction->user->name ?? 'Admin'); ?></td>
                                </tr>
                                <tr>
                                    <th>Catatan</th>
                                    <td><?php echo e($transaction->note ?? '-'); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Informasi Pembayaran</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 40%;">Total Tagihan</th>
                                    <td><strong>Rp <?php echo e(number_format($transaction->total_amount, 0, ',', '.')); ?></strong></td>
                                </tr>
                                <tr>
                                    <th>Metode Pembayaran</th>
                                    <td>
                                        <?php if($transaction->payment_method === 'gateway'): ?>
                                            <span class="badge bg-primary">Payment Gateway</span>
                                            <small class="text-muted d-block">Midtrans</small>
                                        <?php else: ?>
                                            <span class="badge bg-success">Manual</span>
                                            <small class="text-muted d-block"><?php echo e(ucfirst($transaction->payment_method)); ?></small>
                                        <?php endif; ?>
                                    </td>
                                </tr>

                                <?php if($transaction->payment_method === 'gateway'): ?>
                                    <!-- Gateway Payment Information -->
                                    <?php if($transaction->payment_gateway_paid_at): ?>
                                    <tr>
                                        <th>Dibayar Pada</th>
                                        <td><?php echo e($transaction->payment_gateway_paid_at->format('d M Y H:i:s')); ?></td>
                                    </tr>
                                    <?php endif; ?>

                                    <?php if($transaction->payment_gateway_transaction_id): ?>
                                    <tr>
                                        <th>Transaction ID</th>
                                        <td><code><?php echo e($transaction->payment_gateway_transaction_id); ?></code></td>
                                    </tr>
                                    <?php endif; ?>



                                    <tr>
                                        <th>Uang Kembalian</th>
                                        <td>
                                            <span class="text-muted">Rp 0</span>
                                            <small class="text-muted d-block">Tidak ada kembalian untuk payment gateway</small>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <!-- Manual Payment Information -->
                                    <tr>
                                        <th>Jumlah Dibayar</th>
                                        <td>
                                            <span class="fw-bold">Rp <?php echo e(number_format($transaction->amount_paid ?? 0, 0, ',', '.')); ?></span>
                                        </td>
                                    </tr>

                                    <tr>
                                        <th>Uang Kembalian</th>
                                        <td>
                                            <?php if(($transaction->change_amount ?? 0) > 0): ?>
                                                <span class="text-info fw-bold">Rp <?php echo e(number_format($transaction->change_amount, 0, ',', '.')); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">Rp 0</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>

                    <h5>Daftar Item</h5>
                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Nama Produk</th>
                                    <th>Harga Satuan</th>
                                    <th>Jumlah</th>
                                    <th>Subtotal</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $transaction->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($index + 1); ?></td>
                                    <td><?php echo e($item->product_name); ?></td>
                                    <td>Rp <?php echo e(number_format($item->price, 0, ',', '.')); ?></td>
                                    <td><?php echo e($item->quantity); ?></td>
                                    <td>Rp <?php echo e(number_format($item->subtotal, 0, ',', '.')); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="4" class="text-end">Total</th>
                                    <th>Rp <?php echo e(number_format($transaction->total_amount, 0, ',', '.')); ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>





<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/transactions/show.blade.php ENDPATH**/ ?>