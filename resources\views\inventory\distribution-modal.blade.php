<!-- Modal Buat Distribusi Produk -->
<div class="modal fade" id="distributionModal" tabindex="-1" aria-labelledby="distributionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="distributionModalLabel">
                    <i class="fas fa-truck"></i> Buat Distribusi Produk
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            
            <form id="distributionForm" method="POST">
                @csrf
                <div class="modal-body">
                    <!-- Alert untuk notifikasi -->
                    <div id="distributionAlert" class="alert d-none" role="alert"></div>
                    
                    <!-- Info Produk -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">
                                        <i class="fas fa-info-circle"></i> Informasi Produk
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p class="mb-1"><strong>Nama Produk:</strong></p>
                                            <p id="productName" class="text-muted">-</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p class="mb-1"><strong>Nomor Batch:</strong></p>
                                            <p id="productBatch" class="text-muted">-</p>
                                        </div>
                                        <div class="col-md-3">
                                            <p class="mb-1"><strong>Stok Tersedia:</strong></p>
                                            <p id="productStock" class="text-muted">-</p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p class="mb-1"><strong>Tanggal Kadaluarsa:</strong></p>
                                            <p id="productExpiry" class="text-muted">-</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="mb-1"><strong>Status Prioritas:</strong></p>
                                            <span id="productPriority" class="badge bg-warning">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Input Distribusi -->
                    <div class="row">
                        <!-- Hidden input untuk product ID -->
                        <input type="hidden" id="processed_inventory_id" name="processed_inventory_id" value="">
                        
                        <!-- Tujuan Distribusi -->
                        <div class="col-md-12 mb-3">
                            <label for="destination" class="form-label">
                                <i class="fas fa-map-marker-alt"></i> Tujuan Distribusi <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="destination" name="destination"
                                   list="destination-suggestions"
                                   placeholder="Masukkan tujuan distribusi (contoh: Pasar Baru Bandung)"
                                   required>
                            <datalist id="destination-suggestions">
                                <option value="Pasar Baru Bandung">
                                <option value="Pasar Cicadas">
                                <option value="Pasar Kosambi">
                                <option value="Pasar Caringin">
                                <option value="Pasar Gedebage">
                                <option value="Pasar Cihapit">
                                <option value="Pasar Induk Kramat Jati">
                                <option value="Pasar Anyar Bogor">
                                <option value="Toko Swalayan">
                                <option value="Distributor Lokal">
                            </datalist>
                            <div class="invalid-feedback"></div>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle"></i>
                                Ketik untuk mencari atau pilih dari suggestions. Contoh: Pasar Baru Bandung, Toko ABC, dll.
                            </small>
                        </div>

                        <!-- Jumlah yang Akan Didistribusikan -->
                        <div class="col-md-6 mb-3">
                            <label for="quantity" class="form-label">
                                <i class="fas fa-boxes"></i> Jumlah yang Akan Didistribusikan <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="quantity" name="quantity" 
                                       min="1" max="" required>
                                <span class="input-group-text">unit</span>
                            </div>
                            <div class="invalid-feedback"></div>
                            <small class="form-text text-muted">Maksimal: <span id="maxQuantity">0</span> unit</small>
                        </div>

                        <!-- Tanggal Distribusi -->
                        <div class="col-md-6 mb-3">
                            <label for="distribution_date" class="form-label">
                                <i class="fas fa-calendar-alt"></i> Tanggal Distribusi <span class="text-danger">*</span>
                            </label>
                            <input type="date" class="form-control" id="distribution_date" name="distribution_date" 
                                   min="{{ date('Y-m-d') }}" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Catatan -->
                        <div class="col-md-12 mb-3">
                            <label for="notes" class="form-label">
                                <i class="fas fa-sticky-note"></i> Catatan (Opsional)
                            </label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="Catatan tambahan untuk distribusi ini..."></textarea>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-primary" id="submitDistribution">
                        <i class="fas fa-truck"></i> Buat Distribusi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- JavaScript untuk Modal Distribusi -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const distributionModal = document.getElementById('distributionModal');
    const distributionForm = document.getElementById('distributionForm');
    const alertDiv = document.getElementById('distributionAlert');
    
    // Function untuk menampilkan alert
    function showAlert(message, type = 'success') {
        alertDiv.className = `alert alert-${type}`;
        alertDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i> ${message}`;
        alertDiv.classList.remove('d-none');
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            alertDiv.classList.add('d-none');
        }, 5000);
    }
    
    // Function untuk membuka modal dengan data produk
    window.openDistributionModal = function(productData) {
        // Set data produk ke modal
        document.getElementById('processed_inventory_id').value = productData.id;
        document.getElementById('productName').textContent = productData.name;
        document.getElementById('productBatch').textContent = productData.batch_number || '-';
        document.getElementById('productStock').textContent = productData.current_stock + ' unit';
        document.getElementById('productExpiry').textContent = productData.expiry_date;
        document.getElementById('maxQuantity').textContent = productData.current_stock;
        document.getElementById('quantity').max = productData.current_stock;
        
        // Set priority badge
        const priorityBadge = document.getElementById('productPriority');
        const priority = productData.priority_level || 'Sedang';
        priorityBadge.textContent = priority;
        priorityBadge.className = `badge ${priority === 'Tinggi' ? 'bg-danger' : priority === 'Sedang' ? 'bg-warning' : 'bg-info'}`;
        
        // Set default date to today
        document.getElementById('distribution_date').value = new Date().toISOString().split('T')[0];
        
        // Reset form
        distributionForm.reset();
        document.getElementById('processed_inventory_id').value = productData.id;
        document.getElementById('distribution_date').value = new Date().toISOString().split('T')[0];
        alertDiv.classList.add('d-none');

        // Reset validation classes
        const inputs = distributionForm.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.classList.remove('is-valid', 'is-invalid');
        });
        
        // Show modal
        const modal = new bootstrap.Modal(distributionModal);
        modal.show();
    };
    
    // Validasi input tujuan distribusi
    const destinationInput = document.getElementById('destination');
    destinationInput.addEventListener('input', function() {
        const value = this.value.trim();
        const feedback = this.nextElementSibling;

        if (value.length < 3) {
            this.classList.add('is-invalid');
            this.classList.remove('is-valid');
            feedback.textContent = 'Tujuan distribusi minimal 3 karakter';
        } else {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
            feedback.textContent = '';
        }
    });

    // Handle form submission
    distributionForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validasi tambahan sebelum submit
        const destination = document.getElementById('destination').value.trim();
        if (destination.length < 3) {
            showAlert('Tujuan distribusi harus diisi minimal 3 karakter', 'danger');
            return;
        }

        const submitBtn = document.getElementById('submitDistribution');
        const originalText = submitBtn.innerHTML;

        // Show loading
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memproses...';
        submitBtn.disabled = true;
        
        // Prepare form data
        const formData = new FormData(distributionForm);
        
        // Submit via AJAX
        fetch('/save-distribution-data', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                
                // Reset form after success
                setTimeout(() => {
                    const modal = bootstrap.Modal.getInstance(distributionModal);
                    modal.hide();
                    
                    // Reload page to update data
                    window.location.reload();
                }, 2000);
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Terjadi kesalahan saat memproses distribusi', 'danger');
        })
        .finally(() => {
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
});
</script>
