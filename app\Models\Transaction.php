<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\Auditable;

class Transaction extends Model
{
    use HasFactory, SoftDeletes;
    // Temporarily disable Auditable to prevent locks
    // use Auditable;

    protected $table = 'transactions';

    protected $fillable = [
        'invoice_number',
        'user_id',
        'customer_name',
        'customer_phone',
        'subtotal',
        'tax',
        'discount',
        'total_amount',
        'amount_paid',
        'change_amount',
        'payment_method',
        'payment_gateway',
        'payment_gateway_transaction_id',
        'payment_gateway_order_id',
        'payment_gateway_status',
        'payment_gateway_response',
        'snap_token',
        'snap_redirect_url',
        'payment_gateway_paid_at',
        'payment_gateway_expired_at',
        'status',
        'payment_status',
        'notes'
    ];

    protected static function booted()
    {
        static::creating(function ($transaction) {
            if (empty($transaction->invoice_number)) {
                // Generate completely unique invoice number without any database operations
                $timestamp = now()->format('YmdHis');
                $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
                $transaction->invoice_number = "INV/{$timestamp}/{$random}";
            }
        });
    }

    /**
     * Get the user that created the transaction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the transaction items for this transaction.
     */
    public function items(): HasMany
    {
        return $this->hasMany(TransactionItem::class);
    }

    /**
     * Cast attributes to appropriate types
     */
    protected function casts(): array
    {
        return [
            'payment_gateway_paid_at' => 'datetime',
            'payment_gateway_expired_at' => 'datetime',
            'payment_gateway_response' => 'array',
        ];
    }

    /**
     * Check if transaction is paid via payment gateway
     */
    public function isPaidViaGateway(): bool
    {
        return !empty($this->payment_gateway) &&
               $this->payment_gateway_status === 'settlement' &&
               $this->status === 'completed';
    }

    /**
     * Check if transaction is pending payment
     */
    public function isPendingPayment(): bool
    {
        return $this->status === 'pending' && !empty($this->snap_token);
    }

    /**
     * Get payment gateway display name
     */
    public function getPaymentGatewayDisplayAttribute(): string
    {
        if (empty($this->payment_gateway)) {
            return ucfirst($this->payment_method);
        }

        $gateways = [
            'midtrans' => 'Midtrans',
            'xendit' => 'Xendit',
            'doku' => 'DOKU',
        ];

        return $gateways[$this->payment_gateway] ?? ucfirst($this->payment_gateway);
    }

    /**
     * Get status badge HTML
     */
    public function getStatusBadgeAttribute(): string
    {
        $badges = [
            'pending' => 'bg-warning',
            'completed' => 'bg-success',
            'cancelled' => 'bg-secondary',
            'refunded' => 'bg-info',
            'failed' => 'bg-danger',
            'expired' => 'bg-dark'
        ];

        $class = $badges[$this->status] ?? 'bg-secondary';

        // Translate status to Indonesian
        $statusTexts = [
            'pending' => 'Pending',
            'completed' => 'Selesai',
            'cancelled' => 'Batal',
            'refunded' => 'Dikembalikan',
            'failed' => 'Gagal',
            'expired' => 'Kedaluwarsa'
        ];

        $text = $statusTexts[$this->status] ?? ucfirst($this->status);

        return '<span class="badge ' . $class . '">' . $text . '</span>';
    }
}
