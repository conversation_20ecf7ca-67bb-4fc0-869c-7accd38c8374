

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-plus-circle"></i>
        <span>Tambah Jenis Produk Baru</span>
    </div>

    <!-- Info Panel -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> Informasi Tambah Produk</h5>
                <p><strong>Halaman ini untuk menambahkan JENIS PRODUK BARU ke katalog.</strong></p>
                <ul class="mb-0">
                    <li>Gunakan untuk membuat template produk baru (contoh: "Ubi Bakar Keju", "Ubi Bakar Coklat")</li>
                    <li>Stok awal bisa diisi 0, nanti akan bertambah melalui <strong>"Proses Produksi"</strong></li>
                    <li>Untuk memproduksi produk yang sudah ada, gunakan menu <strong>"Proses Produksi"</strong></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-plus-circle me-2"></i>Form Tambah Jenis Produk Baru</span>
                    <a href="<?php echo e(route('processed-inventory.index')); ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('processed-inventory.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        
                        <!-- Informasi Dasar Produk -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-tag me-2"></i>Informasi Dasar Produk</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Nama Produk <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="name" name="name" value="<?php echo e(old('name')); ?>" placeholder="Contoh: Ubi Bakar Keju" required>
                                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <small class="form-text text-muted">Nama unik untuk jenis produk ini</small>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="product_type" class="form-label">Jenis Produk <span class="text-danger">*</span></label>
                                        <select class="form-select <?php $__errorArgs = ['product_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="product_type" name="product_type" required>
                                            <option value="">-- Pilih Jenis Produk --</option>
                                            <option value="Original" <?php echo e(old('product_type') == 'Original' ? 'selected' : ''); ?>>Original (7 hari)</option>
                                            <option value="Premium" <?php echo e(old('product_type') == 'Premium' ? 'selected' : ''); ?>>Premium (5 hari)</option>
                                            <option value="Special" <?php echo e(old('product_type') == 'Special' ? 'selected' : ''); ?>>Special (3 hari)</option>
                                        </select>
                                        <?php $__errorArgs = ['product_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <small class="form-text text-muted">Menentukan masa simpan produk</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Pengaturan Harga dan Stok -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-dollar-sign me-2"></i>Pengaturan Harga & Stok</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="selling_price" class="form-label">Harga Jual per Item (Rp) <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control <?php $__errorArgs = ['selling_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="selling_price" name="selling_price" value="<?php echo e(old('selling_price', 0)); ?>" min="0" required>
                                        <?php $__errorArgs = ['selling_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <small class="form-text text-muted">Harga jual standar untuk produk ini</small>
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="current_stock" class="form-label">Stok Awal</label>
                                        <input type="number" class="form-control <?php $__errorArgs = ['current_stock'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="current_stock" name="current_stock" value="<?php echo e(old('current_stock', 0)); ?>" min="0">
                                        <?php $__errorArgs = ['current_stock'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <small class="form-text text-muted">Bisa diisi 0, stok akan bertambah saat produksi</small>
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="min_stock_threshold" class="form-label">Batas Minimum Stok</label>
                                        <input type="number" class="form-control <?php $__errorArgs = ['min_stock_threshold'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="min_stock_threshold" name="min_stock_threshold" value="<?php echo e(old('min_stock_threshold', 5)); ?>" min="0">
                                        <?php $__errorArgs = ['min_stock_threshold'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <small class="form-text text-muted">Peringatan saat stok di bawah nilai ini</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Spesifikasi Produksi -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Spesifikasi Produksi</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="raw_material_per_item" class="form-label">Kebutuhan Ubi Mentah per Item (kg) <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control <?php $__errorArgs = ['raw_material_per_item'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="raw_material_per_item" name="raw_material_per_item" value="<?php echo e(old('raw_material_per_item', 0.2)); ?>" step="0.01" min="0.01" required>
                                        <?php $__errorArgs = ['raw_material_per_item'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <small class="form-text text-muted">Berapa kg ubi mentah untuk membuat 1 item produk ini</small>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="estimated_cost_per_item" class="form-label">Estimasi Biaya Produksi per Item (Rp)</label>
                                        <input type="number" class="form-control" id="estimated_cost_per_item" name="cost_per_item" value="<?php echo e(old('cost_per_item', 0)); ?>" min="0" readonly>
                                        <small class="form-text text-muted">Akan dihitung otomatis saat produksi</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Informasi Tambahan -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informasi Tambahan</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="notes" class="form-label">Deskripsi Produk</label>
                                        <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="notes" name="notes" rows="3" placeholder="Deskripsi produk, bahan tambahan, cara penyajian, dll..."><?php echo e(old('notes')); ?></textarea>
                                        <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="is_active" class="form-label">Status Produk</label>
                                        <div class="form-check form-switch mt-2">
                                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" <?php echo e(old('is_active', '1') ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="is_active">
                                                Produk Aktif
                                            </label>
                                        </div>
                                        <small class="form-text text-muted">Produk aktif akan muncul dalam menu produksi</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const productTypeSelect = document.getElementById('product_type');
    const sellingPriceInput = document.getElementById('selling_price');
    const estimatedCostInput = document.getElementById('estimated_cost_per_item');
    const rawMaterialInput = document.getElementById('raw_material_per_item');

    // Show estimated margin based on selling price
    function calculateEstimatedMargin() {
        const selling = parseFloat(sellingPriceInput.value) || 0;
        const estimatedCost = parseFloat(estimatedCostInput.value) || 0;

        if (selling > 0) {
            // Create or update margin display
            let marginDisplay = document.getElementById('margin-display');
            if (!marginDisplay) {
                marginDisplay = document.createElement('small');
                marginDisplay.id = 'margin-display';
                marginDisplay.className = 'form-text text-info mt-1';
                sellingPriceInput.parentNode.appendChild(marginDisplay);
            }

            if (estimatedCost > 0) {
                const margin = ((selling - estimatedCost) / selling * 100).toFixed(1);
                marginDisplay.innerHTML = `<i class="fas fa-chart-line"></i> Estimasi Margin: ${margin}%`;

                // Color coding for margin
                if (margin < 20) {
                    marginDisplay.className = 'form-text text-danger mt-1';
                } else if (margin < 40) {
                    marginDisplay.className = 'form-text text-warning mt-1';
                } else {
                    marginDisplay.className = 'form-text text-success mt-1';
                }
            } else {
                marginDisplay.innerHTML = `<i class="fas fa-info-circle"></i> Margin akan dihitung saat produksi`;
                marginDisplay.className = 'form-text text-muted mt-1';
            }
        }
    }

    // Show shelf life info based on product type
    function showShelfLifeInfo() {
        const productType = productTypeSelect.value;

        let shelfLifeDisplay = document.getElementById('shelf-life-display');
        if (!shelfLifeDisplay) {
            shelfLifeDisplay = document.createElement('small');
            shelfLifeDisplay.id = 'shelf-life-display';
            shelfLifeDisplay.className = 'form-text text-info mt-1';
            productTypeSelect.parentNode.appendChild(shelfLifeDisplay);
        }

        switch(productType) {
            case 'Original':
                shelfLifeDisplay.innerHTML = '<i class="fas fa-clock"></i> Masa simpan: 7 hari';
                break;
            case 'Premium':
                shelfLifeDisplay.innerHTML = '<i class="fas fa-clock"></i> Masa simpan: 5 hari (dengan topping)';
                break;
            case 'Special':
                shelfLifeDisplay.innerHTML = '<i class="fas fa-clock"></i> Masa simpan: 3 hari (dengan dairy/cream)';
                break;
            default:
                shelfLifeDisplay.innerHTML = '';
        }
    }

    // Validate raw material requirement
    function validateRawMaterial() {
        const rawMaterial = parseFloat(rawMaterialInput.value) || 0;

        let validationDisplay = document.getElementById('raw-material-validation');
        if (!validationDisplay) {
            validationDisplay = document.createElement('small');
            validationDisplay.id = 'raw-material-validation';
            rawMaterialInput.parentNode.appendChild(validationDisplay);
        }

        if (rawMaterial > 0) {
            if (rawMaterial < 0.1) {
                validationDisplay.innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i> Nilai terlalu kecil, minimal 0.1 kg';
                validationDisplay.className = 'form-text text-warning mt-1';
            } else if (rawMaterial > 1) {
                validationDisplay.innerHTML = '<i class="fas fa-info-circle text-info"></i> Nilai cukup besar, pastikan sudah benar';
                validationDisplay.className = 'form-text text-info mt-1';
            } else {
                validationDisplay.innerHTML = '<i class="fas fa-check text-success"></i> Nilai wajar untuk 1 item ubi bakar';
                validationDisplay.className = 'form-text text-success mt-1';
            }
        } else {
            validationDisplay.innerHTML = '';
        }
    }

    // Event listeners
    productTypeSelect.addEventListener('change', showShelfLifeInfo);
    sellingPriceInput.addEventListener('input', calculateEstimatedMargin);
    estimatedCostInput.addEventListener('input', calculateEstimatedMargin);
    rawMaterialInput.addEventListener('input', validateRawMaterial);

    // Initial calculations
    showShelfLifeInfo();
    calculateEstimatedMargin();
    validateRawMaterial();
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/inventory/processed/create.blade.php ENDPATH**/ ?>