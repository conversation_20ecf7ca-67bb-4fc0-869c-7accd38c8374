<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AuditLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\View\View;

class AuditLogController extends Controller
{
    /**
     * Menampilkan daftar audit logs.
     */
    public function index(Request $request): View
    {
        // Ambil parameter filter
        $action = $request->input('action');
        $modelType = $request->input('model_type');
        $userId = $request->input('user_id');
        $dateFrom = $request->input('date_from');
        $dateTo = $request->input('date_to');

        // Query dasar
        $query = AuditLog::with('user')->latest();

        // Terapkan filter
        if ($action) {
            $query->where('action', $action);
        }

        if ($modelType) {
            $query->where('model_type', $modelType);
        }

        if ($userId) {
            $query->where('user_id', $userId);
        }

        if ($dateFrom) {
            $query->whereDate('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->whereDate('created_at', '<=', $dateTo);
        }

        // Ambil data dengan pagination
        $auditLogs = $query->paginate(20);

        // Ambil daftar pengguna untuk dropdown filter
        $users = User::all();

        // Ambil daftar model types yang ada di audit logs
        $modelTypes = AuditLog::distinct('model_type')->pluck('model_type');

        // Ambil daftar actions yang ada di audit logs
        $actions = AuditLog::distinct('action')->pluck('action');

        // Statistics for dashboard
        $stats = [
            'total_logs' => AuditLog::count(),
            'today_logs' => AuditLog::whereDate('created_at', today())->count(),
            'this_week_logs' => AuditLog::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month_logs' => AuditLog::whereMonth('created_at', now()->month)->count(),
        ];

        // Recent activity summary
        $recentActivity = AuditLog::with('user')
            ->latest()
            ->limit(5)
            ->get();

        // Most active users (last 30 days)
        $activeUsers = AuditLog::with('user')
            ->where('created_at', '>=', now()->subDays(30))
            ->whereNotNull('user_id')
            ->selectRaw('user_id, COUNT(*) as activity_count')
            ->groupBy('user_id')
            ->orderBy('activity_count', 'desc')
            ->limit(5)
            ->get();

        return view('admin.audit-logs.index', compact(
            'auditLogs',
            'users',
            'modelTypes',
            'actions',
            'action',
            'modelType',
            'userId',
            'dateFrom',
            'dateTo',
            'stats',
            'recentActivity',
            'activeUsers'
        ));
    }

    /**
     * Menampilkan detail audit log.
     */
    public function show(AuditLog $auditLog): View
    {
        return view('admin.audit-logs.show', compact('auditLog'));
    }

    /**
     * Export audit logs to CSV.
     */
    public function export(Request $request)
    {
        // Apply same filters as index
        $query = AuditLog::with('user')->latest();

        if ($request->input('action')) {
            $query->where('action', $request->input('action'));
        }

        if ($request->input('model_type')) {
            $query->where('model_type', $request->input('model_type'));
        }

        if ($request->input('user_id')) {
            $query->where('user_id', $request->input('user_id'));
        }

        if ($request->input('date_from')) {
            $query->whereDate('created_at', '>=', $request->input('date_from'));
        }

        if ($request->input('date_to')) {
            $query->whereDate('created_at', '<=', $request->input('date_to'));
        }

        $auditLogs = $query->get();

        $filename = 'audit_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($auditLogs) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID',
                'Tanggal',
                'Pengguna',
                'Aksi',
                'Model',
                'Model ID',
                'IP Address',
                'User Agent'
            ]);

            foreach ($auditLogs as $log) {
                fputcsv($file, [
                    $log->id,
                    $log->created_at->format('Y-m-d H:i:s'),
                    $log->user->name ?? 'System',
                    ucfirst($log->action),
                    class_basename($log->model_type),
                    $log->model_id,
                    $log->ip_address,
                    $log->user_agent
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
