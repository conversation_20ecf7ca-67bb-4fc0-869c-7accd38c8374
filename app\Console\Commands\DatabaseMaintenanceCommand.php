<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class DatabaseMaintenanceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:maintenance 
                            {--force : Force maintenance even during business hours}
                            {--optimize : Run table optimization}
                            {--clear-locks : Clear database locks}
                            {--analyze : Analyze tables for better performance}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Perform automated database maintenance to prevent locks and optimize performance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Starting automated database maintenance...');
        $this->newLine();

        // Check if maintenance should run
        if (!$this->shouldRunMaintenance()) {
            $this->warn('⏰ Maintenance skipped - running during business hours. Use --force to override.');
            return;
        }

        $maintenanceResults = [
            'started_at' => now(),
            'operations' => []
        ];

        try {
            // 1. Clear database locks
            if ($this->option('clear-locks') || !$this->hasOptions()) {
                $this->info('🔒 Clearing database locks...');
                $lockResults = $this->clearDatabaseLocks();
                $maintenanceResults['operations']['clear_locks'] = $lockResults;
                $this->displayResults('Lock Clearing', $lockResults);
            }

            // 2. Optimize tables
            if ($this->option('optimize') || !$this->hasOptions()) {
                $this->info('⚡ Optimizing database tables...');
                $optimizeResults = $this->optimizeTables();
                $maintenanceResults['operations']['optimize'] = $optimizeResults;
                $this->displayResults('Table Optimization', $optimizeResults);
            }

            // 3. Analyze tables
            if ($this->option('analyze') || !$this->hasOptions()) {
                $this->info('📊 Analyzing tables for better performance...');
                $analyzeResults = $this->analyzeTables();
                $maintenanceResults['operations']['analyze'] = $analyzeResults;
                $this->displayResults('Table Analysis', $analyzeResults);
            }

            // 4. Update database settings
            $this->info('⚙️  Updating database settings...');
            $settingsResults = $this->updateDatabaseSettings();
            $maintenanceResults['operations']['settings'] = $settingsResults;
            $this->displayResults('Settings Update', $settingsResults);

            // 5. Clean up old data
            $this->info('🧹 Cleaning up old data...');
            $cleanupResults = $this->cleanupOldData();
            $maintenanceResults['operations']['cleanup'] = $cleanupResults;
            $this->displayResults('Data Cleanup', $cleanupResults);

            $maintenanceResults['completed_at'] = now();
            $maintenanceResults['duration'] = $maintenanceResults['completed_at']->diffInSeconds($maintenanceResults['started_at']);
            $maintenanceResults['status'] = 'success';

            // Store maintenance log
            $this->storeMaintenance($maintenanceResults);

            $this->newLine();
            $this->info('✅ Database maintenance completed successfully!');
            $this->info("⏱️  Total duration: {$maintenanceResults['duration']} seconds");

        } catch (\Exception $e) {
            $maintenanceResults['error'] = $e->getMessage();
            $maintenanceResults['status'] = 'failed';
            $maintenanceResults['completed_at'] = now();

            $this->storeMaintenance($maintenanceResults);

            $this->error('❌ Database maintenance failed: ' . $e->getMessage());
            Log::error('Database maintenance failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Check if maintenance should run
     */
    private function shouldRunMaintenance(): bool
    {
        if ($this->option('force')) {
            return true;
        }

        // Only run during off-business hours (22:00 - 06:00)
        $currentHour = now()->hour;
        return $currentHour >= 22 || $currentHour <= 6;
    }

    /**
     * Check if any options are specified
     */
    private function hasOptions(): bool
    {
        return $this->option('optimize') || 
               $this->option('clear-locks') || 
               $this->option('analyze');
    }

    /**
     * Clear database locks
     */
    private function clearDatabaseLocks(): array
    {
        $results = [
            'killed_processes' => 0,
            'cleared_caches' => false,
            'flushed_tables' => false,
            'errors' => []
        ];

        try {
            // Kill long-running processes
            $processes = DB::select('SHOW PROCESSLIST');
            
            foreach ($processes as $process) {
                if ($process->Time > 60 && $process->Command !== 'Sleep') {
                    try {
                        DB::statement("KILL {$process->Id}");
                        $results['killed_processes']++;
                    } catch (\Exception $e) {
                        $results['errors'][] = "Failed to kill process {$process->Id}: " . $e->getMessage();
                    }
                }
            }

            // Clear query cache
            try {
                DB::statement('RESET QUERY CACHE');
                $results['cleared_caches'] = true;
            } catch (\Exception $e) {
                $results['errors'][] = 'Failed to clear query cache: ' . $e->getMessage();
            }

            // Flush tables
            try {
                DB::statement('FLUSH TABLES');
                $results['flushed_tables'] = true;
            } catch (\Exception $e) {
                $results['errors'][] = 'Failed to flush tables: ' . $e->getMessage();
            }

        } catch (\Exception $e) {
            $results['errors'][] = 'General error: ' . $e->getMessage();
        }

        return $results;
    }

    /**
     * Optimize database tables
     */
    private function optimizeTables(): array
    {
        $tables = [
            'transactions',
            'transaction_items', 
            'processed_inventory',
            'other_products',
            'audit_logs',
            'users',
            'raw_inventory'
        ];

        $results = [
            'optimized' => [],
            'failed' => [],
            'errors' => []
        ];

        foreach ($tables as $table) {
            try {
                DB::statement("OPTIMIZE TABLE {$table}");
                $results['optimized'][] = $table;
                $this->line("  ✅ Optimized: {$table}");
            } catch (\Exception $e) {
                $results['failed'][] = $table;
                $results['errors'][] = "Failed to optimize {$table}: " . $e->getMessage();
                $this->line("  ❌ Failed: {$table}");
            }
        }

        return $results;
    }

    /**
     * Analyze database tables
     */
    private function analyzeTables(): array
    {
        $tables = [
            'transactions',
            'transaction_items',
            'processed_inventory', 
            'other_products',
            'audit_logs',
            'users',
            'raw_inventory'
        ];

        $results = [
            'analyzed' => [],
            'failed' => [],
            'errors' => []
        ];

        foreach ($tables as $table) {
            try {
                DB::statement("ANALYZE TABLE {$table}");
                $results['analyzed'][] = $table;
                $this->line("  ✅ Analyzed: {$table}");
            } catch (\Exception $e) {
                $results['failed'][] = $table;
                $results['errors'][] = "Failed to analyze {$table}: " . $e->getMessage();
                $this->line("  ❌ Failed: {$table}");
            }
        }

        return $results;
    }

    /**
     * Update database settings for optimal performance
     */
    private function updateDatabaseSettings(): array
    {
        $settings = [
            'innodb_lock_wait_timeout' => 5,
            'lock_wait_timeout' => 5,
            'autocommit' => 1,
            'transaction_isolation' => 'READ-COMMITTED',
            'innodb_deadlock_detect' => 'ON'
        ];

        $results = [
            'updated' => [],
            'failed' => [],
            'errors' => []
        ];

        foreach ($settings as $setting => $value) {
            try {
                if (is_string($value)) {
                    DB::statement("SET GLOBAL {$setting} = '{$value}'");
                } else {
                    DB::statement("SET GLOBAL {$setting} = {$value}");
                }
                $results['updated'][] = "{$setting} = {$value}";
                $this->line("  ✅ Updated: {$setting} = {$value}");
            } catch (\Exception $e) {
                $results['failed'][] = $setting;
                $results['errors'][] = "Failed to update {$setting}: " . $e->getMessage();
                $this->line("  ❌ Failed: {$setting}");
            }
        }

        return $results;
    }

    /**
     * Clean up old data to improve performance
     */
    private function cleanupOldData(): array
    {
        $results = [
            'cleaned' => [],
            'errors' => []
        ];

        try {
            // Clean up old audit logs (older than 90 days)
            $deletedAuditLogs = DB::table('audit_logs')
                ->where('created_at', '<', now()->subDays(90))
                ->delete();
            
            if ($deletedAuditLogs > 0) {
                $results['cleaned'][] = "Deleted {$deletedAuditLogs} old audit logs";
                $this->line("  ✅ Cleaned: {$deletedAuditLogs} old audit logs");
            }

            // Clean up old soft-deleted transactions (older than 1 year)
            $deletedTransactions = DB::table('transactions')
                ->whereNotNull('deleted_at')
                ->where('deleted_at', '<', now()->subYear())
                ->delete();
            
            if ($deletedTransactions > 0) {
                $results['cleaned'][] = "Permanently deleted {$deletedTransactions} old transactions";
                $this->line("  ✅ Cleaned: {$deletedTransactions} old transactions");
            }

        } catch (\Exception $e) {
            $results['errors'][] = 'Cleanup error: ' . $e->getMessage();
        }

        return $results;
    }

    /**
     * Display operation results
     */
    private function displayResults(string $operation, array $results): void
    {
        $this->line("  📋 {$operation} Results:");
        
        if (!empty($results['errors'])) {
            foreach ($results['errors'] as $error) {
                $this->line("    ❌ {$error}");
            }
        }
        
        if (empty($results['errors']) && !empty($results)) {
            $this->line("    ✅ Operation completed successfully");
        }
        
        $this->newLine();
    }

    /**
     * Store maintenance log
     */
    private function storeMaintenance(array $results): void
    {
        try {
            // Store in cache for dashboard
            Cache::put('last_database_maintenance', $results, now()->addDays(7));
            
            // Log to Laravel log
            Log::info('Database maintenance completed', $results);
            
        } catch (\Exception $e) {
            Log::error('Failed to store maintenance log', [
                'error' => $e->getMessage(),
                'results' => $results
            ]);
        }
    }
}
