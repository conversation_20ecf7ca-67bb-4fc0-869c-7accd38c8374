@extends('layouts.app')

@section('title', 'Catatan Aktivitas')

@push('styles')
<style>
    .custom-pagination .page-item .page-link {
        padding: 0.5rem 1rem;
        border-radius: 4px;
        margin: 0 3px;
        color: #8B4513;
        background-color: #fff;
        border-color: #ddd;
        transition: all 0.2s ease;
    }
    
    .custom-pagination .page-item.active .page-link {
        background-color: #8B4513;
        border-color: #8B4513;
        color: white;
    }
    
    .custom-pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
        border-color: #ddd;
        box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .custom-pagination .page-item.disabled .page-link {
        color: #6c757d;
        pointer-events: none;
        background-color: #fff;
    }
</style>
@endpush

@section('content')
<div class="container-fluid px-4">
    <h1 class="mt-4">Catatan Aktivitas</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item active">Catatan Aktivitas</li>
    </ol>

    <!-- Statistics Dashboard -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">Total Catatan</div>
                            <div class="text-lg fw-bold">{{ number_format($stats['total_logs']) }}</div>
                        </div>
                        <i class="fas fa-database fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">Hari Ini</div>
                            <div class="text-lg fw-bold">{{ number_format($stats['today_logs']) }}</div>
                        </div>
                        <i class="fas fa-calendar-day fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">Minggu Ini</div>
                            <div class="text-lg fw-bold">{{ number_format($stats['this_week_logs']) }}</div>
                        </div>
                        <i class="fas fa-calendar-week fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">Bulan Ini</div>
                            <div class="text-lg fw-bold">{{ number_format($stats['this_month_logs']) }}</div>
                        </div>
                        <i class="fas fa-calendar-alt fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Summary -->
    <div class="row mb-4">
        <div class="col-xl-8">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-clock me-1"></i>
                    Aktivitas Terbaru
                </div>
                <div class="card-body">
                    @if($recentActivity->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($recentActivity as $activity)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ $activity->user->name ?? 'System' }}</strong>
                                    <span class="badge bg-{{ $activity->action == 'create' ? 'success' : ($activity->action == 'update' ? 'warning' : 'danger') }} ms-2">
                                        {{ ucfirst($activity->action) }}
                                    </span>
                                    <span class="text-muted">{{ class_basename($activity->model_type) }}</span>
                                </div>
                                <small class="text-muted">{{ $activity->created_at->diffForHumans() }}</small>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-muted mb-0">Belum ada aktivitas terbaru.</p>
                    @endif
                </div>
            </div>
        </div>
        <div class="col-xl-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-users me-1"></i>
                    Pengguna Paling Aktif (30 Hari)
                </div>
                <div class="card-body">
                    @if($activeUsers->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($activeUsers as $userActivity)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ $userActivity->user->name ?? 'Unknown' }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $userActivity->user->role ?? 'N/A' }}</small>
                                </div>
                                <span class="badge bg-primary rounded-pill">{{ $userActivity->activity_count }}</span>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-muted mb-0">Belum ada data aktivitas pengguna.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Filter Catatan Aktivitas
        </div>
        <div class="card-body">
            <form action="{{ route('admin.audit-logs.index') }}" method="GET" class="row g-3">
                <div class="col-md-2">
                    <label for="action" class="form-label">Aksi</label>
                    <select class="form-select" id="action" name="action">
                        <option value="">Semua Aksi</option>
                        @foreach($actions as $act)
                            <option value="{{ $act }}" {{ $action == $act ? 'selected' : '' }}>
                                {{ ucfirst($act) }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="model_type" class="form-label">Jenis Aktivitas</label>
                    <select class="form-select" id="model_type" name="model_type">
                        <option value="">Semua Jenis</option>
                        @foreach($modelTypes as $type)
                            <option value="{{ $type }}" {{ $modelType == $type ? 'selected' : '' }}>
                                @if(class_basename($type) == 'User')
                                    Pengguna
                                @elseif(class_basename($type) == 'RawInventory')
                                    Stok Ubi Mentah
                                @elseif(class_basename($type) == 'ProcessedInventory')
                                    Stok Ubi Bakar
                                @elseif(class_basename($type) == 'OtherProduct')
                                    Produk Lainnya
                                @elseif(class_basename($type) == 'Transaction')
                                    Transaksi
                                @elseif(class_basename($type) == 'ProductionLog')
                                    Log Produksi
                                @else
                                    {{ class_basename($type) }}
                                @endif
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="user_id" class="form-label">Pengguna</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">Semua Pengguna</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}" {{ $userId == $user->id ? 'selected' : '' }}>
                                {{ $user->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="date_from" class="form-label">Dari Tanggal</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ $dateFrom }}">
                </div>

                <div class="col-md-2">
                    <label for="date_to" class="form-label">Sampai Tanggal</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ $dateTo }}">
                </div>

                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                </div>
            </form>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Daftar Aktivitas Pengguna
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Pengguna</th>
                            <th>Aksi</th>
                            <th>Deskripsi Aktivitas</th>
                            <th>Waktu</th>
                            <th>Detail</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($auditLogs as $log)
                            <tr>
                                <td>{{ $log->id }}</td>
                                <td>{{ $log->user ? $log->user->name : 'Sistem' }}</td>
                                <td>
                                    <span class="badge bg-{{ $log->action == 'create' ? 'success' : ($log->action == 'update' ? 'primary' : ($log->action == 'delete' ? 'danger' : 'warning')) }}">
                                        @if($log->action == 'create')
                                            Tambah
                                        @elseif($log->action == 'update')
                                            Ubah
                                        @elseif($log->action == 'delete')
                                            Hapus
                                        @else
                                            {{ ucfirst($log->action) }}
                                        @endif
                                    </span>
                                </td>
                                <td>
                                    @if(class_basename($log->model_type) == 'User')
                                        @if($log->action == 'create')
                                            Menambahkan pengguna baru
                                        @elseif($log->action == 'update')
                                            Mengubah data pengguna
                                        @elseif($log->action == 'delete')
                                            Menghapus pengguna
                                        @endif
                                    @elseif(class_basename($log->model_type) == 'RawInventory')
                                        @if($log->action == 'create')
                                            Menambahkan stok ubi mentah baru
                                        @elseif($log->action == 'update')
                                            Mengubah data stok ubi mentah
                                        @elseif($log->action == 'delete')
                                            Menghapus stok ubi mentah
                                        @endif
                                    @elseif(class_basename($log->model_type) == 'ProcessedInventory')
                                        @if($log->action == 'create')
                                            Menambahkan stok ubi bakar baru
                                        @elseif($log->action == 'update')
                                            Mengubah data stok ubi bakar
                                        @elseif($log->action == 'delete')
                                            Menghapus stok ubi bakar
                                        @endif
                                    @elseif(class_basename($log->model_type) == 'OtherProduct')
                                        @if($log->action == 'create')
                                            Menambahkan produk baru
                                        @elseif($log->action == 'update')
                                            Mengubah data produk
                                        @elseif($log->action == 'delete')
                                            Menghapus produk
                                        @endif
                                    @elseif(class_basename($log->model_type) == 'Transaction')
                                        @if($log->action == 'create')
                                            Membuat transaksi baru
                                        @elseif($log->action == 'update')
                                            Mengubah data transaksi
                                        @elseif($log->action == 'delete')
                                            Menghapus transaksi
                                        @endif
                                    @elseif(class_basename($log->model_type) == 'ProductionLog')
                                        @if($log->action == 'create')
                                            Mencatat produksi baru
                                        @elseif($log->action == 'update')
                                            Mengubah data produksi
                                        @elseif($log->action == 'delete')
                                            Menghapus catatan produksi
                                        @endif
                                    @else
                                        {{ class_basename($log->model_type) }} - {{ $log->action }}
                                    @endif
                                </td>
                                <td>{{ $log->created_at->format('d M Y H:i:s') }}</td>
                                <td>
                                    <a href="{{ route('admin.audit-logs.show', $log) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> Detail
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center">Tidak ada data aktivitas</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center mt-4">
                <div class="pagination-wrapper">
                    <nav>
                        <ul class="pagination custom-pagination">
                            <!-- Previous Page Link -->
                            @if ($auditLogs->onFirstPage())
                                <li class="page-item disabled">
                                    <span class="page-link"><i class="fas fa-chevron-left me-1"></i> Previous</span>
                                </li>
                            @else
                                <li class="page-item">
                                    <a class="page-link" href="{{ $auditLogs->previousPageUrl() }}" rel="prev"><i class="fas fa-chevron-left me-1"></i> Previous</a>
                                </li>
                            @endif

                            <!-- Pagination Elements -->
                            @foreach ($auditLogs->links()->elements as $element)
                                <!-- "Three Dots" Separator -->
                                @if (is_string($element))
                                    <li class="page-item disabled"><span class="page-link">{{ $element }}</span></li>
                                @endif

                                <!-- Array Of Links -->
                                @if (is_array($element))
                                    @foreach ($element as $page => $url)
                                        @if ($page == $auditLogs->currentPage())
                                            <li class="page-item active"><span class="page-link">{{ $page }}</span></li>
                                        @else
                                            <li class="page-item"><a class="page-link" href="{{ $url }}">{{ $page }}</a></li>
                                        @endif
                                    @endforeach
                                @endif
                            @endforeach

                            <!-- Next Page Link -->
                            @if ($auditLogs->hasMorePages())
                                <li class="page-item">
                                    <a class="page-link" href="{{ $auditLogs->nextPageUrl() }}" rel="next">Next <i class="fas fa-chevron-right ms-1"></i></a>
                                </li>
                            @else
                                <li class="page-item disabled">
                                    <span class="page-link">Next <i class="fas fa-chevron-right ms-1"></i></span>
                                </li>
                            @endif
                        </ul>
                    </nav>
                </div>
            </div>
            
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="pagination-info small text-muted">
                    Showing {{ $auditLogs->firstItem() ?? 0 }} to {{ $auditLogs->lastItem() ?? 0 }} of {{ $auditLogs->total() }} results
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
