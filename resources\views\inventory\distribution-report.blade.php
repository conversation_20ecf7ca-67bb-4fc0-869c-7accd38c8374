@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-truck"></i>
        <span>Laporan Distribusi Ubi <PERSON></span>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>Filter Laporan</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('expiry-recommendations.distribution-report') }}" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Tanggal Mulai</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ $startDate }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $endDate }}">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search"></i> Filter
                            </button>
                            <a href="{{ route('expiry-recommendations.distribution-report') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-sync"></i> Reset
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon primary">
                    <i class="fas fa-truck-loading"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Total Distribusi</div>
                    <h3 class="stats-value">{{ $totalDistributions }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon warning">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Total Item</div>
                    <h3 class="stats-value">{{ $totalItems }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon success">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Total Nilai</div>
                    <h3 class="stats-value">Rp {{ number_format($totalValue, 0, ',', '.') }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon info">
                    <i class="fas fa-store"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Pasar Terlayani</div>
                    <h3 class="stats-value">{{ $marketPerformance->count() }}</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Market Performance -->
    @if($marketPerformance->count() > 0)
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>Performa Pasar</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Nama Pasar</th>
                                    <th>Jumlah Distribusi</th>
                                    <th>Total Item</th>
                                    <th>Total Nilai</th>
                                    <th>Rata-rata per Distribusi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($marketPerformance as $market)
                                <tr>
                                    <td>
                                        <strong>{{ $market['market'] }}</strong>
                                    </td>
                                    <td>{{ $market['distributions'] }}</td>
                                    <td>{{ $market['total_items'] }}</td>
                                    <td>Rp {{ number_format($market['total_value'], 0, ',', '.') }}</td>
                                    <td>Rp {{ number_format($market['distributions'] > 0 ? $market['total_value'] / $market['distributions'] : 0, 0, ',', '.') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Distribution Details -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5>Detail Distribusi Ubi Hampir Kadaluarsa</h5>
            <div>
                <button class="btn btn-success btn-sm" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> Export Excel
                </button>
                <button class="btn btn-danger btn-sm" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf"></i> Export PDF
                </button>
            </div>
        </div>
        <div class="card-body">
            @if($distributions->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>No. Distribusi</th>
                                <th>Tanggal</th>
                                <th>Tujuan</th>
                                <th>Produk</th>
                                <th>Jumlah</th>
                                <th>Nilai</th>
                                <th>Status</th>
                                <th>Prioritas</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($distributions as $distribution)
                                @foreach($distribution->items as $item)
                                    @if($item->processedInventory && $item->processedInventory->expiry_date <= now()->addDays(7))
                                    <tr>
                                        <td>{{ $distribution->distribution_number }}</td>
                                        <td>{{ $distribution->distribution_date->format('d/m/Y') }}</td>
                                        <td>{{ $distribution->market_name }}</td>
                                        <td>
                                            <strong>{{ $item->processedInventory->name }}</strong><br>
                                            <small class="text-muted">
                                                Batch: {{ $item->processedInventory->batch_number }}<br>
                                                Kadaluarsa: {{ $item->processedInventory->expiry_date->format('d/m/Y') }}
                                            </small>
                                        </td>
                                        <td>{{ $item->quantity }}</td>
                                        <td>Rp {{ number_format($item->total_price, 0, ',', '.') }}</td>
                                        <td>
                                            <span class="badge bg-{{ $distribution->status === 'completed' ? 'success' : ($distribution->status === 'in_transit' ? 'warning' : 'info') }}">
                                                {{ ucfirst($distribution->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            @php
                                                $daysLeft = $item->processedInventory->expiry_date->diffInDays(now(), false);
                                            @endphp
                                            @if($daysLeft <= 1)
                                                <span class="badge bg-danger">URGENT</span>
                                            @elseif($daysLeft <= 3)
                                                <span class="badge bg-warning">HIGH</span>
                                            @else
                                                <span class="badge bg-info">MEDIUM</span>
                                            @endif
                                        </td>
                                    </tr>
                                    @endif
                                @endforeach
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                    <h5>Tidak ada data distribusi</h5>
                    <p class="text-muted">Belum ada distribusi ubi hampir kadaluarsa pada periode ini.</p>
                </div>
            @endif
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <a href="{{ route('expiry-recommendations.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali ke Daftar Ubi Hampir Kadaluarsa
            </a>
        </div>
    </div>
</div>

<script>
function exportToExcel() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '{{ route("expiry-recommendations.distribution-report") }}?' + params.toString();
}

function exportToPDF() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.location.href = '{{ route("expiry-recommendations.distribution-report") }}?' + params.toString();
}
</script>

<style>
.page-title {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: bold;
    color: #495057;
}

.page-title i {
    margin-right: 15px;
    color: #fd7e14;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    height: 100%;
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 24px;
    color: white;
}

.stats-icon.primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stats-icon.success { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stats-icon.info { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.stats-icon.warning { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }

.stats-title {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 5px;
}

.stats-value {
    font-size: 24px;
    font-weight: bold;
    color: #495057;
    margin: 0;
}

.card {
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 10px;
}

.card-header {
    background: linear-gradient(135deg, #fd7e14 0%, #e55a4e 100%);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    border: none;
}
</style>
@endsection
