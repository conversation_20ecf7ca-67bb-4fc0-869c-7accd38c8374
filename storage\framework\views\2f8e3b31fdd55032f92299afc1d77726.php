<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-users"></i>
        <span>Manajemen User</span>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon primary">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Total User</div>
                    <h3 class="stats-value"><?php echo e($roleStats['total']); ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon success">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Admin</div>
                    <h3 class="stats-value"><?php echo e($roleStats['admin']); ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon info">
                    <i class="fas fa-user-tie"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Karyawan</div>
                    <h3 class="stats-value"><?php echo e($roleStats['employee']); ?></h3>
                </div>
            </div>
        </div>

        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon success">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Aktif</div>
                    <h3 class="stats-value"><?php echo e($roleStats['active']); ?></h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter and Search -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>Filter & Pencarian</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('users.index')); ?>" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Pencarian</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo e(request('search')); ?>" placeholder="Nama atau email...">
                        </div>
                        <div class="col-md-3">
                            <label for="role" class="form-label">Role</label>
                            <select class="form-select" id="role" name="role">
                                <option value="">Semua Role</option>
                                <option value="admin" <?php echo e(request('role') == 'admin' ? 'selected' : ''); ?>>Admin</option>
                                <option value="employee" <?php echo e(request('role') == 'employee' ? 'selected' : ''); ?>>Karyawan</option>
                                <option value="cashier" <?php echo e(request('role') == 'cashier' ? 'selected' : ''); ?>>Kasir</option>
                                <option value="warehouse" <?php echo e(request('role') == 'warehouse' ? 'selected' : ''); ?>>Gudang</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?php echo e(request('status', 'active') == 'active' ? 'selected' : ''); ?>>Aktif</option>
                                <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Tidak Aktif</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search"></i> Filter
                            </button>
                            <a href="<?php echo e(route('users.index')); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-sync"></i> Reset
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5>Daftar User</h5>
            <a href="<?php echo e(route('users.create')); ?>" class="btn btn-success">
                <i class="fas fa-plus"></i> Tambah User
            </a>
        </div>
        <div class="card-body">
            <?php if($users->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nama</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Terakhir Aktif</th>
                                <th>Dibuat</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($user->id); ?></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <div class="avatar-title bg-primary rounded-circle">
                                                    <?php echo e(strtoupper(substr($user->name, 0, 1))); ?>

                                                </div>
                                            </div>
                                            <div>
                                                <strong><?php echo e($user->name); ?></strong>
                                                <?php if($user->id === auth()->id()): ?>
                                                    <span class="badge bg-info ms-1">Anda</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo e($user->email); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo e($user->role === 'admin' ? 'danger' : ($user->role === 'employee' ? 'primary' : ($user->role === 'cashier' ? 'warning' : 'secondary'))); ?>">
                                            <?php echo e($user->role_display); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php if($user->deleted_at): ?>
                                            <span class="badge bg-danger">Tidak Aktif</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">Aktif</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($user->last_activity): ?>
                                            <?php echo e($user->last_activity->diffForHumans()); ?>

                                        <?php else: ?>
                                            <span class="text-muted">Belum pernah login</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($user->created_at->format('d/m/Y H:i')); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('users.show', $user)); ?>" class="btn btn-sm btn-info" title="Detail">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('users.edit', $user)); ?>" class="btn btn-sm btn-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if($user->id !== auth()->id()): ?>
                                                <?php if($user->deleted_at): ?>
                                                    <form action="<?php echo e(route('users.restore', $user->id)); ?>" method="POST" class="d-inline">
                                                        <?php echo csrf_field(); ?>
                                                        <button type="submit" class="btn btn-sm btn-success" title="Pulihkan"
                                                                onclick="return confirm('Yakin ingin memulihkan user ini?')">
                                                            <i class="fas fa-undo"></i>
                                                        </button>
                                                    </form>
                                                    <form action="<?php echo e(route('users.force-delete', $user->id)); ?>" method="POST" class="d-inline">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-danger" title="Hapus Permanen"
                                                                onclick="return confirm('PERINGATAN: Ini akan menghapus user secara permanen! Yakin?')">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                <?php else: ?>
                                                    <form action="<?php echo e(route('users.destroy', $user)); ?>" method="POST" class="d-inline">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-danger" title="Hapus"
                                                                onclick="return confirm('Yakin ingin menghapus user ini?')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        Menampilkan <?php echo e($users->firstItem()); ?> - <?php echo e($users->lastItem()); ?> dari <?php echo e($users->total()); ?> user
                    </div>
                    <div>
                        <?php echo e($users->appends(request()->query())->links()); ?>

                    </div>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5>Tidak ada user ditemukan</h5>
                    <p class="text-muted">Belum ada user yang sesuai dengan filter yang dipilih.</p>
                    <a href="<?php echo e(route('users.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Tambah User Pertama
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    height: 100%;
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 24px;
    color: white;
}

.stats-icon.primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stats-icon.success { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stats-icon.info { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.stats-icon.warning { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
.stats-icon.secondary { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }

.stats-title {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 5px;
}

.stats-value {
    font-size: 24px;
    font-weight: bold;
    color: #495057;
    margin: 0;
}

.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
}

.page-title {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: bold;
    color: #495057;
}

.page-title i {
    margin-right: 15px;
    color: #6f42c1;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/admin/users/index.blade.php ENDPATH**/ ?>