@extends('layouts.app')

@section('title', 'Laporan Laba Rugi')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Laporan Laba Rugi
                    </h4>
                    <div class="btn-group">
                        <a href="{{ route('financial.income_statement', array_merge(request()->all(), ['export' => 'pdf'])) }}" 
                           class="btn btn-danger btn-sm">
                            <i class="fas fa-file-pdf me-1"></i> PDF
                        </a>
                        <a href="{{ route('financial.income_statement', array_merge(request()->all(), ['export' => 'excel'])) }}" 
                           class="btn btn-success btn-sm">
                            <i class="fas fa-file-excel me-1"></i> Excel
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Filter Form -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="start_date" class="form-label">Tanggal Mulai</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" 
                                       value="{{ $startDate->format('Y-m-d') }}">
                            </div>
                            <div class="col-md-4">
                                <label for="end_date" class="form-label">Tanggal Akhir</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" 
                                       value="{{ $endDate->format('Y-m-d') }}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i> Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Income Statement Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-dark">
                                <tr>
                                    <th colspan="2" class="text-center">
                                        LAPORAN LABA RUGI<br>
                                        <small>Periode: {{ $startDate->format('d M Y') }} - {{ $endDate->format('d M Y') }}</small>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Revenue Section -->
                                <tr class="table-light">
                                    <th colspan="2">PENDAPATAN</th>
                                </tr>
                                <tr>
                                    <td style="padding-left: 30px;">Penjualan</td>
                                    <td class="text-end">Rp {{ number_format($revenue, 0, ',', '.') }}</td>
                                </tr>
                                <tr class="fw-bold">
                                    <td>Total Pendapatan</td>
                                    <td class="text-end">Rp {{ number_format($revenue, 0, ',', '.') }}</td>
                                </tr>
                                
                                <!-- COGS Section -->
                                <tr class="table-light">
                                    <th colspan="2">HARGA POKOK PENJUALAN</th>
                                </tr>
                                <tr>
                                    <td style="padding-left: 30px;">Biaya Produksi</td>
                                    <td class="text-end">Rp {{ number_format($cogs, 0, ',', '.') }}</td>
                                </tr>
                                <tr class="fw-bold">
                                    <td>Total HPP</td>
                                    <td class="text-end">Rp {{ number_format($cogs, 0, ',', '.') }}</td>
                                </tr>
                                
                                <!-- Gross Profit -->
                                <tr class="fw-bold table-success">
                                    <td>LABA KOTOR</td>
                                    <td class="text-end">Rp {{ number_format($grossProfit, 0, ',', '.') }}</td>
                                </tr>
                                
                                <!-- Operating Expenses -->
                                <tr class="table-light">
                                    <th colspan="2">BIAYA OPERASIONAL</th>
                                </tr>
                                @foreach($expenses as $expense)
                                <tr>
                                    <td style="padding-left: 30px;">{{ ucfirst($expense->expense_category) }}</td>
                                    <td class="text-end">Rp {{ number_format($expense->total, 0, ',', '.') }}</td>
                                </tr>
                                @endforeach
                                <tr class="fw-bold">
                                    <td>Total Biaya Operasional</td>
                                    <td class="text-end">Rp {{ number_format($totalExpenses, 0, ',', '.') }}</td>
                                </tr>
                                
                                <!-- Net Profit -->
                                <tr class="fw-bold table-primary fs-5">
                                    <td>LABA BERSIH</td>
                                    <td class="text-end">Rp {{ number_format($netProfit, 0, ',', '.') }}</td>
                                </tr>
                                
                                <!-- Ratios -->
                                <tr class="table-light">
                                    <th colspan="2">RASIO KEUANGAN</th>
                                </tr>
                                <tr>
                                    <td style="padding-left: 30px;">Margin Laba Kotor</td>
                                    <td class="text-end">{{ $revenue > 0 ? number_format(($grossProfit / $revenue) * 100, 2) : 0 }}%</td>
                                </tr>
                                <tr>
                                    <td style="padding-left: 30px;">Margin Laba Bersih</td>
                                    <td class="text-end">{{ $revenue > 0 ? number_format(($netProfit / $revenue) * 100, 2) : 0 }}%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.table-success td {
    background-color: #d1e7dd !important;
}

.table-primary td {
    background-color: #cff4fc !important;
}

.fs-5 {
    font-size: 1.25rem !important;
}
</style>
@endpush
