<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\ProcessedInventory;
use App\Models\Expense;
use Carbon\Carbon;

class MonthlyReportTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some processed inventory items
        $products = ProcessedInventory::take(5)->get();
        
        if ($products->isEmpty()) {
            $this->command->info('No processed inventory found. Please run ProcessedInventorySeeder first.');
            return;
        }

        // Create transactions for different months
        $months = [
            Carbon::now()->subMonths(2), // 2 months ago
            Carbon::now()->subMonth(),    // Last month
            Carbon::now(),                // This month
        ];

        foreach ($months as $month) {
            // Create 5-10 transactions per month
            $transactionCount = rand(5, 10);
            
            for ($i = 0; $i < $transactionCount; $i++) {
                // Random date within the month
                $transactionDate = $month->copy()->addDays(rand(1, $month->daysInMonth - 1));
                
                $transaction = Transaction::create([
                    'invoice_number' => 'INV-' . $transactionDate->format('Ymd') . '-' . str_pad($i + 1, 4, '0', STR_PAD_LEFT),
                    'user_id' => 1, // Assuming user ID 1 exists
                    'customer_name' => 'Customer ' . ($i + 1),
                    'customer_phone' => '08' . rand(1000000000, 9999999999),
                    'subtotal' => 0, // Will be calculated
                    'tax' => 0,
                    'discount' => 0,
                    'total_amount' => 0, // Will be calculated
                    'amount_paid' => 0, // Will be calculated
                    'change_amount' => 0,
                    'payment_method' => ['cash', 'transfer', 'card'][rand(0, 2)],
                    'status' => 'completed',
                    'notes' => 'Test transaction for ' . $transactionDate->format('F Y'),
                    'created_at' => $transactionDate,
                    'updated_at' => $transactionDate,
                ]);

                $totalAmount = 0;
                
                // Add 1-3 items per transaction
                $itemCount = rand(1, 3);
                for ($j = 0; $j < $itemCount; $j++) {
                    $product = $products->random();
                    $quantity = rand(1, 5);
                    $price = $product->selling_price ?? rand(10000, 50000);
                    $subtotal = $quantity * $price;
                    
                    TransactionItem::create([
                        'transaction_id' => $transaction->id,
                        'processed_inventory_id' => $product->id,
                        'quantity' => $quantity,
                        'price_per_item' => $price,
                        'subtotal' => $subtotal,
                        'created_at' => $transactionDate,
                        'updated_at' => $transactionDate,
                    ]);
                    
                    $totalAmount += $subtotal;
                }
                
                // Update transaction total
                $transaction->update([
                    'subtotal' => $totalAmount,
                    'total_amount' => $totalAmount,
                    'amount_paid' => $totalAmount
                ]);
            }
            
            // Create some expenses for each month
            $expenseCount = rand(3, 7);
            for ($i = 0; $i < $expenseCount; $i++) {
                $expenseDate = $month->copy()->addDays(rand(1, $month->daysInMonth - 1));
                
                Expense::create([
                    'category' => ['operational', 'marketing', 'utilities', 'maintenance'][rand(0, 3)],
                    'description' => 'Test expense ' . ($i + 1) . ' for ' . $expenseDate->format('F Y'),
                    'amount' => rand(50000, 500000),
                    'expense_date' => $expenseDate,
                    'notes' => 'Monthly test expense',
                    'created_at' => $expenseDate,
                    'updated_at' => $expenseDate,
                ]);
            }
            
            $this->command->info('Created test data for ' . $month->format('F Y'));
        }
        
        $this->command->info('Monthly report test data seeded successfully!');
    }
}
