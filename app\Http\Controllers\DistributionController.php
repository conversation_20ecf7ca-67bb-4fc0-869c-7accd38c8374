<?php

namespace App\Http\Controllers;

use App\Models\Distribution;
use App\Models\DistributionItem;
use App\Models\ProcessedInventory;
use App\Models\OtherProduct;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DistributionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Distribution::with(['user', 'items.processedInventory']);

        // Filter berdasarkan pencarian
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('distribution_number', 'like', "%{$search}%")
                  ->orWhere('market_name', 'like', "%{$search}%")
                  ->orWhere('notes', 'like', "%{$search}%");
            });
        }

        // Status filter removed - all distributions are automatically successful

        // Filter berdasarkan tanggal
        if ($request->filled('date_from')) {
            $query->whereDate('distribution_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('distribution_date', '<=', $request->date_to);
        }

        $distributions = $query->orderBy('created_at', 'desc')->paginate(15);

        // Calculate statistics - simplified without status
        $stats = [
            'total' => Distribution::count(),
            'this_month' => Distribution::whereMonth('distribution_date', now()->month)->count(),
            'today' => Distribution::whereDate('distribution_date', today())->count(),
        ];

        return view('distributions.index', compact('distributions', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $processedInventory = ProcessedInventory::where('current_stock', '>', 0)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('distributions.create', compact('processedInventory'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'destination' => 'required|string|max:255',
            'distribution_date' => 'required|date',
            'vehicle_info' => 'nullable|string|max:255',
            'driver_name' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.processed_inventory_id' => 'required|exists:processed_inventory,id',
            'items.*.quantity' => 'required|integer|min:1',
        ]);
        
        DB::beginTransaction();
        
        try {
            // Create distribution record
            $distribution = Distribution::create([
                'distribution_number' => 'DIST-' . date('YmdHis'),
                'user_id' => Auth::id(),
                'destination' => $validated['destination'],
                'distribution_date' => $validated['distribution_date'],
                'vehicle_info' => $validated['vehicle_info'],
                'driver_name' => $validated['driver_name'],
                'notes' => $validated['notes']
                // Status removed - all distributions are automatically successful
            ]);
            
            // Process items
            foreach ($validated['items'] as $item) {
                $product = ProcessedInventory::findOrFail($item['processed_inventory_id']);

                // Check if enough stock
                if ($product->current_stock < $item['quantity']) {
                    throw new \Exception("Stok tidak mencukupi untuk produk: {$product->name}");
                }

                // Create distribution item
                DistributionItem::create([
                    'distribution_id' => $distribution->id,
                    'processed_inventory_id' => $product->id,
                    'quantity' => $item['quantity'],
                    'price_per_item' => $product->selling_price,
                    'total_price' => $item['quantity'] * $product->selling_price
                ]);

                // Update stock
                $product->current_stock -= $item['quantity'];
                $product->save();
            }
            
            DB::commit();
            
            return redirect()->route('distributions.index')
                ->with('success', 'Distribusi berhasil dicatat');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Terjadi kesalahan: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Distribution $distribution)
    {
        $distribution->load(['user', 'items.processedInventory', 'items.otherProduct']);

        return view('distributions.show', compact('distribution'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Distribution $distribution)
    {
        // All distributions can be edited since status is removed

        $processedInventory = ProcessedInventory::where('current_stock', '>', 0)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('distributions.edit', compact('distribution', 'processedInventory'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Distribution $distribution)
    {
        // All distributions can be updated since status is removed

        $validated = $request->validate([
            'destination' => 'required|string|max:255',
            'distribution_date' => 'required|date',
            'vehicle_info' => 'nullable|string|max:255',
            'driver_name' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:processed_inventory,id',
            'items.*.quantity' => 'required|integer|min:1',
        ]);

        try {
            DB::beginTransaction();

            // Update distribution
            $distribution->update([
                'market_name' => $validated['destination'],
                'distribution_date' => $validated['distribution_date'],
                'vehicle_info' => $validated['vehicle_info'] ?? null,
                'driver_name' => $validated['driver_name'] ?? null,
                'notes' => $validated['notes'] ?? null,
            ]);

            // Delete existing items
            $distribution->items()->delete();

            // Add new items
            foreach ($validated['items'] as $item) {
                $product = ProcessedInventory::findOrFail($item['product_id']);

                DistributionItem::create([
                    'distribution_id' => $distribution->id,
                    'processed_inventory_id' => $product->id,
                    'quantity' => $item['quantity'],
                    'price_per_item' => $product->selling_price,
                    'total_price' => $item['quantity'] * $product->selling_price,
                ]);
            }

            DB::commit();

            return redirect()->route('distributions.index')
                ->with('success', 'Distribusi berhasil diupdate!');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Gagal mengupdate distribusi: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Distribution $distribution)
    {
        // All distributions can be deleted since status is removed

        try {
            DB::beginTransaction();

            // Delete distribution items first
            $distribution->items()->delete();

            // Delete distribution
            $distribution->delete();

            DB::commit();

            return redirect()->route('distributions.index')
                ->with('success', 'Distribusi berhasil dihapus!');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('distributions.index')
                ->with('error', 'Gagal menghapus distribusi: ' . $e->getMessage());
        }
    }

    // updateStatus method removed - status functionality no longer needed

    /**
     * Generate distribution report
     */
    public function report(Request $request)
    {
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));
        
        $distributions = Distribution::whereBetween('distribution_date', [$startDate, $endDate])
            ->with(['user', 'items.processedInventory', 'items.otherProduct'])
            ->orderBy('distribution_date', 'desc')
            ->get();
            
        $totalDistributed = $distributions->count();
        $totalItems = DistributionItem::whereHas('distribution', function($query) use ($startDate, $endDate) {
                $query->whereBetween('distribution_date', [$startDate, $endDate]);
            })->sum('quantity');
            
        $totalValue = DistributionItem::whereHas('distribution', function($query) use ($startDate, $endDate) {
                $query->whereBetween('distribution_date', [$startDate, $endDate]);
            })->sum('total_price');
        
        // Get daily distribution data for chart
        $dailyData = Distribution::whereBetween('distribution_date', [$startDate, $endDate])
            ->select(DB::raw('DATE(distribution_date) as date'), DB::raw('COUNT(*) as total'))
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        $labels = $dailyData->pluck('date')->toArray();
        $data = $dailyData->pluck('total')->toArray();
        
        return view('distribution.report', compact(
            'distributions',
            'startDate',
            'endDate',
            'totalDistributed',
            'totalItems',
            'totalValue',
            'labels',
            'data'
        ));
    }

    /**
     * Show distribution detail
     */
    public function detail($id)
    {
        $distribution = Distribution::with(['items.processedInventory', 'user'])->findOrFail($id);

        return view('inventory.distribution-detail', compact('distribution'));
    }

    /**
     * Export distributions to CSV
     */
    public function export(Request $request)
    {
        $query = Distribution::with(['user', 'items.processedInventory']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('distribution_number', 'like', "%{$search}%")
                  ->orWhere('market_name', 'like', "%{$search}%")
                  ->orWhere('notes', 'like', "%{$search}%");
            });
        }

        // Status filter removed - all distributions are automatically successful

        if ($request->filled('start_date')) {
            $query->whereDate('distribution_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('distribution_date', '<=', $request->end_date);
        }

        $distributions = $query->orderBy('distribution_date', 'desc')->get();

        $filename = 'distribusi_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($distributions) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // CSV Headers
            fputcsv($file, [
                'No. Distribusi',
                'Tanggal Distribusi',
                'Tujuan (Market)',
                'Penanggung Jawab',
                'Total Item',
                'Total Quantity',
                'Kendaraan',
                'Driver',
                'Catatan',
                'Dibuat Tanggal'
            ]);

            foreach ($distributions as $distribution) {
                fputcsv($file, [
                    $distribution->distribution_number ?? 'DIST-' . $distribution->id,
                    $distribution->distribution_date->format('d/m/Y H:i'),
                    $distribution->market_name,
                    $distribution->user->name ?? 'N/A',
                    $distribution->items->count(),
                    $distribution->items->sum('quantity'),
                    $distribution->vehicle ?? '-',
                    $distribution->driver ?? '-',
                    $distribution->notes ?? '-',
                    $distribution->created_at->format('d/m/Y H:i')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }


}
