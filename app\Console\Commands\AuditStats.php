<?php

namespace App\Console\Commands;

use App\Models\AuditLog;
use App\Models\User;
use Illuminate\Console\Command;
use Carbon\Carbon;

class AuditStats extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'audit:stats {--days=30 : Number of days to analyze}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Display audit log statistics';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $startDate = Carbon::now()->subDays($days);

        $this->info("Audit Log Statistics (Last {$days} days)");
        $this->line(str_repeat('=', 50));

        // Total logs
        $totalLogs = AuditLog::where('created_at', '>=', $startDate)->count();
        $this->info("Total Audit Logs: {$totalLogs}");

        // Logs by action
        $this->line("\nLogs by Action:");
        $actionStats = AuditLog::where('created_at', '>=', $startDate)
            ->selectRaw('action, COUNT(*) as count')
            ->groupBy('action')
            ->orderBy('count', 'desc')
            ->get();

        $headers = ['Action', 'Count', 'Percentage'];
        $rows = [];

        foreach ($actionStats as $stat) {
            $percentage = $totalLogs > 0 ? round(($stat->count / $totalLogs) * 100, 2) : 0;
            $rows[] = [
                ucfirst($stat->action),
                $stat->count,
                $percentage . '%'
            ];
        }

        $this->table($headers, $rows);

        // Logs by model
        $this->line("\nTop 10 Models by Activity:");
        $modelStats = AuditLog::where('created_at', '>=', $startDate)
            ->selectRaw('model_type, COUNT(*) as count')
            ->groupBy('model_type')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        $headers = ['Model', 'Count', 'Percentage'];
        $rows = [];

        foreach ($modelStats as $stat) {
            $percentage = $totalLogs > 0 ? round(($stat->count / $totalLogs) * 100, 2) : 0;
            $modelName = class_basename($stat->model_type);
            $rows[] = [
                $modelName,
                $stat->count,
                $percentage . '%'
            ];
        }

        $this->table($headers, $rows);

        // Most active users
        $this->line("\nTop 10 Most Active Users:");
        $userStats = AuditLog::where('created_at', '>=', $startDate)
            ->whereNotNull('user_id')
            ->selectRaw('user_id, COUNT(*) as count')
            ->groupBy('user_id')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->with('user')
            ->get();

        $headers = ['User', 'Count', 'Percentage'];
        $rows = [];

        foreach ($userStats as $stat) {
            $percentage = $totalLogs > 0 ? round(($stat->count / $totalLogs) * 100, 2) : 0;
            $userName = $stat->user ? $stat->user->name : 'Unknown User';
            $rows[] = [
                $userName,
                $stat->count,
                $percentage . '%'
            ];
        }

        $this->table($headers, $rows);

        // Daily activity
        $this->line("\nDaily Activity (Last 7 days):");
        $dailyStats = AuditLog::where('created_at', '>=', Carbon::now()->subDays(7))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->get();

        $headers = ['Date', 'Count'];
        $rows = [];

        foreach ($dailyStats as $stat) {
            $rows[] = [
                Carbon::parse($stat->date)->format('Y-m-d (l)'),
                $stat->count
            ];
        }

        $this->table($headers, $rows);

        $this->line("\n" . str_repeat('=', 50));
        $this->info("Analysis completed for period: {$startDate->format('Y-m-d')} to " . Carbon::now()->format('Y-m-d'));
    }
}
