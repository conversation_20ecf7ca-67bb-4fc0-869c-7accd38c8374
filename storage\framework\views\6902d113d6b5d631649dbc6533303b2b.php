<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-boxes"></i>
        <span>Laporan Inventory</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Ringkasan Inventory</span>
                    <a href="<?php echo e(route('reports.export', ['type' => 'inventory'])); ?>" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> Export Excel
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-4">
                            <div class="stats-card">
                                <div class="stats-icon primary">
                                    <i class="fas fa-seedling"></i>
                                </div>
                                <div class="stats-info">
                                    <div class="stats-title">Total Ubi Mentah</div>
                                    <h3 class="stats-value"><?php echo e($summary['raw_count']); ?> Jenis</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="stats-card">
                                <div class="stats-icon success">
                                    <i class="fas fa-weight"></i>
                                </div>
                                <div class="stats-info">
                                    <div class="stats-title">Total Berat Ubi Mentah</div>
                                    <h3 class="stats-value"><?php echo e(number_format($summary['raw_total_stock'], 1, ',', '.')); ?> kg</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="stats-card">
                                <div class="stats-icon warning">
                                    <i class="fas fa-fire-alt"></i>
                                </div>
                                <div class="stats-info">
                                    <div class="stats-title">Total Produk Matang</div>
                                    <h3 class="stats-value"><?php echo e($summary['processed_count']); ?> Jenis</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="stats-card">
                                <div class="stats-icon danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stats-info">
                                    <div class="stats-title">Stok Menipis</div>
                                    <h3 class="stats-value"><?php echo e($summary['low_stock_count']); ?> Item</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    <span>Nilai Inventory</span>
                                </div>
                                <div class="card-body">
                                    <canvas id="inventoryValueChart" height="250"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    <span>Status Inventory</span>
                                </div>
                                <div class="card-body">
                                    <canvas id="stockStatusChart" height="250"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <ul class="nav nav-tabs" id="inventoryTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="raw-tab" data-bs-toggle="tab" data-bs-target="#raw-content" type="button" role="tab" aria-controls="raw-content" aria-selected="true">Ubi Mentah</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="processed-tab" data-bs-toggle="tab" data-bs-target="#processed-content" type="button" role="tab" aria-controls="processed-content" aria-selected="false">Ubi Matang</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="low-stock-tab" data-bs-toggle="tab" data-bs-target="#low-stock-content" type="button" role="tab" aria-controls="low-stock-content" aria-selected="false">Stok Menipis</button>
                </li>
            </ul>
            <div class="tab-content p-3 border border-top-0 rounded-bottom" id="inventoryTabsContent">
                <div class="tab-pane fade show active" id="raw-content" role="tabpanel" aria-labelledby="raw-tab">
                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Nama Ubi</th>
                                    <th>Stok Saat Ini (kg)</th>
                                    <th>Harga per kg</th>
                                    <th>Supplier</th>
                                    <th>Nilai Inventory</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $rawInventory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($index + 1); ?></td>
                                    <td><?php echo e($item->name); ?></td>
                                    <td><?php echo e($item->current_stock); ?> kg</td>
                                    <td>Rp <?php echo e(number_format($item->cost_per_kg, 0, ',', '.')); ?></td>
                                    <td><?php echo e($item->supplier ?: '-'); ?></td>
                                    <td>Rp <?php echo e(number_format($item->current_stock * $item->cost_per_kg, 0, ',', '.')); ?></td>
                                    <td>
                                        <?php if($item->current_stock <= ($item->min_stock_threshold ?? 0)): ?>
                                        <span class="status-badge danger">Stok Menipis</span>
                                        <?php else: ?>
                                        <span class="status-badge success">Tersedia</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center">Tidak ada data stok ubi mentah</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="2">Total</th>
                                    <th><?php echo e(number_format($summary['raw_total_stock'], 1, ',', '.')); ?> kg</th>
                                    <th>-</th>
                                    <th>-</th>
                                    <th>Rp <?php echo e(number_format($summary['raw_total_value'], 0, ',', '.')); ?></th>
                                    <th>-</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="tab-pane fade" id="processed-content" role="tabpanel" aria-labelledby="processed-tab">
                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Nama Produk</th>
                                    <th>Stok Saat Ini</th>
                                    <th>Harga Jual</th>
                                    <th>Biaya per Item</th>
                                    <th>Nilai Inventory</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $processedInventory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($index + 1); ?></td>
                                    <td><?php echo e($item->name); ?></td>
                                    <td><?php echo e($item->current_stock); ?> pcs</td>
                                    <td>Rp <?php echo e(number_format($item->selling_price, 0, ',', '.')); ?></td>
                                    <td>
                                        <?php if($item->cost_per_item): ?>
                                        Rp <?php echo e(number_format($item->cost_per_item, 0, ',', '.')); ?>

                                        <?php else: ?>
                                        -
                                        <?php endif; ?>
                                    </td>
                                    <td>Rp <?php echo e(number_format($item->current_stock * $item->selling_price, 0, ',', '.')); ?></td>
                                    <td>
                                        <?php if($item->current_stock <= ($item->min_stock_threshold ?? 0)): ?>
                                        <span class="status-badge danger">Stok Menipis</span>
                                        <?php else: ?>
                                        <span class="status-badge success">Tersedia</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center">Tidak ada data produk ubi matang</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="2">Total</th>
                                    <th><?php echo e($summary['processed_total_stock']); ?> pcs</th>
                                    <th>-</th>
                                    <th>-</th>
                                    <th>Rp <?php echo e(number_format($summary['processed_total_value'], 0, ',', '.')); ?></th>
                                    <th>-</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="tab-pane fade" id="low-stock-content" role="tabpanel" aria-labelledby="low-stock-tab">
                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Nama Produk</th>
                                    <th>Tipe</th>
                                    <th>Stok Saat Ini</th>
                                    <th>Batas Minimum</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $lowStock; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($index + 1); ?></td>
                                    <td><?php echo e($item->name); ?></td>
                                    <td>
                                        <?php if($item->type === 'raw'): ?>
                                        <span class="badge bg-primary">Ubi Mentah</span>
                                        <?php else: ?>
                                        <span class="badge bg-warning">Ubi Matang</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($item->type === 'raw'): ?>
                                        <?php echo e($item->current_stock); ?> kg
                                        <?php else: ?>
                                        <?php echo e($item->current_stock); ?> pcs
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($item->type === 'raw'): ?>
                                        <?php echo e($item->min_stock_threshold); ?> kg
                                        <?php else: ?>
                                        <?php echo e($item->min_stock_threshold); ?> pcs
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($item->type === 'raw'): ?>
                                        <a href="<?php echo e(route('raw-inventory.show', $item->id)); ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> Lihat
                                        </a>
                                        <?php else: ?>
                                        <a href="<?php echo e(route('processed-inventory.show', $item->id)); ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> Lihat
                                        </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="text-center">Tidak ada item yang stoknya menipis</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inventory Value Chart
        const valueChartData = <?php echo json_encode($valueChartData, 15, 512) ?>;
        const valueChart = new Chart(document.getElementById('inventoryValueChart'), {
            type: 'pie',
            data: {
                labels: valueChartData.labels,
                datasets: [{
                    data: valueChartData.data,
                    backgroundColor: [
                        'rgba(139, 69, 19, 0.7)',
                        'rgba(255, 140, 0, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return context.label + ': ' + percentage + '% (Rp ' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".") + ')';
                            }
                        }
                    }
                }
            }
        });
        
        // Stock Status Chart
        const statusChartData = <?php echo json_encode($stockStatusData, 15, 512) ?>;
        const statusChart = new Chart(document.getElementById('stockStatusChart'), {
            type: 'bar',
            data: {
                labels: statusChartData.labels,
                datasets: [{
                    label: 'Stok Tersedia',
                    data: statusChartData.available,
                    backgroundColor: 'rgba(76, 175, 80, 0.7)',
                    borderColor: 'rgba(76, 175, 80, 1)',
                    borderWidth: 1
                }, {
                    label: 'Stok Menipis',
                    data: statusChartData.low,
                    backgroundColor: 'rgba(220, 53, 69, 0.7)',
                    borderColor: 'rgba(220, 53, 69, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    x: {
                        stacked: false
                    },
                    y: {
                        stacked: false,
                        beginAtZero: true
                    }
                }
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/reports/inventory.blade.php ENDPATH**/ ?>