<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\ProcessedInventory;
use App\Models\OtherProduct;
use App\Services\MidtransService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;

class TransactionController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Filter berdasarkan tanggal jika parameter disediakan
        $query = Transaction::with('items.product', 'user')
            ->withCount('items as items_count');

        // Filter berdasarkan tanggal
        if ($request->has('start_date') && $request->start_date) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }

        if ($request->has('end_date') && $request->end_date) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // Filter berdasarkan pencarian jika ada
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhere('customer_info', 'like', "%{$search}%")
                  ->orWhere('total_amount', 'like', "%{$search}%")
                  ->orWhereHas('user', function($u) use ($search) {
                      $u->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Dapatkan transaksi dengan paginasi
        $transactions = $query->latest()->paginate(15);

        // Hitung ringkasan transaksi untuk tampilan
        $transactionQuery = clone $query;
        $totalTransactions = $transactionQuery->count();
        $totalAmount = $transactionQuery->sum('total_amount');
        $averageAmount = $totalTransactions > 0 ? $totalAmount / $totalTransactions : 0;

        $transactionSummary = [
            'count' => $totalTransactions,
            'total' => $totalAmount,
            'average' => $averageAmount
        ];

        return view('transactions.index', compact('transactions', 'transactionSummary'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Get active processed inventory products with stock
        $processedProducts = ProcessedInventory::where('current_stock', '>', 0)
                                      ->where('is_active', true)
                                      ->orderBy('name')
                                      ->get();

        // Get active other products with stock
        $otherProducts = OtherProduct::where('current_stock', '>', 0)
                                     ->where('is_active', true)
                                     ->orderBy('name')
                                     ->get();

        // Combine both product types
        $products = $processedProducts->concat($otherProducts);

        // Get available categories (using a simple grouping since we don't have a categories table)
        $productTypes = ProcessedInventory::select('product_type')
                                        ->distinct()
                                        ->whereNotNull('product_type')
                                        ->get()
                                        ->pluck('product_type');

        // Add other product categories
        $otherProductCategories = OtherProduct::select('category')
                                           ->distinct()
                                           ->whereNotNull('category')
                                           ->get()
                                           ->pluck('category');

        $combinedCategories = $productTypes->concat($otherProductCategories)->unique();

        $categories = collect($combinedCategories)->map(function($type) {
            return (object)[
                'id' => strtolower(str_replace(' ', '_', $type)),
                'name' => $type
            ];
        });

        return view('transactions.pos', compact('products', 'categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'items' => 'required',
            'payment_method' => 'required|in:cash,transfer,qris,debit,credit,gateway',
            'amount_paid' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
            'use_payment_gateway' => 'nullable|boolean',
        ]);

        // Add logging to detect duplicate submissions
        Log::info('Transaction store request received', [
            'user_id' => Auth::id(),
            'session_id' => session()->getId(),
            'request_time' => now(),
            'items_count' => is_string($request->items) ? count(json_decode($request->items, true)) : count($request->items),
            'payment_method' => $request->payment_method,
            'user_agent' => $request->userAgent(),
            'ip' => $request->ip()
        ]);

        // Check for recent duplicate transactions (within last 10 seconds)
        $recentTransaction = Transaction::where('user_id', Auth::id())
            ->where('created_at', '>', now()->subSeconds(10))
            ->orderBy('created_at', 'desc')
            ->first();

        if ($recentTransaction) {
            $items = is_string($request->items) ? json_decode($request->items, true) : $request->items;
            $requestTotal = collect($items)->sum(function($item) {
                return $item['price'] * $item['quantity'];
            });

            // If amounts match, likely duplicate
            if (abs($recentTransaction->total_amount - $requestTotal) < 100) {
                Log::warning('Potential duplicate transaction detected', [
                    'recent_transaction_id' => $recentTransaction->id,
                    'recent_total' => $recentTransaction->total_amount,
                    'request_total' => $requestTotal,
                    'time_diff' => now()->diffInSeconds($recentTransaction->created_at)
                ]);

                if ($request->wantsJson()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Transaksi sudah dibuat sebelumnya',
                        'transaction' => $recentTransaction,
                        'use_payment_gateway' => $recentTransaction->payment_method === 'gateway',
                        'is_duplicate' => true
                    ]);
                }

                return redirect()->route('transactions.show', $recentTransaction)
                               ->with('info', 'Transaksi sudah dibuat sebelumnya');
            }
        }

        // Temporarily disable audit logging for performance
        config(['audit.disabled' => true]);

        try {
            // Parse items from JSON if it's a string
            $items = $request->items;
            if (is_string($items)) {
                $items = json_decode($items, true);
            }

            if (empty($items)) {
                throw new \Exception("Tidak ada item dalam keranjang");
            }

            // Calculate subtotal and total first
            $subtotal = 0;
            $totalItems = [];

            // Pre-load all products to avoid queries inside transaction
            $processedIds = [];
            $otherProductIds = [];

            foreach ($items as $item) {
                if (!isset($item['id']) || !isset($item['quantity'])) {
                    throw new \Exception("Data item tidak valid. Item harus memiliki ID dan quantity.");
                }

                $productType = $item['type'] ?? 'processed';
                if ($productType === 'other') {
                    $otherProductIds[] = $item['id'];
                } else {
                    $processedIds[] = $item['id'];
                }
            }

            // Batch load all products
            $processedProducts = ProcessedInventory::whereIn('id', $processedIds)->get()->keyBy('id');
            $otherProducts = OtherProduct::whereIn('id', $otherProductIds)->get()->keyBy('id');

            // Verify each item and calculate subtotal
            foreach ($items as $item) {
                $productType = $item['type'] ?? 'processed';
                $product = null;

                if ($productType === 'other') {
                    $product = $otherProducts->get($item['id']);
                    if (!$product) {
                        throw new \Exception("Produk lain dengan ID {$item['id']} tidak ditemukan.");
                    }
                } else {
                    $product = $processedProducts->get($item['id']);
                    if (!$product) {
                        throw new \Exception("Produk olahan dengan ID {$item['id']} tidak ditemukan.");
                    }
                }

                // Check if product is active
                if (!$product->is_active) {
                    throw new \Exception("Produk {$product->name} sudah tidak aktif.");
                }

                // Check stock availability
                if ($product->current_stock < $item['quantity']) {
                    throw new \Exception("Stok tidak mencukupi untuk {$product->name}. Tersedia: {$product->current_stock}");
                }

                // Calculate subtotal for this item
                $itemSubtotal = $product->selling_price * $item['quantity'];
                $subtotal += $itemSubtotal;

                // Save item details for later
                $totalItems[] = [
                    'product' => $product,
                    'product_type' => $productType,
                    'quantity' => $item['quantity'],
                    'price' => $product->selling_price,
                    'subtotal' => $itemSubtotal
                ];
            }

            // Calculate total (subtotal + tax - discount)
            $taxRate = 0; // Bisa disesuaikan jika ada pajak
            $tax = $subtotal * $taxRate;
            $discount = 0; // Bisa disesuaikan jika ada diskon
            $totalAmount = $subtotal + $tax - $discount;

            // Determine if using payment gateway
            $usePaymentGateway = $request->boolean('use_payment_gateway') || $request->payment_method === 'gateway';

            // For payment gateway, we don't need amount_paid validation yet
            $amountPaid = 0;
            $changeAmount = 0;
            $status = 'pending'; // Default status for payment gateway
            $paymentStatus = 'pending'; // Default payment status

            if (!$usePaymentGateway) {
                // Traditional payment validation
                if (!$request->has('amount_paid') || $request->amount_paid < $totalAmount) {
                    throw new \Exception("Pembayaran tidak mencukupi. Total: Rp ".number_format($totalAmount, 0, ',', '.'));
                }
                $amountPaid = $request->amount_paid;
                $changeAmount = $amountPaid - $totalAmount;
                $status = 'completed';
                $paymentStatus = 'paid'; // Manual payments are immediately paid
            }

            // Parse customer info (name and phone) with validation for Midtrans
            $customerName = $request->customer_name ?? null;
            $customerPhone = $request->customer_phone ?? null;

            // Ensure customer name is not empty for gateway payments
            if ($usePaymentGateway && (empty($customerName) || strlen(trim($customerName)) < 2)) {
                $customerName = 'Customer';
            }

            // Ensure phone number is not empty for gateway payments (prevents Midtrans 500 error)
            if ($usePaymentGateway && empty($customerPhone)) {
                $customerPhone = '081234567890';
            }

            // ULTIMATE SOLUTION: Disable all constraints that can cause locks
            DB::statement('SET SESSION innodb_lock_wait_timeout = 1');
            DB::statement('SET SESSION lock_wait_timeout = 1');
            DB::statement('SET SESSION autocommit = 1');
            DB::statement('SET FOREIGN_KEY_CHECKS = 0'); // DISABLE FOREIGN KEY CHECKS

            try {
                // Create transaction record with RAW INSERT to avoid model events
                $transactionData = [
                    'invoice_number' => 'INV/' . now()->format('YmdHis') . '/' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT),
                    'user_id' => Auth::id(),
                    'customer_name' => $customerName,
                    'customer_phone' => $customerPhone,
                    'subtotal' => $subtotal,
                    'tax' => $tax,
                    'discount' => $discount,
                    'total_amount' => $totalAmount,
                    'amount_paid' => $amountPaid,
                    'change_amount' => $changeAmount,
                    'payment_method' => $usePaymentGateway ? 'gateway' : $request->payment_method,
                    'payment_gateway' => $usePaymentGateway ? 'midtrans' : null,
                    'status' => $status,
                    'payment_status' => $paymentStatus,
                    'notes' => $request->notes,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                // Use RAW INSERT for maximum speed
                $transactionId = DB::table('transactions')->insertGetId($transactionData);

                // Create a Transaction model instance for return
                $transaction = new Transaction();
                $transaction->id = $transactionId;
                $transaction->fill($transactionData);
                $transaction->exists = true;

                // Create transaction items with RAW INSERT
                $itemsData = [];
                foreach ($totalItems as $item) {
                    $productId = $item['product']->id;
                    $processedInventoryId = null;

                    if ($item['product_type'] === 'processed') {
                        $processedInventoryId = $productId;
                    }

                    $itemsData[] = [
                        'transaction_id' => $transactionId,
                        'processed_inventory_id' => $processedInventoryId,
                        'product_id' => $productId,
                        'product_name' => $item['product']->name,
                        'quantity' => $item['quantity'],
                        'price' => $item['price'],
                        'subtotal' => $item['subtotal'],
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }

                // Batch insert items
                if (!empty($itemsData)) {
                    DB::table('transaction_items')->insert($itemsData);
                }

                // Update stock with RAW UPDATE for non-gateway payments
                if (!$usePaymentGateway) {
                    foreach ($totalItems as $item) {
                        $productId = $item['product']->id;

                        if ($item['product_type'] === 'processed') {
                            DB::table('processed_inventory')
                                ->where('id', $productId)
                                ->decrement('current_stock', $item['quantity']);
                        } else {
                            DB::table('other_products')
                                ->where('id', $productId)
                                ->decrement('current_stock', $item['quantity']);
                        }
                    }
                }

            } finally {
                // Re-enable foreign key checks
                DB::statement('SET FOREIGN_KEY_CHECKS = 1');
            }

            // AUDIT LOGGING TEMPORARILY DISABLED DUE TO DATABASE LOCK ISSUES
            // Manual audit logging AFTER transaction (no locks)
            // try {
            //     $this->logTransactionAudit($transaction, $totalItems, 'create');
            // } catch (\Exception $e) {
            //     // Don't fail transaction if audit logging fails
            //     \Log::warning('Manual audit logging failed', [
            //         'transaction_id' => $transaction->id,
            //         'error' => $e->getMessage()
            //     ]);
            // }

            // Log transaction success to Laravel log instead
            \Log::info('Transaction created successfully', [
                'transaction_id' => $transaction->id,
                'invoice_number' => $transaction->invoice_number,
                'customer_name' => $transaction->customer_name,
                'total_amount' => $transaction->total_amount,
                'payment_method' => $transaction->payment_method,
                'user_id' => Auth::id(),
                'ip_address' => request()->ip(),
                'created_at' => now()
            ]);

            if ($request->wantsJson()) {
                $response = [
                    'success' => true,
                    'message' => 'Transaksi berhasil dibuat',
                    'transaction' => $transaction,
                    'use_payment_gateway' => $usePaymentGateway,
                ];

                // If using payment gateway, include payment URL
                if ($usePaymentGateway) {
                    $response['payment_url'] = route('payment.create', $transaction);

                    Log::info('Payment gateway transaction created', [
                        'transaction_id' => $transaction->id,
                        'invoice_number' => $transaction->invoice_number,
                        'total_amount' => $transaction->total_amount,
                        'payment_url' => $response['payment_url']
                    ]);
                }

                return response()->json($response);
            }

            // Add logging for debugging
            \Log::info('Transaction created successfully', [
                'transaction_id' => $transaction->id,
                'invoice_number' => $transaction->invoice_number,
                'use_payment_gateway' => $usePaymentGateway,
                'redirect_to' => route('transactions.show', $transaction)
            ]);

            if ($usePaymentGateway) {
                // For payment gateway, redirect to payment creation
                return redirect()->route('payment.create', $transaction);
            } else {
                return redirect()->route('transactions.show', $transaction)
                                ->with('success', 'Transaksi berhasil dibuat');
            }

        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Transaction creation failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 422);
            }

            return redirect()->back()
                            ->with('error', $e->getMessage())
                            ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Transaction $transaction)
    {
        $transaction->load('items.product', 'user');
        return view('transactions.show', compact('transaction'));
    }

    /**
     * Cancel a transaction (soft delete)
     */
    public function cancel(Transaction $transaction)
    {
        // Only allow cancellation within 24 hours
        if (now()->diffInHours($transaction->created_at) > 24) {
            return redirect()->back()
                            ->with('error', 'Transaksi tidak dapat dibatalkan setelah 24 jam');
        }

        DB::beginTransaction();
        try {
            // Return items to inventory
            foreach ($transaction->items as $item) {
                $item->product->increment('current_stock', $item->quantity);
            }

            // Update transaction status
            $transaction->update(['status' => 'cancelled']);

            DB::commit();

            return redirect()->route('transactions.index')
                            ->with('success', 'Transaksi berhasil dibatalkan');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                            ->with('error', 'Gagal membatalkan transaksi: ' . $e->getMessage());
        }
    }

    /**
     * Generate a receipt for the transaction
     */
    public function receipt(Transaction $transaction)
    {
        $transaction->load(['items', 'user']);

        // Ensure all required data is available
        if (!$transaction->items || $transaction->items->isEmpty()) {
            return redirect()->route('transactions.index')
                ->with('error', 'Data transaksi tidak lengkap untuk dicetak');
        }

        return view('transactions.receipt', compact('transaction'));
    }

    /**
     * Generate a printable receipt for the transaction
     */
    public function print(Transaction $transaction)
    {
        $transaction->load(['items', 'user']);

        // Ensure all required data is available
        if (!$transaction->items || $transaction->items->isEmpty()) {
            return redirect()->route('transactions.index')
                ->with('error', 'Data transaksi tidak lengkap untuk dicetak');
        }

        return view('transactions.print', compact('transaction'));
    }

    /**
     * Export receipt as PDF
     */
    public function exportPdf(Transaction $transaction)
    {
        $transaction->load(['items', 'user']);

        // Ensure all required data is available
        if (!$transaction->items || $transaction->items->isEmpty()) {
            return redirect()->route('transactions.index')
                ->with('error', 'Data transaksi tidak lengkap untuk dicetak');
        }

        try {
            $pdf = Pdf::loadView('transactions.receipt-pdf', compact('transaction'));

            // Set paper size for receipt (80mm width)
            $pdf->setPaper([0, 0, 226.77, 841.89], 'portrait'); // 80mm x 297mm (A4 height)

            return $pdf->download('struk-' . $transaction->invoice_number . '.pdf');
        } catch (\Exception $e) {
            // If PDF generation fails, redirect to print view with auto-download instruction
            return redirect()->route('transactions.print', $transaction)
                ->with('info', 'Gunakan tombol "Save as PDF" untuk menyimpan struk sebagai PDF melalui browser Anda.');
        }
    }

    /**
     * Simple HTML export for PDF conversion
     */
    public function exportHtml(Transaction $transaction)
    {
        $transaction->load(['items', 'user']);

        // Ensure all required data is available
        if (!$transaction->items || $transaction->items->isEmpty()) {
            return redirect()->route('transactions.index')
                ->with('error', 'Data transaksi tidak lengkap untuk dicetak');
        }

        return view('transactions.export-html', compact('transaction'));
    }

    /**
     * Export transactions to CSV
     */
    public function export(Request $request)
    {
        $query = Transaction::with(['items', 'user']);

        // Apply filters
        if ($request->start_date) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->end_date) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }
        if ($request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhere('customer_info', 'like', "%{$search}%")
                  ->orWhereHas('user', function($u) use ($search) {
                      $u->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $transactions = $query->orderBy('created_at', 'desc')->get();

        // Create CSV content
        $csvContent = "No Invoice,Tanggal,Kasir,Metode Pembayaran,Jumlah Item,Total,Bayar,Kembali\n";

        foreach ($transactions as $transaction) {
            $csvContent .= '"' . $transaction->invoice_number . '",';
            $csvContent .= $transaction->created_at->format('d/m/Y H:i') . ',';
            $csvContent .= '"' . ($transaction->user->name ?? 'Admin') . '",';
            $csvContent .= '"' . $transaction->payment_method . '",';
            $csvContent .= $transaction->items->sum('quantity') . ',';
            $csvContent .= number_format($transaction->total_amount, 0) . ',';
            $csvContent .= number_format($transaction->amount_paid, 0) . ',';
            $csvContent .= number_format($transaction->change_amount, 0) . "\n";
        }

        $filename = 'laporan_transaksi_' . date('Y-m-d_H-i-s') . '.csv';

        return response($csvContent)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Transaction $transaction)
    {
        // Check if user has permission to delete
        if (!auth()->user()->isAdmin()) {
            return redirect()->back()
                ->with('error', 'Anda tidak memiliki izin untuk menghapus transaksi.');
        }

        // Check if transaction is not too old (prevent deletion of old transactions)
        if ($transaction->created_at->diffInDays(now()) > 30) {
            return redirect()->back()
                ->with('error', 'Transaksi yang lebih dari 30 hari tidak dapat dihapus.');
        }

        // Use optimized approach with minimal transaction scope
        try {
            // Load transaction items with products in one query to avoid N+1 problem
            $transaction->load(['items.processedInventory', 'items.otherProduct']);

            // Prepare bulk updates for better performance
            $processedUpdates = [];
            $otherProductUpdates = [];

            foreach ($transaction->items as $item) {
                if ($item->processed_inventory_id && $item->processedInventory) {
                    $processedUpdates[$item->processed_inventory_id] =
                        ($processedUpdates[$item->processed_inventory_id] ?? 0) + $item->quantity;
                } elseif ($item->product_id && $item->otherProduct) {
                    $otherProductUpdates[$item->product_id] =
                        ($otherProductUpdates[$item->product_id] ?? 0) + $item->quantity;
                }
            }

            // Temporarily disable audit logging for performance
            config(['audit.disabled' => true]);

            // Start transaction only for critical operations
            DB::beginTransaction();

            // Bulk update processed inventory stock
            foreach ($processedUpdates as $productId => $quantity) {
                ProcessedInventory::where('id', $productId)
                    ->increment('current_stock', $quantity);
            }

            // Bulk update other products stock
            foreach ($otherProductUpdates as $productId => $quantity) {
                OtherProduct::where('id', $productId)
                    ->increment('current_stock', $quantity);
            }

            // Delete transaction (soft delete)
            $transaction->delete();

            DB::commit();

            // Re-enable audit logging
            config(['audit.disabled' => false]);

            return redirect()->route('transactions.index')
                ->with('success', 'Transaksi berhasil dihapus dan stok produk telah dikembalikan.');

        } catch (\Exception $e) {
            DB::rollBack();

            // Log the error for debugging
            \Log::error('Transaction deletion failed', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return redirect()->back()
                ->with('error', 'Gagal menghapus transaksi. Silakan coba lagi atau hubungi administrator.');
        }
    }

    /**
     * Force delete transaction without stock restoration (emergency use only)
     */
    public function forceDestroy(Transaction $transaction)
    {
        // Only super admin can force delete
        if (!auth()->user()->isAdmin()) {
            return redirect()->back()
                ->with('error', 'Anda tidak memiliki izin untuk menghapus paksa transaksi.');
        }

        try {
            // Simple force delete without complex operations
            $transaction->forceDelete();

            return redirect()->route('transactions.index')
                ->with('success', 'Transaksi berhasil dihapus permanen.');

        } catch (\Exception $e) {
            \Log::error('Force transaction deletion failed', [
                'transaction_id' => $transaction->id ?? 'unknown',
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return redirect()->back()
                ->with('error', 'Gagal menghapus transaksi secara paksa.');
        }
    }

    /**
     * Get daily sales report
     */
    public function dailyReport(Request $request)
    {
        $date = $request->get('date', today()->format('Y-m-d'));

        $transactions = Transaction::whereDate('created_at', $date)
                                   ->where('status', 'completed')
                                   ->with('items.product', 'user')
                                   ->latest()
                                   ->get();

        $totalSales = $transactions->sum('total_amount');
        $totalTransactions = $transactions->count();
        $averageTransaction = $totalTransactions > 0 ? $totalSales / $totalTransactions : 0;

        // Group by payment method
        $paymentSummary = $transactions->groupBy('payment_method')
                                       ->map(function ($group) {
                                           return [
                                               'count' => $group->count(),
                                               'total' => $group->sum('total_amount'),
                                           ];
                                       });

        // Top selling products
        $topProducts = TransactionItem::whereHas('transaction', function ($query) use ($date) {
                                        $query->whereDate('created_at', $date)
                                              ->where('status', 'completed');
                                     })
                                     ->with('product')
                                     ->select('product_id', DB::raw('SUM(quantity) as total_qty'))
                                     ->groupBy('product_id')
                                     ->orderByDesc('total_qty')
                                     ->limit(5)
                                     ->get();

        return view('transactions.daily-report', compact(
            'transactions',
            'totalSales',
            'totalTransactions',
            'averageTransaction',
            'paymentSummary',
            'topProducts',
            'date'
        ));
    }

    /**
     * Manual audit logging for transactions (lock-free with RAW SQL)
     */
    private function logTransactionAudit($transaction, $items, $action = 'create')
    {
        try {
            $userId = Auth::id();
            $ipAddress = request()->ip();
            $userAgent = request()->userAgent();
            $now = now();

            // Prepare audit data for transaction
            $transactionAuditData = [
                'user_id' => $userId,
                'action' => $action,
                'model_type' => 'App\Models\Transaction',
                'model_id' => $transaction->id,
                'old_values' => null,
                'new_values' => json_encode([
                    'invoice_number' => $transaction->invoice_number,
                    'customer_name' => $transaction->customer_name,
                    'total_amount' => $transaction->total_amount,
                    'payment_method' => $transaction->payment_method,
                    'status' => $transaction->status,
                    'payment_status' => $transaction->payment_status,
                ]),
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'created_at' => $now,
                'updated_at' => $now,
            ];

            // Prepare audit data for transaction items
            $itemsAuditData = [
                'user_id' => $userId,
                'action' => $action . '_items',
                'model_type' => 'App\Models\TransactionItem',
                'model_id' => $transaction->id,
                'old_values' => null,
                'new_values' => json_encode([
                    'transaction_id' => $transaction->id,
                    'items_count' => count($items),
                    'items_summary' => array_map(function($item) {
                        return [
                            'product_name' => $item['product']->name,
                            'quantity' => $item['quantity'],
                            'price' => $item['price'],
                            'subtotal' => $item['subtotal'],
                        ];
                    }, $items)
                ]),
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'created_at' => $now,
                'updated_at' => $now,
            ];

            // Insert audit logs with RAW SQL (no model events, no locks)
            DB::table('audit_logs')->insert([$transactionAuditData, $itemsAuditData]);

            \Log::info('Manual audit logging successful', [
                'transaction_id' => $transaction->id,
                'logs_created' => 2
            ]);

        } catch (\Exception $e) {
            // Log audit failure but don't break the main operation
            \Log::error('Manual audit logging failed', [
                'transaction_id' => $transaction->id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
