@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Edit Distribusi
                    </h5>
                    <a href="{{ route('distributions.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Kembali
                    </a>
                </div>

                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('distributions.update', $distribution) }}" method="POST" id="distributionForm">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="destination" class="form-label">Tujuan Distribusi <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="destination" name="destination" 
                                           value="{{ old('destination', $distribution->market_name) }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="distribution_date" class="form-label">Tanggal Distribusi <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="distribution_date" name="distribution_date" 
                                           value="{{ old('distribution_date', $distribution->distribution_date->format('Y-m-d')) }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vehicle_info" class="form-label">Informasi Kendaraan</label>
                                    <input type="text" class="form-control" id="vehicle_info" name="vehicle_info" 
                                           value="{{ old('vehicle_info', $distribution->vehicle_info) }}" 
                                           placeholder="Contoh: Truck B 1234 CD">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="driver_name" class="form-label">Nama Sopir</label>
                                    <input type="text" class="form-control" id="driver_name" name="driver_name" 
                                           value="{{ old('driver_name', $distribution->driver_name) }}" 
                                           placeholder="Nama sopir">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Catatan</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="Catatan tambahan untuk distribusi ini">{{ old('notes', $distribution->notes) }}</textarea>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Produk yang Didistribusikan</h6>
                            <button type="button" class="btn btn-sm btn-primary" id="addProductBtn">
                                <i class="fas fa-plus me-1"></i>Tambah Produk
                            </button>
                        </div>

                        <div id="productContainer">
                            @foreach($distribution->items as $index => $item)
                            <div class="product-item border rounded p-3 mb-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">Produk <span class="text-danger">*</span></label>
                                        <select name="items[{{ $index }}][product_id]" class="form-select product-select" required>
                                            <option value="">Pilih Produk</option>
                                            @foreach($processedInventory as $product)
                                                <option value="{{ $product->id }}" 
                                                        data-stock="{{ $product->current_stock }}" 
                                                        data-price="{{ $product->selling_price }}"
                                                        {{ $item->processed_inventory_id == $product->id ? 'selected' : '' }}>
                                                    {{ $product->name }} (Stok: {{ $product->current_stock }})
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Jumlah <span class="text-danger">*</span></label>
                                        <input type="number" name="items[{{ $index }}][quantity]" 
                                               class="form-control quantity-input" 
                                               value="{{ $item->quantity }}" 
                                               min="1" required>
                                        <small class="text-muted">Stok tersedia: <span class="available-stock">{{ $item->processedInventory->current_stock ?? 0 }}</span></small>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="button" class="btn btn-danger w-100 remove-product">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('distributions.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Update Distribusi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let productIndex = {{ count($distribution->items) }};
    
    // Add product button
    document.getElementById('addProductBtn').addEventListener('click', function() {
        const container = document.getElementById('productContainer');
        const productOptions = @json($processedInventory->map(function($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'stock' => $product->current_stock,
                'price' => $product->selling_price
            ];
        }));
        
        const productHtml = `
            <div class="product-item border rounded p-3 mb-3">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Produk <span class="text-danger">*</span></label>
                        <select name="items[${productIndex}][product_id]" class="form-select product-select" required>
                            <option value="">Pilih Produk</option>
                            ${productOptions.map(product => 
                                `<option value="${product.id}" data-stock="${product.stock}" data-price="${product.price}">
                                    ${product.name} (Stok: ${product.stock})
                                </option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Jumlah <span class="text-danger">*</span></label>
                        <input type="number" name="items[${productIndex}][quantity]" class="form-control quantity-input" min="1" required>
                        <small class="text-muted">Stok tersedia: <span class="available-stock">0</span></small>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-danger w-100 remove-product">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', productHtml);
        productIndex++;
        
        // Attach event listeners to new elements
        attachProductEventListeners();
    });
    
    // Remove product
    function attachProductEventListeners() {
        document.querySelectorAll('.remove-product').forEach(button => {
            button.addEventListener('click', function() {
                this.closest('.product-item').remove();
            });
        });
        
        document.querySelectorAll('.product-select').forEach(select => {
            select.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                const stockSpan = this.closest('.product-item').querySelector('.available-stock');
                const quantityInput = this.closest('.product-item').querySelector('.quantity-input');
                
                if (selectedOption.value) {
                    const stock = selectedOption.dataset.stock;
                    stockSpan.textContent = stock;
                    quantityInput.max = stock;
                } else {
                    stockSpan.textContent = '0';
                    quantityInput.max = '';
                }
            });
        });
    }
    
    // Initial attachment
    attachProductEventListeners();
});
</script>
@endsection
