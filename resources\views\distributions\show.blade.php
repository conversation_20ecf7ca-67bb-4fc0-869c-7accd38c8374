@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-truck me-2"></i>Detail Distribusi
                        </h4>
                        <div>
                            <a href="{{ route('distributions.index') }}" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i>Kembali
                            </a>
                            <button onclick="window.print()" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-print me-1"></i>Cetak
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Distribution Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-card">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-info-circle me-2"></i>Informasi Distribusi
                                </h6>
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td width="40%"><strong>ID Distribusi:</strong></td>
                                        <td>{{ $distribution->id }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Tanggal Distribusi:</strong></td>
                                        <td>{{ $distribution->distribution_date->format('d/m/Y H:i') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Tujuan:</strong></td>
                                        <td>{{ $distribution->destination }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="badge bg-{{ $distribution->status === 'completed' ? 'success' : ($distribution->status === 'pending' ? 'warning' : 'danger') }}">
                                                {{ ucfirst($distribution->status) }}
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-card">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-user me-2"></i>Informasi Penanggung Jawab
                                </h6>
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td width="40%"><strong>Dibuat oleh:</strong></td>
                                        <td>{{ $distribution->user->name ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Tanggal Dibuat:</strong></td>
                                        <td>{{ $distribution->created_at->format('d/m/Y H:i') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Terakhir Update:</strong></td>
                                        <td>{{ $distribution->updated_at->format('d/m/Y H:i') }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Distribution Items -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-boxes me-2"></i>Produk yang Didistribusikan
                            </h6>
                            
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th width="5%">#</th>
                                            <th width="15%">Gambar</th>
                                            <th width="25%">Nama Produk</th>
                                            <th width="15%">Batch Number</th>
                                            <th width="10%">Jumlah</th>
                                            <th width="15%">Tanggal Kadaluarsa</th>
                                            <th width="15%">Status Kadaluarsa</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($distribution->items as $index => $item)
                                        <tr>
                                            <td>{{ $index + 1 }}</td>
                                            <td>
                                                @if($item->processedInventory && $item->processedInventory->image)
                                                    <img src="{{ asset('images/products/' . $item->processedInventory->image) }}"
                                                         alt="{{ $item->processedInventory->name }}"
                                                         class="img-thumbnail"
                                                         style="width: 60px; height: 60px; object-fit: cover;"
                                                         onerror="this.onerror=null; this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                    <div class="bg-light d-flex align-items-center justify-content-center"
                                                         style="width: 60px; height: 60px; border-radius: 8px; display: none;">
                                                        <i class="fas fa-seedling text-success"></i>
                                                    </div>
                                                @else
                                                    <div class="bg-light d-flex align-items-center justify-content-center"
                                                         style="width: 60px; height: 60px; border-radius: 8px;">
                                                        <i class="fas fa-seedling text-success"></i>
                                                    </div>
                                                @endif
                                            </td>
                                            <td>
                                                <strong>{{ $item->processedInventory->name ?? 'N/A' }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $item->processedInventory->product_type ?? '' }}</small>
                                            </td>
                                            <td>
                                                <code>{{ $item->processedInventory->batch_number ?? 'N/A' }}</code>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary fs-6">{{ $item->quantity }}</span>
                                            </td>
                                            <td>
                                                {{ $item->processedInventory->expiry_date ? $item->processedInventory->expiry_date->format('d/m/Y') : 'N/A' }}
                                            </td>
                                            <td>
                                                @if($item->processedInventory && $item->processedInventory->expiry_date)
                                                    @php
                                                        $daysUntilExpiry = now()->diffInDays($item->processedInventory->expiry_date, false);
                                                    @endphp
                                                    @if($daysUntilExpiry < 0)
                                                        <span class="badge bg-danger">Kadaluarsa</span>
                                                    @elseif($daysUntilExpiry <= 3)
                                                        <span class="badge bg-warning">{{ $daysUntilExpiry }} hari lagi</span>
                                                    @else
                                                        <span class="badge bg-success">{{ $daysUntilExpiry }} hari lagi</span>
                                                    @endif
                                                @else
                                                    <span class="badge bg-secondary">N/A</span>
                                                @endif
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">Tidak ada produk dalam distribusi ini</p>
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Summary -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <h5 class="mb-1">{{ $distribution->items->count() }}</h5>
                                        <small>Total Item</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h5 class="mb-1">{{ $distribution->items->sum('quantity') }}</h5>
                                        <small>Total Quantity</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h5 class="mb-1">{{ $distribution->destination }}</h5>
                                        <small>Tujuan Distribusi</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h5 class="mb-1">{{ ucfirst($distribution->status) }}</h5>
                                        <small>Status</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    @if($distribution->notes)
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-light">
                                <h6 class="alert-heading">
                                    <i class="fas fa-sticky-note me-2"></i>Catatan
                                </h6>
                                <p class="mb-0">{{ $distribution->notes }}</p>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    height: 100%;
}

@media print {
    .btn, .card-header .d-flex > div:last-child {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
@endsection
