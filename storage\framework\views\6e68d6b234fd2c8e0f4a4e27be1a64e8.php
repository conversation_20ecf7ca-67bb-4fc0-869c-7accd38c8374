<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        height: 100%;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }
    
    .stats-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-bottom: 15px;
        font-size: 24px;
    }
    
    .stats-icon.primary {
        background-color: rgba(0, 123, 255, 0.2);
        color: #0d6efd;
    }
    
    .stats-icon.success {
        background-color: rgba(40, 167, 69, 0.2);
        color: #28a745;
    }
    
    .stats-icon.warning {
        background-color: rgba(255, 193, 7, 0.2);
        color: #ffc107;
    }
    
    .stats-icon.danger {
        background-color: rgba(220, 53, 69, 0.2);
        color: #dc3545;
    }
    
    .stats-title {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 8px;
    }
    
    .stats-value {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 8px;
        color: #343a40;
    }
    
    .stats-desc {
        font-size: 12px;
        color: #6c757d;
    }
    
    .card {
        border-radius: 10px;
        border: none;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: all 0.3s ease;
        overflow: hidden;
    }
    
    .card:hover {
        box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }
    
    .card-header {
        background-color: white;
        border-bottom: 1px solid rgba(0,0,0,0.1);
        padding: 15px 20px;
        font-weight: 600;
        color: #343a40;
    }
    
    .card-body {
        padding: 20px;
    }
    
    .page-title {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 20px;
        color: var(--primary-color);
        padding: 10px 0;
        border-bottom: 1px solid rgba(0,0,0,0.1);
    }
    
    .page-title i {
        margin-right: 10px;
        color: var(--primary-color);
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        padding: 15px;
        border-radius: 10px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
        margin-bottom: 10px;
        text-decoration: none;
        color: #343a40;
    }
    
    .quick-action:hover {
        background-color: #e9ecef;
        transform: translateX(5px);
    }
    
    .quick-action i {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin-right: 15px;
        color: white;
    }
    
    .quick-action i.bg-primary {
        background-color: #0d6efd;
    }
    
    .quick-action i.bg-success {
        background-color: #28a745;
    }
    
    .quick-action i.bg-warning {
        background-color: #ffc107;
    }
    
    .quick-action i.bg-danger {
        background-color: #dc3545;
    }
    
    .custom-table {
        width: 100%;
    }
    
    .custom-table th, .custom-table td {
        padding: 10px;
        border-bottom: 1px solid #dee2e6;
    }
    
    .custom-table th {
        font-weight: 600;
        color: #495057;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
    
    .recent-transaction {
        display: flex;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #dee2e6;
    }
    
    .recent-transaction .transaction-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }
    
    .recent-transaction .transaction-details {
        flex-grow: 1;
    }
    
    .recent-transaction .transaction-title {
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .recent-transaction .transaction-date {
        font-size: 12px;
        color: #6c757d;
    }
    
    .recent-transaction .transaction-amount {
        font-weight: 600;
        color: #28a745;
    }
    
    .financial-summary {
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 10px;
    }
    
    .financial-summary.revenue {
        background-color: rgba(40, 167, 69, 0.1);
        border-left: 3px solid #28a745;
    }
    
    .financial-summary.expense {
        background-color: rgba(220, 53, 69, 0.1);
        border-left: 3px solid #dc3545;
    }
    
    .financial-summary.profit {
        background-color: rgba(0, 123, 255, 0.1);
        border-left: 3px solid #0d6efd;
    }
    
    .financial-summary .financial-title {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 5px;
    }
    
    .financial-summary .financial-value {
        font-size: 18px;
        font-weight: 700;
        color: #343a40;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="page-title">
            <i class="fas fa-tachometer-alt"></i> Dashboard
        </h1>
        <div>
            <span class="badge bg-primary"><?php echo e(Carbon\Carbon::now()->format('d F Y')); ?></span>
        </div>
    </div>

    <!-- Stats Row -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-title">Penjualan Hari Ini</div>
                        <h3 class="stats-value"><?php echo e($summary['today_sales_count']); ?></h3>
                        <div class="stats-desc">Rp <?php echo e(number_format($summary['today_sales_amount'], 0, ',', '.')); ?></div>
                    </div>
                    <div class="stats-icon primary">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-title">Pendapatan Bulan Ini</div>
                        <h3 class="stats-value">Rp <?php echo e(number_format($summary['month_revenue'], 0, ',', '.')); ?></h3>
                        <div class="stats-desc"><?php echo e($summary['month_revenue_trend']); ?></div>
                    </div>
                    <div class="stats-icon success">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-title">Stok Ubi Mentah</div>
                        <h3 class="stats-value"><?php echo e(number_format($summary['raw_inventory_total'], 1, ',', '.')); ?> kg</h3>
                        <div class="stats-desc"><?php echo e($summary['raw_inventory_count']); ?> jenis</div>
                    </div>
                    <div class="stats-icon warning">
                        <i class="fas fa-seedling"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-title">Stok Menipis</div>
                        <h3 class="stats-value"><?php echo e($summary['low_stock_count']); ?></h3>
                        <div class="stats-desc">Memerlukan perhatian segera</div>
                    </div>
                    <div class="stats-icon danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Summary & Monthly Sales Chart -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-chart-line me-2"></i> Grafik Penjualan 7 Hari Terakhir</span>
                    <a href="<?php echo e(route('reports.sales')); ?>" class="btn btn-sm btn-outline-primary">
                        Detail Laporan
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="px-4 py-3 border-bottom">
                        <div class="modern-chart-container" style="position: relative; height: 300px;">
                            <div id="weeklySalesChart"></div>
                        </div>
                    </div>
                    
                    <div class="row py-3 m-0 text-center">
                        <div class="col-md-4 border-end">
                            <div class="small text-muted mb-1">Total Pendapatan</div>
                            <div class="h5 fw-bold text-primary mb-0">Rp <?php echo e(number_format(array_sum($salesChartData['revenue']), 0, ',', '.')); ?></div>
                        </div>
                        <div class="col-md-4 border-end">
                            <div class="small text-muted mb-1">Total Transaksi</div>
                            <div class="h5 fw-bold text-success mb-0"><?php echo e(array_sum($salesChartData['transactions'])); ?></div>
                        </div>
                        <div class="col-md-4">
                            <div class="small text-muted mb-1">Rata-rata per Hari</div>
                            <div class="h5 fw-bold text-info mb-0">Rp <?php echo e(number_format(array_sum($salesChartData['revenue']) / count($salesChartData['labels']), 0, ',', '.')); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card h-100">
                <div class="card-header">
                    <i class="fas fa-calculator me-2"></i> Ringkasan Keuangan (<?php echo e(Carbon\Carbon::now()->format('F Y')); ?>)
                </div>
                <div class="card-body">
                    <div class="financial-summary revenue mb-3">
                        <div class="financial-title">Pendapatan</div>
                        <div class="financial-value">Rp <?php echo e(number_format($financialReport['revenue'], 0, ',', '.')); ?></div>
                    </div>
                    <div class="financial-summary expense mb-3">
                        <div class="financial-title">Biaya Bahan Baku</div>
                        <div class="financial-value">Rp <?php echo e(number_format($financialReport['raw_material_cost'], 0, ',', '.')); ?></div>
                    </div>
                    <div class="financial-summary profit">
                        <div class="financial-title">Laba Kotor</div>
                        <div class="financial-value">Rp <?php echo e(number_format($financialReport['gross_profit'], 0, ',', '.')); ?></div>
                    </div>
                    <div class="text-center mt-4">
                        <a href="<?php echo e(route('reports.financial')); ?>" class="btn btn-sm btn-primary">
                            <i class="fas fa-file-invoice-dollar me-1"></i> Laporan Lengkap
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Top Products & Recent Transactions -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-trophy me-2"></i> Produk Terlaris</span>
                    <span class="badge bg-info">30 hari terakhir</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>Produk</th>
                                    <th class="text-center">Terjual</th>
                                    <th class="text-end">Pendapatan</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $topProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="ms-2">
                                                <div class="fw-bold"><?php echo e($product->product->name ?? $product->name); ?></div>
                                                <div class="small text-muted"><?php echo e($product->product->product_type ?? 'Produk'); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center"><?php echo e($product->total_sold); ?> pcs</td>
                                    <td class="text-end">Rp <?php echo e(number_format(($product->product->selling_price ?? 0) * $product->total_sold, 0, ',', '.')); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="3" class="text-center">Tidak ada data</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-receipt me-2"></i> Transaksi Terbaru</span>
                    <a href="<?php echo e(route('transactions.index')); ?>" class="btn btn-sm btn-outline-primary">
                        Lihat Semua
                    </a>
                </div>
                <div class="card-body">
                    <?php $__empty_1 = true; $__currentLoopData = $recentTransactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="recent-transaction">
                        <div class="transaction-icon">
                            <i class="fas fa-shopping-bag text-primary"></i>
                        </div>
                        <div class="transaction-details">
                            <div class="transaction-title">Transaksi #<?php echo e($transaction->transaction_number); ?></div>
                            <div class="transaction-date"><?php echo e($transaction->created_at->format('d M Y, H:i')); ?></div>
                        </div>
                        <div class="transaction-amount">
                            Rp <?php echo e(number_format($transaction->total_amount, 0, ',', '.')); ?>

                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <p>Belum ada transaksi hari ini</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Alert & Quick Actions -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-exclamation-circle me-2"></i> Peringatan Stok Menipis</span>
                    <a href="<?php echo e(route('inventory.low-stock-alert')); ?>" class="btn btn-sm btn-outline-danger">
                        Kelola Stok
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>Produk</th>
                                    <th class="text-center">Stok Saat Ini</th>
                                    <th class="text-center">Batas Minimum</th>
                                    <th class="text-end">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $lowStockItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div class="fw-bold"><?php echo e($item->name); ?></div>
                                        <div class="small text-muted"><?php echo e($item->type == 'raw' ? 'Ubi Mentah' : 'Ubi Bakar'); ?></div>
                                    </td>
                                    <td class="text-center"><?php echo e($item->current_stock); ?></td>
                                    <td class="text-center"><?php echo e($item->min_stock_threshold); ?></td>
                                    <td class="text-end">
                                        <span class="badge bg-danger">Stok Rendah</span>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="4" class="text-center">Tidak ada stok yang menipis</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <i class="fas fa-bolt me-2"></i> Aksi Cepat
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="<?php echo e(route('transactions.create')); ?>" class="quick-action">
                                <i class="fas fa-cash-register bg-primary"></i>
                                <div>
                                    <div class="fw-bold">Transaksi Baru</div>
                                    <div class="small text-muted">Buat transaksi penjualan</div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="<?php echo e(route('processed-inventory.show-process-form')); ?>" class="quick-action">
                                <i class="fas fa-industry bg-success"></i>
                                <div>
                                    <div class="fw-bold">Produksi Ubi</div>
                                    <div class="small text-muted">Proses ubi mentah</div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="<?php echo e(route('raw-inventory.index')); ?>" class="quick-action">
                                <i class="fas fa-boxes bg-warning"></i>
                                <div>
                                    <div class="fw-bold">Tambah Stok</div>
                                    <div class="small text-muted">Perbarui inventaris</div>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="<?php echo e(route('expiry-recommendations.index')); ?>" class="quick-action">
                                <i class="fas fa-exclamation-triangle bg-danger"></i>
                                <div>
                                    <div class="fw-bold">Rekomendasi Segera Jual</div>
                                    <div class="small text-muted">Cek ubi hampir kadaluarsa</div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var salesData = <?php echo json_encode($salesChartData, 15, 512) ?>;
        
        var options = {
            series: [
                {
                    name: 'Pendapatan',
                    type: 'column',
                    data: salesData.revenue
                },
                {
                    name: 'Transaksi',
                    type: 'line',
                    data: salesData.transactions
                }
            ],
            chart: {
                height: 350,
                type: 'line',
                fontFamily: 'Poppins, sans-serif',
                toolbar: {
                    show: false
                },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800,
                    animateGradually: {
                        enabled: true,
                        delay: 150
                    },
                    dynamicAnimation: {
                        enabled: true,
                        speed: 350
                    }
                }
            },
            colors: ['#4e73df', '#1cc88a'],
            plotOptions: {
                bar: {
                    borderRadius: 6,
                    dataLabels: {
                        position: 'top'
                    }
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                width: [0, 3],
                curve: 'smooth'
            },
            legend: {
                show: true,
                position: 'top',
                horizontalAlign: 'right',
                fontSize: '14px',
                markers: {
                    radius: 12
                }
            },
            xaxis: {
                categories: salesData.labels,
                labels: {
                    style: {
                        fontSize: '12px'
                    }
                }
            },
            yaxis: [
                {
                    title: {
                        text: 'Pendapatan (Rp)',
                        style: {
                            fontSize: '12px'
                        }
                    },
                    labels: {
                        formatter: function(val) {
                            return 'Rp ' + new Intl.NumberFormat('id-ID').format(val);
                        },
                        style: {
                            fontSize: '12px'
                        }
                    }
                },
                {
                    opposite: true,
                    title: {
                        text: 'Jumlah Transaksi',
                        style: {
                            fontSize: '12px'
                        }
                    },
                    labels: {
                        style: {
                            fontSize: '12px'
                        }
                    }
                }
            ],
            tooltip: {
                shared: true,
                intersect: false,
                y: {
                    formatter: function(value, { seriesIndex }) {
                        if (seriesIndex === 0) {
                            return 'Rp ' + new Intl.NumberFormat('id-ID').format(value);
                        }
                        return value + ' transaksi';
                    }
                }
            },
            grid: {
                borderColor: '#f1f1f1',
                padding: {
                    bottom: 10
                }
            },
            responsive: [
                {
                    breakpoint: 768,
                    options: {
                        yaxis: [
                            {
                                labels: {
                                    formatter: function(val) {
                                        return 'Rp ' + (val / 1000) + 'K';
                                    }
                                }
                            },
                            {}
                        ]
                    }
                }
            ]
        };

        var chart = new ApexCharts(document.querySelector("#weeklySalesChart"), options);
        chart.render();
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/dashboard/index.blade.php ENDPATH**/ ?>