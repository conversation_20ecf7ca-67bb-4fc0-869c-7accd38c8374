@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-users"></i>
        <span>Manajemen User</span>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon primary">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Total User</div>
                    <h3 class="stats-value">{{ $roleStats['total'] }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon success">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Admin</div>
                    <h3 class="stats-value">{{ $roleStats['admin'] }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon info">
                    <i class="fas fa-user-tie"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Karyawan</div>
                    <h3 class="stats-value">{{ $roleStats['employee'] }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon success">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Aktif</div>
                    <h3 class="stats-value">{{ $roleStats['active'] }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon danger">
                    <i class="fas fa-user-times"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Terhapus</div>
                    <h3 class="stats-value">{{ $roleStats['deleted'] }}</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Actions -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <form method="GET" action="{{ route('users.index') }}" class="row g-3">
                        <div class="col-md-4">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="Cari nama atau email..." 
                                   value="{{ request('search') }}">
                        </div>
                        <div class="col-md-3">
                            <select name="role" class="form-select">
                                <option value="">Semua Role</option>
                                <option value="admin" {{ request('role') == 'admin' ? 'selected' : '' }}>Admin</option>
                                <option value="employee" {{ request('role') == 'employee' ? 'selected' : '' }}>Karyawan</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select" onchange="this.form.submit()">
                                <option value="active" {{ request('status', 'active') == 'active' ? 'selected' : '' }}>
                                    Aktif ({{ $roleStats['active'] }})
                                </option>
                                <option value="deleted" {{ request('status') == 'deleted' ? 'selected' : '' }}>
                                    Terhapus ({{ $roleStats['deleted'] }})
                                </option>
                                <option value="all" {{ request('status') == 'all' ? 'selected' : '' }}>
                                    Semua ({{ $roleStats['total'] }})
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </form>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ route('users.create') }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Tambah User
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Info Alert -->
    @if(request('status') == 'deleted')
        <div class="alert alert-warning">
            <i class="fas fa-info-circle"></i>
            <strong>Menampilkan User Terhapus:</strong> Anda sedang melihat {{ $roleStats['deleted'] }} user yang telah dihapus.
            <a href="{{ route('users.index', ['status' => 'active']) }}" class="alert-link">Klik di sini untuk melihat user aktif</a>.
        </div>
    @elseif(request('status') == 'all')
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <strong>Menampilkan Semua User:</strong> Anda sedang melihat semua user (aktif dan terhapus).
        </div>
    @else
        @if($roleStats['deleted'] > 0)
            <div class="alert alert-success">
                <i class="fas fa-info-circle"></i>
                <strong>Menampilkan User Aktif:</strong> Ada {{ $roleStats['deleted'] }} user terhapus yang tidak ditampilkan.
                <a href="{{ route('users.index', ['status' => 'deleted']) }}" class="alert-link">Klik di sini untuk melihat user terhapus</a>.
            </div>
        @endif
    @endif

    <!-- Users Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-list"></i> Daftar User
                @if(request('search'))
                    <small class="text-muted">- Hasil pencarian: "{{ request('search') }}"</small>
                @endif
                @if(request('status') == 'deleted')
                    <span class="badge bg-warning ms-2">Terhapus</span>
                @elseif(request('status') == 'all')
                    <span class="badge bg-info ms-2">Semua</span>
                @else
                    <span class="badge bg-success ms-2">Aktif</span>
                @endif
            </h5>
        </div>
        <div class="card-body">
            @if($users->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Nama</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Dibuat</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($users as $user)
                                <tr>
                                    <td>{{ $user->id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <div class="avatar-title bg-primary rounded-circle">
                                                    {{ strtoupper(substr($user->name, 0, 1)) }}
                                                </div>
                                            </div>
                                            <strong>{{ $user->name }}</strong>
                                        </div>
                                    </td>
                                    <td>{{ $user->email }}</td>
                                    <td>
                                        <span class="badge bg-{{ $user->role === 'admin' ? 'danger' : 'primary' }}">
                                            {{ $user->role === 'admin' ? 'Admin' : 'Karyawan' }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($user->deleted_at)
                                            <span class="badge bg-danger">Terhapus</span>
                                        @else
                                            <span class="badge bg-success">Aktif</span>
                                        @endif
                                    </td>
                                    <td>{{ \Carbon\Carbon::parse($user->created_at)->format('d/m/Y H:i') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('users.show', $user->id) }}" 
                                               class="btn btn-sm btn-info" title="Lihat">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            @if(!$user->deleted_at)
                                                <a href="{{ route('users.edit', $user->id) }}" 
                                                   class="btn btn-sm btn-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                
                                                @if($user->id != auth()->id())
                                                    <button type="button" class="btn btn-sm btn-danger" 
                                                            title="Hapus" onclick="deleteUser({{ $user->id }}, '{{ $user->name }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                @endif
                                            @else
                                                <form action="{{ route('users.restore', $user->id) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-success" 
                                                            title="Pulihkan" onclick="return confirm('Yakin ingin memulihkan user ini?')">
                                                        <i class="fas fa-undo"></i>
                                                    </button>
                                                </form>
                                                
                                                @if($user->role !== 'admin')
                                                    <form action="{{ route('users.force-delete', $user->id) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-dark" 
                                                                title="Hapus Permanen" onclick="return confirm('PERINGATAN: Ini akan menghapus user secara permanen! Yakin?')">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        Menampilkan {{ $users->firstItem() }} - {{ $users->lastItem() }} dari {{ $users->total() }} user
                    </div>
                    <div>
                        {{ $users->appends(request()->query())->links() }}
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Tidak ada user ditemukan</h5>
                    <p class="text-muted">
                        @if(request('search'))
                            Tidak ada user yang sesuai dengan pencarian "{{ request('search') }}"
                        @else
                            Belum ada user yang terdaftar
                        @endif
                    </p>
                    @if(!request('search'))
                        <a href="{{ route('users.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Tambah User Pertama
                        </a>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>

<script>
function deleteUser(userId, userName) {
    if (!confirm(`Yakin ingin menghapus user "${userName}"?`)) {
        return;
    }
    
    // Show loading state
    const button = event.target.closest('button');
    const originalHtml = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;
    
    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/users/${userId}`;
    form.style.display = 'none';
    
    // Add CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken.getAttribute('content');
        form.appendChild(csrfInput);
    }
    
    // Add method override
    const methodInput = document.createElement('input');
    methodInput.type = 'hidden';
    methodInput.name = '_method';
    methodInput.value = 'DELETE';
    form.appendChild(methodInput);
    
    // Submit form
    document.body.appendChild(form);
    form.submit();
}
</script>
@endsection
