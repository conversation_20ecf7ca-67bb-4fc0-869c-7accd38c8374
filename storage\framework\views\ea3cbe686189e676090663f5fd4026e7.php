<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Ubi Bakar Cilembu</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=poppins:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #FF8C00;
            --accent-color: #4CAF50;
            --light-color: #FFF8E1;
            --dark-color: #5D4037;
            --transition: all 0.3s ease-in-out;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light-color);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .login-container {
            display: flex;
            border-radius: 15px;
            overflow: hidden;
            width: 900px;
            max-width: 100%;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .login-image {
            width: 40%;
            background: #5D5D5D;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            color: white;
        }

        .login-image .logo {
            font-size: 24px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .login-image .logo i {
            color: var(--secondary-color);
        }

        .login-image .welcome-text {
            margin-top: auto;
        }

        .login-image h2 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .login-image p {
            font-size: 16px;
            opacity: 0.9;
        }

        .login-form {
            width: 60%;
            background: white;
            padding: 40px;
        }

        .login-form h1 {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 30px;
        }

        .info-box {
            background-color: #FFF9F3;
            border-left: 4px solid var(--secondary-color);
            padding: 15px;
            margin-bottom: 25px;
            border-radius: 4px;
        }

        .info-box h5 {
            font-size: 16px;
            margin-bottom: 8px;
            color: #333;
        }

        .info-box ul {
            padding-left: 20px;
            margin-bottom: 0;
        }

        .info-box ul li {
            margin-bottom: 5px;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
            color: #333;
        }

        .form-control {
            height: 50px;
            border-radius: 4px;
            border: 1px solid #ddd;
            padding: 10px 15px;
            font-size: 15px;
            width: 100%;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(139, 69, 19, 0.25);
        }

        .role-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 25px;
        }

        .role-option {
            flex: 1;
            text-align: center;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            transition: var(--transition);
            background: #f9f9f9;
        }

        .role-option.active {
            border-color: var(--primary-color);
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .role-option i {
            font-size: 20px;
            margin-right: 8px;
        }

        .form-check {
            margin-bottom: 20px;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            height: 50px;
            font-weight: 600;
            padding: 10px 30px;
            border-radius: 4px;
            transition: var(--transition);
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: #773A0D;
            border-color: #773A0D;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .input-group-text {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-right: none;
        }

        .invalid-feedback {
            display: block;
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                width: 95%;
            }
            .login-image, .login-form {
                width: 100%;
            }
            .login-image {
                padding: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-image">
            <div class="logo">
                <i class="fas fa-fire"></i> Toko Ubi Bakar Cilembu
            </div>
            <div class="welcome-text">
                <h2>Selamat Datang Kembali!</h2>
                <p>Silakan login untuk mengakses sistem manajemen Toko Ubi Bakar Cilembu dan kelola usaha Anda dengan lebih efisien.</p>
            </div>
        </div>
        <div class="login-form">
            <h1>Login</h1>
            
            <div class="info-box">
                <h5>Info! Kami memiliki 2 jenis akun:</h5>
                <ul>
                    <li><strong>Admin:</strong> Akses penuh ke semua fitur</li>
                    <li><strong>Karyawan:</strong> Akses ke transaksi dan inventory</li>
                </ul>
            </div>

            <h5 class="mb-3">Pilih Jenis Akun:</h5>
            <div class="role-selector mb-4">
                <div class="role-option active" id="admin-option" onclick="selectRole('admin')">
                    <i class="fas fa-user-shield"></i> Admin
                </div>
                <div class="role-option" id="karyawan-option" onclick="selectRole('karyawan')">
                    <i class="fas fa-user"></i> Karyawan
                </div>
            </div>

            <form method="POST" action="<?php echo e(route('login')); ?>">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="role" id="role" value="admin">

                <div class="form-group">
                    <label for="email">Email</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                        <input id="email" type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="email" value="<?php echo e(old('email') ?? '<EMAIL>'); ?>" required autocomplete="email" autofocus>
                    </div>
                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="invalid-feedback" role="alert">
                            <strong><?php echo e($message); ?></strong>
                        </span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input id="password" type="password" class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="password" required autocomplete="current-password">
                    </div>
                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="invalid-feedback" role="alert">
                            <strong><?php echo e($message); ?></strong>
                        </span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="form-check mb-4">
                    <input class="form-check-input" type="checkbox" name="remember" id="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                    <label class="form-check-label" for="remember">
                        Ingat saya
                    </label>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> Login
                </button>
            </form>
        </div>
    </div>

    <script>
        // Fungsi untuk mengisi email berdasarkan role yang dipilih
        function selectRole(role) {
            document.getElementById('role').value = role;
            
            if (role === 'admin') {
                document.getElementById('admin-option').classList.add('active');
                document.getElementById('karyawan-option').classList.remove('active');
                document.getElementById('email').value = '<EMAIL>';
            } else {
                document.getElementById('admin-option').classList.remove('active');
                document.getElementById('karyawan-option').classList.add('active');
                document.getElementById('email').value = '<EMAIL>';
            }
        }
    </script>
</body>
</html>
<?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/auth/login.blade.php ENDPATH**/ ?>