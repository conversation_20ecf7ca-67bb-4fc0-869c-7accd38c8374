

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-box"></i>
        <span>Produk Lain</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Daftar Produk Lain</span>
                    <div>
                        <a href="<?php echo e(route('other-products.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Tambah Produk Baru
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if($lowStock->count() > 0): ?>
                    <div class="alert alert-warning mb-4">
                        <h5><i class="fas fa-exclamation-triangle"></i> Peringatan Stok Menipis</h5>
                        <ul class="mb-0">
                            <?php $__currentLoopData = $lowStock; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($item->name); ?> - tersisa <?php echo e($item->current_stock); ?> <?php echo e($item->unit); ?> (minimum: <?php echo e($item->min_stock_threshold ?? 0); ?> <?php echo e($item->unit); ?>)</li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>SKU</th>
                                    <th>Nama Produk</th>
                                    <th>Kategori</th>
                                    <th>Stok</th>
                                    <th>Harga Beli</th>
                                    <th>Harga Jual</th>
                                    <th>Margin</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $otherProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($index + 1); ?></td>
                                    <td><?php echo e($item->sku); ?></td>
                                    <td><?php echo e($item->name); ?></td>
                                    <td><?php echo e($item->category ?: '-'); ?></td>
                                    <td><?php echo e($item->current_stock); ?> <?php echo e($item->unit); ?></td>
                                    <td>Rp <?php echo e(number_format($item->purchase_price, 0, ',', '.')); ?></td>
                                    <td>Rp <?php echo e(number_format($item->selling_price, 0, ',', '.')); ?></td>
                                    <td><?php echo e(number_format($item->getProfitMargin(), 1)); ?>%</td>
                                    <td>
                                        <?php if($item->isLowStock()): ?>
                                        <span class="status-badge danger">Stok Menipis</span>
                                        <?php else: ?>
                                        <span class="status-badge success">Tersedia</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="<?php echo e(route('other-products.edit', $item)); ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?php echo e(route('other-products.show', $item)); ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <form action="<?php echo e(route('other-products.destroy', $item)); ?>" method="POST" class="d-inline" onsubmit="return confirm('Yakin ingin menghapus produk ini?');">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="10" class="text-center">Tidak ada data produk</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/inventory/other-products/index.blade.php ENDPATH**/ ?>