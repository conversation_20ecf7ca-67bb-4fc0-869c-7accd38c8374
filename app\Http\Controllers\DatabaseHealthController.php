<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class DatabaseHealthController extends Controller
{
    /**
     * Display database health dashboard
     */
    public function index()
    {
        // Only allow admin access
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized access to database health dashboard');
        }

        $healthData = $this->getDatabaseHealthData();
        
        return view('admin.database-health', compact('healthData'));
    }

    /**
     * Get database health data via API
     */
    public function api()
    {
        // Only allow admin access
        if (!auth()->user()->isAdmin()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        return response()->json($this->getDatabaseHealthData());
    }

    /**
     * Clear database locks manually
     */
    public function clearLocks()
    {
        // Only allow admin access
        if (!auth()->user()->isAdmin()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $result = $this->performLockClearing();
            
            return response()->json([
                'success' => true,
                'message' => 'Database locks cleared successfully',
                'result' => $result
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to clear database locks', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear database locks: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Optimize database performance
     */
    public function optimize()
    {
        // Only allow admin access
        if (!auth()->user()->isAdmin()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $result = $this->performDatabaseOptimization();
            
            return response()->json([
                'success' => true,
                'message' => 'Database optimization completed',
                'result' => $result
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to optimize database', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to optimize database: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get comprehensive database health data
     */
    private function getDatabaseHealthData(): array
    {
        $healthData = [
            'status' => 'healthy',
            'timestamp' => now(),
            'processes' => $this->getCurrentProcesses(),
            'locks' => $this->getLockInformation(),
            'performance' => $this->getPerformanceMetrics(),
            'settings' => $this->getDatabaseSettings(),
            'recommendations' => []
        ];

        // Analyze health and add recommendations
        $healthData = $this->analyzeHealthAndAddRecommendations($healthData);

        return $healthData;
    }

    /**
     * Get current database processes
     */
    private function getCurrentProcesses(): array
    {
        try {
            $processes = DB::select('SHOW PROCESSLIST');
            
            $processData = [
                'total' => count($processes),
                'active' => 0,
                'sleeping' => 0,
                'long_running' => 0,
                'details' => []
            ];

            foreach ($processes as $process) {
                if ($process->Command === 'Sleep') {
                    $processData['sleeping']++;
                } else {
                    $processData['active']++;
                }

                if ($process->Time > 10 && $process->Command !== 'Sleep') {
                    $processData['long_running']++;
                }

                $processData['details'][] = [
                    'id' => $process->Id,
                    'user' => $process->User,
                    'host' => $process->Host,
                    'db' => $process->db,
                    'command' => $process->Command,
                    'time' => $process->Time,
                    'state' => $process->State,
                    'info' => substr($process->Info ?? '', 0, 100)
                ];
            }

            return $processData;

        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get lock information
     */
    private function getLockInformation(): array
    {
        try {
            $lockData = [
                'innodb_status' => $this->getInnoDBStatus(),
                'lock_waits' => $this->getLockWaits(),
                'deadlocks' => $this->getDeadlockInfo()
            ];

            return $lockData;

        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics(): array
    {
        try {
            $metrics = DB::select("
                SHOW STATUS WHERE Variable_name IN (
                    'Threads_connected',
                    'Threads_running', 
                    'Queries',
                    'Slow_queries',
                    'Innodb_rows_read',
                    'Innodb_rows_inserted',
                    'Innodb_rows_updated',
                    'Innodb_rows_deleted',
                    'Innodb_buffer_pool_hit_rate'
                )
            ");

            $performanceData = [];
            foreach ($metrics as $metric) {
                $performanceData[$metric->Variable_name] = $metric->Value;
            }

            return $performanceData;

        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get database settings
     */
    private function getDatabaseSettings(): array
    {
        try {
            $settings = DB::select("
                SHOW VARIABLES WHERE Variable_name IN (
                    'innodb_lock_wait_timeout',
                    'lock_wait_timeout',
                    'autocommit',
                    'transaction_isolation',
                    'innodb_deadlock_detect',
                    'max_execution_time'
                )
            ");

            $settingsData = [];
            foreach ($settings as $setting) {
                $settingsData[$setting->Variable_name] = $setting->Value;
            }

            return $settingsData;

        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get InnoDB status information
     */
    private function getInnoDBStatus(): array
    {
        try {
            $status = DB::select('SHOW ENGINE INNODB STATUS');
            
            if (empty($status)) {
                return ['status' => 'No InnoDB status available'];
            }

            $statusText = $status[0]->Status;
            
            return [
                'has_deadlocks' => strpos($statusText, 'LATEST DETECTED DEADLOCK') !== false,
                'has_lock_waits' => strpos($statusText, 'LOCK WAIT') !== false,
                'transaction_count' => substr_count($statusText, 'TRANSACTION'),
                'last_check' => now()
            ];

        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get lock waits information
     */
    private function getLockWaits(): array
    {
        try {
            // This query might not work on all MySQL versions
            $lockWaits = DB::select("
                SELECT COUNT(*) as lock_wait_count
                FROM information_schema.innodb_lock_waits
            ");

            return [
                'count' => $lockWaits[0]->lock_wait_count ?? 0,
                'last_check' => now()
            ];

        } catch (\Exception $e) {
            return ['count' => 0, 'note' => 'Lock wait information not available'];
        }
    }

    /**
     * Get deadlock information
     */
    private function getDeadlockInfo(): array
    {
        // This would require parsing InnoDB status or maintaining a deadlock log
        return [
            'recent_deadlocks' => 0,
            'last_deadlock' => null,
            'note' => 'Deadlock tracking requires additional implementation'
        ];
    }

    /**
     * Analyze health data and add recommendations
     */
    private function analyzeHealthAndAddRecommendations(array $healthData): array
    {
        $recommendations = [];

        // Check for long-running processes
        if (isset($healthData['processes']['long_running']) && $healthData['processes']['long_running'] > 0) {
            $recommendations[] = [
                'type' => 'warning',
                'message' => "Found {$healthData['processes']['long_running']} long-running processes",
                'action' => 'Consider killing long-running processes'
            ];
            $healthData['status'] = 'warning';
        }

        // Check for lock waits
        if (isset($healthData['locks']['innodb_status']['has_lock_waits']) && $healthData['locks']['innodb_status']['has_lock_waits']) {
            $recommendations[] = [
                'type' => 'error',
                'message' => 'Lock waits detected in InnoDB status',
                'action' => 'Clear database locks immediately'
            ];
            $healthData['status'] = 'critical';
        }

        // Check for deadlocks
        if (isset($healthData['locks']['innodb_status']['has_deadlocks']) && $healthData['locks']['innodb_status']['has_deadlocks']) {
            $recommendations[] = [
                'type' => 'error',
                'message' => 'Recent deadlocks detected',
                'action' => 'Review application logic for deadlock prevention'
            ];
            $healthData['status'] = 'critical';
        }

        $healthData['recommendations'] = $recommendations;

        return $healthData;
    }

    /**
     * Perform lock clearing operations
     */
    private function performLockClearing(): array
    {
        $results = [];

        try {
            // Kill long-running processes
            $processes = DB::select('SHOW PROCESSLIST');
            $killedCount = 0;

            foreach ($processes as $process) {
                if ($process->Time > 30 && $process->Command !== 'Sleep') {
                    try {
                        DB::statement("KILL {$process->Id}");
                        $killedCount++;
                    } catch (\Exception $e) {
                        // Process might have already finished
                    }
                }
            }

            $results['killed_processes'] = $killedCount;

            // Clear query cache
            try {
                DB::statement('RESET QUERY CACHE');
                $results['query_cache_cleared'] = true;
            } catch (\Exception $e) {
                $results['query_cache_cleared'] = false;
            }

            // Flush tables
            try {
                DB::statement('FLUSH TABLES');
                $results['tables_flushed'] = true;
            } catch (\Exception $e) {
                $results['tables_flushed'] = false;
            }

        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Perform database optimization
     */
    private function performDatabaseOptimization(): array
    {
        $results = [];

        try {
            // Optimize tables
            $tables = ['transactions', 'transaction_items', 'processed_inventory', 'other_products', 'audit_logs'];
            
            foreach ($tables as $table) {
                try {
                    DB::statement("OPTIMIZE TABLE {$table}");
                    $results['optimized_tables'][] = $table;
                } catch (\Exception $e) {
                    $results['failed_tables'][] = $table;
                }
            }

            // Analyze tables
            foreach ($tables as $table) {
                try {
                    DB::statement("ANALYZE TABLE {$table}");
                    $results['analyzed_tables'][] = $table;
                } catch (\Exception $e) {
                    $results['failed_analysis'][] = $table;
                }
            }

        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }

        return $results;
    }
}
