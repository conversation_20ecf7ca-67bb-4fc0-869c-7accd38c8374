

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-industry"></i>
        <span>Proses Produksi</span>
    </div>

    <!-- Info Panel -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-success">
                <h5><i class="fas fa-info-circle"></i> Informasi Proses Produksi</h5>
                <p><strong>Halaman ini untuk MEMPRODUKSI produk yang sudah ada menggunakan ubi mentah.</strong></p>
                <ul class="mb-0">
                    <li>Pilih ubi mentah yang akan digunakan sebagai bahan baku</li>
                    <li>Pilih produk yang akan diproduksi (harus sudah ada di katalog)</li>
                    <li>Tentukan jumlah yang akan diproduksi</li>
                    <li>Sistem akan otomatis mengurangi stok ubi mentah dan menambah stok produk</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-industry me-2"></i>Form Proses Produksi</span>
                    <div>
                        <a href="<?php echo e(route('processed-inventory.create')); ?>" class="btn btn-info me-2">
                            <i class="fas fa-plus"></i> Tambah Produk Baru
                        </a>
                        <a href="<?php echo e(route('processed-inventory.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <h5><i class="fas fa-info-circle"></i> Informasi Proses Produksi</h5>
                        <p>Pada halaman ini, Anda dapat mengkonversi stok ubi mentah menjadi produk ubi matang. Proses ini akan:</p>
                        <ol>
                            <li>Mengurangi stok ubi mentah yang dipilih</li>
                            <li>Menambah stok produk ubi matang yang dipilih</li>
                            <li>Mencatat biaya produksi berdasarkan harga bahan baku</li>
                        </ol>
                    </div>

                    <form action="<?php echo e(route('processed-inventory.process')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-header bg-primary text-white">
                                        <i class="fas fa-seedling me-2"></i> Pilih Bahan Baku
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="raw_inventory_id" class="form-label">Ubi Mentah <span class="text-danger">*</span></label>
                                            <select class="form-select <?php $__errorArgs = ['raw_inventory_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="raw_inventory_id" name="raw_inventory_id" required>
                                                <option value="">-- Pilih Ubi Mentah --</option>
                                                <?php $__currentLoopData = $rawItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $raw): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($raw->id); ?>" <?php echo e(old('raw_inventory_id') == $raw->id ? 'selected' : ''); ?>

                                                    data-stock="<?php echo e($raw->current_stock); ?>" data-cost="<?php echo e($raw->cost_per_kg); ?>">
                                                    <?php echo e($raw->name); ?> (Stok: <?php echo e($raw->current_stock); ?> kg, Harga: Rp <?php echo e(number_format($raw->cost_per_kg, 0, ',', '.')); ?>/kg)
                                                </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <?php $__errorArgs = ['raw_inventory_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="raw_amount_used" class="form-label">Jumlah Ubi Mentah yang Digunakan (kg) <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control <?php $__errorArgs = ['raw_amount_used'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="raw_amount_used" name="raw_amount_used" value="<?php echo e(old('raw_amount_used')); ?>" step="0.1" min="0.1" required>
                                            <?php $__errorArgs = ['raw_amount_used'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            <small class="form-text text-muted">Pastikan jumlah tidak melebihi stok yang tersedia.</small>
                                        </div>
                                        
                                        <div class="alert alert-warning mb-0 raw-stock-warning" style="display: none;">
                                            <i class="fas fa-exclamation-triangle"></i> Peringatan: Jumlah yang dimasukkan melebihi stok yang tersedia.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-header bg-success text-white">
                                        <i class="fas fa-fire-alt me-2"></i> Pilih Produk Hasil
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="processed_inventory_id" class="form-label">Produk Ubi Matang <span class="text-danger">*</span></label>
                                            <select class="form-select <?php $__errorArgs = ['processed_inventory_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="processed_inventory_id" name="processed_inventory_id" required>
                                                <option value="">-- Pilih Produk Ubi Matang --</option>
                                                <?php $__currentLoopData = $processedItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $processed): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($processed->id); ?>" <?php echo e(old('processed_inventory_id') == $processed->id ? 'selected' : ''); ?>

                                                    data-rawper="<?php echo e($processed->raw_material_per_item); ?>">
                                                    <?php echo e($processed->name); ?> (<?php echo e($processed->raw_material_per_item ?? '?'); ?> kg per item)
                                                </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <?php $__errorArgs = ['processed_inventory_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="produced_amount" class="form-label">Jumlah Produk yang Dihasilkan <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control <?php $__errorArgs = ['produced_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="produced_amount" name="produced_amount" value="<?php echo e(old('produced_amount')); ?>" min="1" required>
                                            <?php $__errorArgs = ['produced_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="auto_calculate" name="auto_calculate" checked>
                                            <label class="form-check-label" for="auto_calculate">
                                                Hitung otomatis berdasarkan bahan baku
                                            </label>
                                            <small class="form-text text-muted d-block">Jika dicentang, jumlah produk akan dihitung berdasarkan bahan baku yang digunakan.</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mb-4">
                            <div class="card-header">
                                <i class="fas fa-calculator me-2"></i> Kalkulasi Biaya
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="additional_cost" class="form-label">Biaya Tambahan (Rp)</label>
                                        <input type="number" class="form-control <?php $__errorArgs = ['additional_cost'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="additional_cost" name="additional_cost" value="<?php echo e(old('additional_cost', 0)); ?>" min="0">
                                        <?php $__errorArgs = ['additional_cost'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <small class="form-text text-muted">Biaya tambahan selain bahan baku (gas, listrik, dsb)</small>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="total_raw_cost" class="form-label">Biaya Bahan Baku</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp</span>
                                            <input type="text" class="form-control" id="total_raw_cost" readonly value="0">
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="total_production_cost" class="form-label">Total Biaya Produksi</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp</span>
                                            <input type="text" class="form-control" id="total_production_cost" readonly value="0">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="cost_per_item" class="form-label">Biaya per Item</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp</span>
                                            <input type="text" class="form-control" id="cost_per_item" readonly value="0">
                                            <input type="hidden" name="cost_per_item" id="cost_per_item_hidden" value="0">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-industry"></i> Proses Produksi
                                </button>
                                <button type="reset" class="btn btn-secondary">
                                    <i class="fas fa-undo"></i> Reset
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const rawInventorySelect = document.getElementById('raw_inventory_id');
        const rawAmountInput = document.getElementById('raw_amount_used');
        const processedInventorySelect = document.getElementById('processed_inventory_id');
        const producedAmountInput = document.getElementById('produced_amount');
        const autoCalculateCheckbox = document.getElementById('auto_calculate');
        const additionalCostInput = document.getElementById('additional_cost');
        const totalRawCostDisplay = document.getElementById('total_raw_cost');
        const totalProductionCostDisplay = document.getElementById('total_production_cost');
        const costPerItemDisplay = document.getElementById('cost_per_item');
        const costPerItemHidden = document.getElementById('cost_per_item_hidden');
        const rawStockWarning = document.querySelector('.raw-stock-warning');
        
        function calculateProduction() {
            // Get selected raw inventory data
            const rawSelectedOption = rawInventorySelect.options[rawInventorySelect.selectedIndex];
            const rawStock = rawSelectedOption ? parseFloat(rawSelectedOption.dataset.stock || 0) : 0;
            const rawCostPerKg = rawSelectedOption ? parseFloat(rawSelectedOption.dataset.cost || 0) : 0;
            
            // Get selected processed inventory data
            const processedSelectedOption = processedInventorySelect.options[processedInventorySelect.selectedIndex];
            const rawPerItem = processedSelectedOption ? parseFloat(processedSelectedOption.dataset.rawper || 0) : 0;
            
            // Get input values
            const rawAmount = parseFloat(rawAmountInput.value || 0);
            const additionalCost = parseFloat(additionalCostInput.value || 0);
            
            // Check if raw amount exceeds stock
            if (rawAmount > rawStock) {
                rawStockWarning.style.display = 'block';
            } else {
                rawStockWarning.style.display = 'none';
            }
            
            // Calculate produced amount if auto-calculate is checked
            if (autoCalculateCheckbox.checked && rawPerItem > 0) {
                const calculatedProducedAmount = Math.floor(rawAmount / rawPerItem);
                producedAmountInput.value = calculatedProducedAmount > 0 ? calculatedProducedAmount : '';
                producedAmountInput.readOnly = true;
            } else {
                producedAmountInput.readOnly = false;
            }
            
            // Calculate costs
            const producedAmount = parseInt(producedAmountInput.value || 0);
            const totalRawCost = rawAmount * rawCostPerKg;
            const totalProductionCost = totalRawCost + additionalCost;
            const costPerItem = producedAmount > 0 ? totalProductionCost / producedAmount : 0;
            
            // Update displays
            totalRawCostDisplay.value = Math.round(totalRawCost).toLocaleString('id-ID');
            totalProductionCostDisplay.value = Math.round(totalProductionCost).toLocaleString('id-ID');
            costPerItemDisplay.value = Math.round(costPerItem).toLocaleString('id-ID');
            costPerItemHidden.value = Math.round(costPerItem);
        }
        
        // Add event listeners
        rawInventorySelect.addEventListener('change', calculateProduction);
        rawAmountInput.addEventListener('input', calculateProduction);
        processedInventorySelect.addEventListener('change', calculateProduction);
        producedAmountInput.addEventListener('input', calculateProduction);
        autoCalculateCheckbox.addEventListener('change', calculateProduction);
        additionalCostInput.addEventListener('input', calculateProduction);
        
        // Initial calculation
        calculateProduction();
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/inventory/processed/process.blade.php ENDPATH**/ ?>