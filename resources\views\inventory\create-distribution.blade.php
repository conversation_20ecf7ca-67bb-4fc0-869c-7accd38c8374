@extends('layouts.app')

@section('content')
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-truck"></i> Buat Distribusi untuk Produk Kadaluarsa</h1>
        <a href="{{ route('expiry-recommendations.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>Form Distribusi</h5>
                </div>
                <div class="card-body">
                    <!-- Form distribusi yang sudah diperbaiki -->
                    <div class="alert alert-success">
                        <strong>✅ Form Siap:</strong> Distribusi akan disimpan ke database
                    </div>

                    <form id="distributionForm" action="{{ route('save.distribution.data') }}" method="POST">
                        @csrf
                        <input type="hidden" name="processed_inventory_id" value="{{ $item->id }}">
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="market_name" class="form-label">Nama Pasar Tujuan</label>
                                <select class="form-select @error('market_name') is-invalid @enderror"
                                        id="market_name" name="market_name" required>
                                    <option value="">Pilih Pasar</option>
                                    @foreach($markets as $market)
                                        <option value="{{ $market }}" {{ old('market_name') == $market ? 'selected' : '' }}>
                                            {{ $market }}
                                        </option>
                                    @endforeach
                                    <option value="Pasar Baru Bandung">Pasar Baru Bandung</option>
                                    <option value="Pasar Cicadas">Pasar Cicadas</option>
                                    <option value="Pasar Kosambi">Pasar Kosambi</option>
                                    <option value="Pasar Caringin">Pasar Caringin</option>
                                </select>
                                @error('market_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="quantity" class="form-label">Jumlah Distribusi</label>
                                <input type="number" class="form-control @error('quantity') is-invalid @enderror" 
                                       id="quantity" name="quantity" value="{{ old('quantity', $item->current_stock) }}" 
                                       min="1" max="{{ $item->current_stock }}" required>
                                <small class="text-muted">Maksimal: {{ $item->current_stock }} unit</small>
                                @error('quantity')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="distribution_date" class="form-label">Tanggal Distribusi</label>
                                <input type="date" class="form-control @error('distribution_date') is-invalid @enderror" 
                                       id="distribution_date" name="distribution_date" 
                                       value="{{ old('distribution_date', date('Y-m-d')) }}" required>
                                @error('distribution_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">Catatan</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" name="notes" rows="3">{{ old('notes', 'Distribusi urgent untuk produk yang akan kadaluarsa') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ route('expiry-recommendations.index') }}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Buat Distribusi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Detail Produk</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        @if($item->image)
                            <img src="{{ asset('storage/products/' . $item->image) }}" 
                                 alt="{{ $item->name }}" class="img-fluid rounded" style="max-height: 150px;">
                        @else
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                 style="height: 150px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        @endif
                    </div>
                    
                    <table class="table table-sm">
                        <tr>
                            <td><strong>Nama Produk:</strong></td>
                            <td>{{ $item->name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Batch:</strong></td>
                            <td>{{ $item->batch_number }}</td>
                        </tr>
                        <tr>
                            <td><strong>Stok Tersedia:</strong></td>
                            <td>{{ $item->current_stock }} unit</td>
                        </tr>
                        <tr>
                            <td><strong>Tanggal Kadaluarsa:</strong></td>
                            <td>{{ $item->expiry_date ? $item->expiry_date->format('d M Y') : '-' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Harga Jual:</strong></td>
                            <td>Rp {{ number_format($item->selling_price, 0, ',', '.') }}</td>
                        </tr>
                        <tr>
                            <td><strong>Total Nilai:</strong></td>
                            <td>Rp {{ number_format($item->current_stock * $item->selling_price, 0, ',', '.') }}</td>
                        </tr>
                    </table>
                    
                    @if($item->expiry_date && $item->expiry_date->isPast())
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Sudah Kadaluarsa!</strong>
                        </div>
                    @elseif($item->expiry_date && $item->expiry_date->diffInDays() <= 3)
                        <div class="alert alert-warning">
                            <i class="fas fa-clock"></i>
                            <strong>Akan kadaluarsa dalam {{ $item->expiry_date->diffInDays() }} hari</strong>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle custom market name
    const marketSelect = document.getElementById('market_name');
    
    marketSelect.addEventListener('change', function() {
        if (this.value === 'other') {
            const customMarket = prompt('Masukkan nama pasar:');
            if (customMarket) {
                const option = new Option(customMarket, customMarket, true, true);
                this.appendChild(option);
            } else {
                this.value = '';
            }
        }
    });
    
    // Calculate total value when quantity changes
    const quantityInput = document.getElementById('quantity');
    const unitPrice = {{ $item->selling_price }};
    
    quantityInput.addEventListener('input', function() {
        const quantity = parseInt(this.value) || 0;
        const totalValue = quantity * unitPrice;
        
        // Update display if needed
        console.log('Total value:', totalValue);
    });
});
</script>
@endpush
@endsection
