<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Add optimized indexes for better query performance
        Schema::table('transactions', function (Blueprint $table) {
            // Composite index for common queries
            $table->index(['status', 'created_at'], 'idx_transactions_status_created');
            $table->index(['user_id', 'created_at'], 'idx_transactions_user_created');
            $table->index(['payment_method', 'status'], 'idx_transactions_payment_status');
            
            // Index for date range queries
            $table->index(['created_at'], 'idx_transactions_created_at');
        });

        Schema::table('transaction_items', function (Blueprint $table) {
            // Composite index for joins
            $table->index(['transaction_id', 'processed_inventory_id'], 'idx_trans_items_trans_product');
            $table->index(['processed_inventory_id'], 'idx_trans_items_processed_inv');
            $table->index(['product_id'], 'idx_trans_items_product');
        });

        Schema::table('processed_inventory', function (Blueprint $table) {
            // Index for stock queries
            $table->index(['current_stock', 'min_stock_threshold'], 'idx_processed_stock_levels');
            $table->index(['status'], 'idx_processed_status');
        });

        Schema::table('other_products', function (Blueprint $table) {
            // Index for stock queries
            $table->index(['current_stock', 'min_stock_threshold'], 'idx_other_products_stock_levels');
            $table->index(['status'], 'idx_other_products_status');
        });

        Schema::table('audit_logs', function (Blueprint $table) {
            // Composite index for audit queries
            $table->index(['model_type', 'model_id'], 'idx_audit_model');
            $table->index(['user_id', 'created_at'], 'idx_audit_user_created');
            $table->index(['action', 'created_at'], 'idx_audit_action_created');
        });

        // 2. Optimize MySQL settings for this session
        DB::statement("SET SESSION innodb_lock_wait_timeout = 5");
        DB::statement("SET SESSION lock_wait_timeout = 5");
        DB::statement("SET SESSION transaction_isolation = 'READ-COMMITTED'");
        
        // 3. Analyze tables for better query optimization
        DB::statement("ANALYZE TABLE transactions");
        DB::statement("ANALYZE TABLE transaction_items");
        DB::statement("ANALYZE TABLE processed_inventory");
        DB::statement("ANALYZE TABLE other_products");
        DB::statement("ANALYZE TABLE audit_logs");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropIndex('idx_transactions_status_created');
            $table->dropIndex('idx_transactions_user_created');
            $table->dropIndex('idx_transactions_payment_status');
            $table->dropIndex('idx_transactions_created_at');
        });

        Schema::table('transaction_items', function (Blueprint $table) {
            $table->dropIndex('idx_trans_items_trans_product');
            $table->dropIndex('idx_trans_items_processed_inv');
            $table->dropIndex('idx_trans_items_product');
        });

        Schema::table('processed_inventory', function (Blueprint $table) {
            $table->dropIndex('idx_processed_stock_levels');
            $table->dropIndex('idx_processed_status');
        });

        Schema::table('other_products', function (Blueprint $table) {
            $table->dropIndex('idx_other_products_stock_levels');
            $table->dropIndex('idx_other_products_status');
        });

        Schema::table('audit_logs', function (Blueprint $table) {
            $table->dropIndex('idx_audit_model');
            $table->dropIndex('idx_audit_user_created');
            $table->dropIndex('idx_audit_action_created');
        });
    }
};
