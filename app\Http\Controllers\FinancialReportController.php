<?php

namespace App\Http\Controllers;

use App\Models\Expense;
use App\Models\Transaction;
use App\Models\ProcessedInventory;
use App\Models\RawInventory;
use App\Models\OtherProduct;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use PDF;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\FinancialReportExport;

class FinancialReportController extends Controller
{
    /**
     * Helper function to safely format dates
     */
    private function safeFormatDate($date, $format = 'Y-m-d')
    {
        if (!$date) {
            return '';
        }

        if (is_string($date)) {
            try {
                return Carbon::parse($date)->format($format);
            } catch (\Exception $e) {
                return $date;
            }
        }

        if ($date instanceof Carbon) {
            return $date->format($format);
        }

        return '';
    }
    /**
     * Display financial report dashboard
     */
    public function index(Request $request)
    {
        $period = $request->input('period', 'monthly');
        $month = $request->input('month', Carbon::now()->month);
        $year = $request->input('year', Carbon::now()->year);

        // Handle filter type
        $filterType = $request->input('filter_type', 'monthly');
        $selectedMonth = $request->input('selected_month', Carbon::now()->format('Y-m'));
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        // Initialize dates based on filter type
        try {
            if ($filterType === 'monthly' && $selectedMonth) {
                // Parse selected month (format: Y-m)
                $monthDate = Carbon::createFromFormat('Y-m', $selectedMonth);
                $startDate = $monthDate->copy()->startOfMonth();
                $endDate = $monthDate->copy()->endOfMonth();
            } elseif ($filterType === 'custom' && $startDate && $endDate) {
                // Use custom date range - ensure they are Carbon objects
                $startDate = Carbon::parse($startDate)->startOfDay();
                $endDate = Carbon::parse($endDate)->endOfDay();
            } else {
                // Default to current month
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
                $selectedMonth = Carbon::now()->format('Y-m');
            }
        } catch (\Exception $e) {
            // If date parsing fails, use current month as fallback
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now()->endOfMonth();
            $selectedMonth = Carbon::now()->format('Y-m');
            $filterType = 'monthly';
        }

        // Ensure dates are Carbon objects for view
        if (!($startDate instanceof Carbon)) {
            $startDate = Carbon::parse($startDate);
        }
        if (!($endDate instanceof Carbon)) {
            $endDate = Carbon::parse($endDate);
        }
        
        // Calculate Revenue (from transactions)
        $revenue = Transaction::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')
            ->sum('total_amount');
            
        // Calculate Cost of Goods Sold (from processed inventory sold during period)
        $cogs = DB::table('transaction_items')
            ->join('transactions', 'transaction_items.transaction_id', '=', 'transactions.id')
            ->join('processed_inventory', 'transaction_items.processed_inventory_id', '=', 'processed_inventory.id')
            ->whereBetween('transactions.created_at', [$startDate, $endDate])
            ->select(DB::raw('SUM(transaction_items.quantity * processed_inventory.cost_per_unit) as total_cost'))
            ->first()->total_cost ?? 0;
            
        // Calculate Expenses
        $expenses = Expense::whereBetween('expense_date', [$startDate, $endDate])
            ->sum('amount');
            
        // Calculate Gross Profit
        $grossProfit = $revenue - $cogs;
        
        // Calculate Net Profit
        $netProfit = $grossProfit - $expenses;
        
        // Get expense breakdown by category
        $expensesByCategory = Expense::whereBetween('expense_date', [$startDate, $endDate])
            ->select('expense_category', DB::raw('SUM(amount) as total'))
            ->groupBy('expense_category')
            ->orderBy('total', 'desc')
            ->get();
            
        // Get daily revenue data for chart
        $dailyRevenue = Transaction::whereBetween('created_at', [$startDate, $endDate])
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('SUM(total_amount) as total'))
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        $revenueLabels = $dailyRevenue->pluck('date')->toArray();
        $revenueData = $dailyRevenue->pluck('total')->toArray();
        
        // Get monthly data for year-to-date chart
        $monthlyData = [];
        $monthlyLabels = [];
        
        if ($period === 'yearly') {
            for ($i = 1; $i <= 12; $i++) {
                $monthStart = Carbon::createFromDate($year, $i, 1)->startOfMonth();
                $monthEnd = Carbon::createFromDate($year, $i, 1)->endOfMonth();
                
                $monthlyRevenue = Transaction::whereBetween('created_at', [$monthStart, $monthEnd])
                    ->sum('total_amount');
                    
                $monthlyExpense = Expense::whereBetween('expense_date', [$monthStart, $monthEnd])
                    ->sum('amount');
                    
                $monthlyData[] = [
                    'revenue' => $monthlyRevenue,
                    'expense' => $monthlyExpense,
                    'profit' => $monthlyRevenue - $monthlyExpense
                ];
                
                $monthlyLabels[] = $monthStart->format('M');
            }
        }
        
        // Create summary data for view
        $summary = [
            'total_revenue' => $revenue,
            'total_cost' => $cogs + $expenses,
            'net_profit' => $netProfit,
            'revenue_change' => 0, // TODO: Calculate actual change
            'profit_change' => 0   // TODO: Calculate actual change
        ];

        // Create details data for view
        $grossMargin = $revenue > 0 ? (($revenue - $cogs) / $revenue) * 100 : 0;
        $netProfitMargin = $revenue > 0 ? ($netProfit / $revenue) * 100 : 0;
        $operatingProfit = $grossProfit - $expenses;

        $details = [
            'total_operating_expense' => $expenses,
            'raw_material_cost' => $cogs * 0.7, // Estimate
            'production_cost' => $cogs * 0.3,    // Estimate
            'gross_margin' => $grossMargin,
            'net_profit_margin' => $netProfitMargin,
            'gross_profit' => $grossProfit,
            'operating_profit' => $operatingProfit,
            'sales_revenue' => $revenue,
            'other_revenue' => 0,
            'salary_expense' => $expenses * 0.4, // Estimate
            'rent_expense' => $expenses * 0.2,   // Estimate
            'utility_expense' => $expenses * 0.15, // Estimate
            'marketing_expense' => $expenses * 0.15, // Estimate
            'other_expense' => $expenses * 0.1,  // Estimate
            'interest_expense' => 0,
            'profit_before_tax' => $netProfit,
            'tax_rate' => 0,
            'tax' => 0,
            'net_profit' => $netProfit
        ];

        // Create report title
        $reportTitle = '';
        if ($period === 'weekly') {
            $reportTitle = 'Mingguan - ' . $startDate->format('d M') . ' s/d ' . $endDate->format('d M Y');
        } elseif ($period === 'monthly') {
            $reportTitle = 'Bulanan - ' . $startDate->format('F Y');
        } elseif ($period === 'yearly') {
            $reportTitle = 'Tahunan - ' . $startDate->format('Y');
        } elseif ($period === 'custom') {
            $reportTitle = 'Kustom - ' . $startDate->format('d M Y') . ' s/d ' . $endDate->format('d M Y');
        }

        // Previous period data (placeholder)
        $previousRevenue = 0;
        $previousNetProfit = 0;

        // Add safe formatted dates for view
        $startDateFormatted = $this->safeFormatDate($startDate, 'Y-m-d');
        $endDateFormatted = $this->safeFormatDate($endDate, 'Y-m-d');

        return view('reports.financial', compact(
            'period',
            'month',
            'year',
            'filterType',
            'selectedMonth',
            'startDate',
            'endDate',
            'startDateFormatted',
            'endDateFormatted',
            'revenue',
            'cogs',
            'expenses',
            'grossProfit',
            'netProfit',
            'expensesByCategory',
            'revenueLabels',
            'revenueData',
            'monthlyData',
            'monthlyLabels',
            'summary',
            'details',
            'reportTitle',
            'previousRevenue',
            'previousNetProfit'
        ));
    }
    
    /**
     * Generate income statement report
     */
    public function incomeStatement(Request $request)
    {
        try {
            $startDate = Carbon::parse($request->input('start_date', Carbon::now()->startOfMonth()));
            $endDate = Carbon::parse($request->input('end_date', Carbon::now()));
        } catch (\Exception $e) {
            // Fallback to current month if date parsing fails
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now();
        }
        
        // Calculate Revenue
        $revenue = Transaction::whereBetween('created_at', [$startDate, $endDate])
            ->sum('total_amount');
            
        // Calculate Cost of Goods Sold
        $cogs = DB::table('transaction_items')
            ->join('transactions', 'transaction_items.transaction_id', '=', 'transactions.id')
            ->join('processed_inventory', 'transaction_items.processed_inventory_id', '=', 'processed_inventory.id')
            ->whereBetween('transactions.created_at', [$startDate, $endDate])
            ->select(DB::raw('SUM(transaction_items.quantity * processed_inventory.cost_per_unit) as total_cost'))
            ->first()->total_cost ?? 0;
            
        // Get expense details
        $expenses = Expense::whereBetween('expense_date', [$startDate, $endDate])
            ->select('expense_category', DB::raw('SUM(amount) as total'))
            ->groupBy('expense_category')
            ->orderBy('expense_category')
            ->get();
            
        $totalExpenses = $expenses->sum('total');
        
        // Calculate profits
        $grossProfit = $revenue - $cogs;
        $netProfit = $grossProfit - $totalExpenses;
        
        // For export
        if ($request->input('export') === 'pdf') {
            $pdf = PDF::loadView('exports.income_statement_pdf', compact(
                'startDate',
                'endDate',
                'revenue',
                'cogs',
                'expenses',
                'totalExpenses',
                'grossProfit',
                'netProfit'
            ));
            
            return $pdf->download('laporan_laba_rugi_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.pdf');
        }
        
        if ($request->input('export') === 'excel') {
            return Excel::download(new FinancialReportExport(
                $startDate,
                $endDate,
                $revenue,
                $cogs,
                $expenses,
                $totalExpenses,
                $grossProfit,
                $netProfit
            ), 'laporan_laba_rugi_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.xlsx');
        }
        
        return view('reports.income_statement', compact(
            'startDate',
            'endDate',
            'revenue',
            'cogs',
            'expenses',
            'totalExpenses',
            'grossProfit',
            'netProfit'
        ));
    }
    
    /**
     * Generate inventory valuation report
     */
    public function inventoryValuation()
    {
        // Raw inventory valuation
        $rawInventory = RawInventory::select(
                'name',
                'batch_number',
                'current_stock',
                'cost_per_kg',
                DB::raw('current_stock * cost_per_kg as total_value')
            )
            ->where('current_stock', '>', 0)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();
            
        $rawTotal = $rawInventory->sum('total_value');
        
        // Processed inventory valuation
        $processedInventory = ProcessedInventory::select(
                'name',
                'batch_number',
                'current_stock',
                'cost_per_unit',
                DB::raw('current_stock * cost_per_unit as total_value')
            )
            ->where('current_stock', '>', 0)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();
            
        $processedTotal = $processedInventory->sum('total_value');
        
        // Other products valuation
        $otherProducts = OtherProduct::select(
                'name',
                'current_stock',
                'purchase_price',
                DB::raw('current_stock * purchase_price as total_value')
            )
            ->where('current_stock', '>', 0)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();
            
        $otherTotal = $otherProducts->sum('total_value');
        
        // Grand total
        $grandTotal = $rawTotal + $processedTotal + $otherTotal;
        
        return view('reports.inventory_valuation', compact(
            'rawInventory',
            'rawTotal',
            'processedInventory',
            'processedTotal',
            'otherProducts',
            'otherTotal',
            'grandTotal'
        ));
    }
    
    /**
     * Generate financial projection
     */
    public function projection(Request $request)
    {
        $months = $request->input('months', 3);
        
        // Get average daily revenue for last 3 months
        $avgDailyRevenue = Transaction::where('created_at', '>=', Carbon::now()->subMonths(3))
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('SUM(total_amount) as total'))
            ->groupBy('date')
            ->get()
            ->avg('total');
            
        if (!$avgDailyRevenue) {
            $avgDailyRevenue = 0;
        }
        
        // Get average daily expense for last 3 months
        $avgDailyExpense = Expense::where('expense_date', '>=', Carbon::now()->subMonths(3))
            ->select(DB::raw('DATE(expense_date) as date'), DB::raw('SUM(amount) as total'))
            ->groupBy('date')
            ->get()
            ->avg('total');
            
        if (!$avgDailyExpense) {
            $avgDailyExpense = 0;
        }
        
        // Calculate COGS ratio based on historical data
        $totalRevenue = Transaction::where('created_at', '>=', Carbon::now()->subMonths(3))
            ->sum('total_amount');
            
        $totalCOGS = DB::table('transaction_items')
            ->join('transactions', 'transaction_items.transaction_id', '=', 'transactions.id')
            ->join('processed_inventory', 'transaction_items.processed_inventory_id', '=', 'processed_inventory.id')
            ->where('transactions.created_at', '>=', Carbon::now()->subMonths(3))
            ->select(DB::raw('SUM(transaction_items.quantity * processed_inventory.cost_per_unit) as total_cost'))
            ->first()->total_cost ?? 0;
            
        $cogsRatio = $totalRevenue > 0 ? $totalCOGS / $totalRevenue : 0.5; // Default to 50% if no data
        
        // Generate projections
        $projections = [];
        $labels = [];
        $revenueData = [];
        $expenseData = [];
        $profitData = [];
        
        for ($i = 0; $i < $months; $i++) {
            $month = Carbon::now()->addMonths($i);
            $daysInMonth = $month->daysInMonth;
            
            $projectedRevenue = $avgDailyRevenue * $daysInMonth;
            $projectedCOGS = $projectedRevenue * $cogsRatio;
            $projectedExpense = $avgDailyExpense * $daysInMonth;
            $projectedProfit = $projectedRevenue - $projectedCOGS - $projectedExpense;
            
            $labels[] = $month->format('M Y');
            $revenueData[] = $projectedRevenue;
            $expenseData[] = $projectedCOGS + $projectedExpense;
            $profitData[] = $projectedProfit;
            
            $projections[] = [
                'month' => $month->format('M Y'),
                'revenue' => $projectedRevenue,
                'cogs' => $projectedCOGS,
                'expense' => $projectedExpense,
                'profit' => $projectedProfit
            ];
        }
        
        return view('reports.financial-projection', compact(
            'projections',
            'months',
            'labels',
            'revenueData',
            'expenseData',
            'profitData',
            'avgDailyRevenue',
            'avgDailyExpense',
            'cogsRatio'
        ));
    }
}
