@extends('layouts.app')

@section('title', 'Daftar Distribusi')

@push('styles')
<style>
    .distribution-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    
    .distribution-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }
    
    .status-planned {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-left: 5px solid #ffc107;
    }
    
    .status-in-progress {
        background: linear-gradient(135deg, #cce5ff 0%, #a8d8ff 100%);
        border-left: 5px solid #007bff;
    }
    
    .status-completed {
        background: linear-gradient(135deg, #d4edda 0%, #a8e6a3 100%);
        border-left: 5px solid #28a745;
    }
    
    .status-cancelled {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        border-left: 5px solid #dc3545;
    }
    
    .stat-card {
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-truck text-primary"></i> Daftar Distribusi
                    </h1>
                    <p class="text-muted">Kelola dan pantau semua distribusi produk</p>
                </div>
                <div>
                    <a href="{{ route('expiry-recommendations.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Kembali ke Rekomendasi
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stat-card bg-warning text-white">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-white-50 mb-1">Direncanakan</h6>
                        <h4 class="mb-0">{{ $stats['planned'] ?? 0 }}</h4>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-clock fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stat-card bg-info text-white">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-white-50 mb-1">Dalam Proses</h6>
                        <h4 class="mb-0">{{ $stats['in_progress'] ?? 0 }}</h4>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-truck-loading fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stat-card bg-success text-white">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-white-50 mb-1">Selesai</h6>
                        <h4 class="mb-0">{{ $stats['completed'] ?? 0 }}</h4>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-check-circle fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stat-card bg-danger text-white">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-white-50 mb-1">Dibatalkan</h6>
                        <h4 class="mb-0">{{ $stats['cancelled'] ?? 0 }}</h4>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-times-circle fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter dan Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('distributions.index') }}">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">Semua Status</option>
                                    <option value="planned" {{ request('status') == 'planned' ? 'selected' : '' }}>Direncanakan</option>
                                    <option value="in_progress" {{ request('status') == 'in_progress' ? 'selected' : '' }}>Dalam Proses</option>
                                    <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Selesai</option>
                                    <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Dibatalkan</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">Dari Tanggal</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">Sampai Tanggal</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                            </div>
                            <div class="col-md-3">
                                <label for="search" class="form-label">Cari</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="search" name="search" 
                                           placeholder="Nomor distribusi atau tujuan..." value="{{ request('search') }}">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Daftar Distribusi -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> Daftar Distribusi
                    </h5>
                    <div>
                        <button class="btn btn-sm btn-success" onclick="exportDistributions()">
                            <i class="fas fa-file-excel"></i> Export Excel
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="printDistributions()">
                            <i class="fas fa-print"></i> Print PDF
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @if($distributions->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>No. Distribusi</th>
                                        <th>Produk</th>
                                        <th>Tujuan</th>
                                        <th>Jumlah</th>
                                        <th>Tanggal</th>
                                        <th>Status</th>
                                        <th>Total Nilai</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($distributions as $distribution)
                                        <tr>
                                            <td>
                                                <strong class="text-primary">{{ $distribution->distribution_number }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $distribution->created_at->format('d/m/Y H:i') }}</small>
                                            </td>
                                            <td>
                                                @foreach($distribution->items as $item)
                                                    <div class="mb-1">
                                                        <strong>{{ $item->processedInventory->name ?? 'Produk Lain' }}</strong>
                                                        @if($item->processedInventory)
                                                            <br><small class="text-muted">Batch: {{ $item->processedInventory->batch_number }}</small>
                                                        @endif
                                                    </div>
                                                @endforeach
                                            </td>
                                            <td>
                                                <i class="fas fa-map-marker-alt text-primary"></i>
                                                {{ $distribution->market_name }}
                                            </td>
                                            <td>
                                                @foreach($distribution->items as $item)
                                                    <div>{{ number_format($item->quantity) }} unit</div>
                                                @endforeach
                                            </td>
                                            <td>{{ $distribution->distribution_date->format('d/m/Y') }}</td>
                                            <td>
                                                @php
                                                    $statusClass = [
                                                        'planned' => 'warning',
                                                        'in_progress' => 'info', 
                                                        'completed' => 'success',
                                                        'cancelled' => 'danger'
                                                    ][$distribution->status] ?? 'secondary';
                                                    
                                                    $statusText = [
                                                        'planned' => 'Direncanakan',
                                                        'in_progress' => 'Dalam Proses',
                                                        'completed' => 'Selesai',
                                                        'cancelled' => 'Dibatalkan'
                                                    ][$distribution->status] ?? 'Unknown';
                                                @endphp
                                                <span class="badge bg-{{ $statusClass }}">{{ $statusText }}</span>
                                            </td>
                                            <td>
                                                <strong class="text-success">
                                                    Rp {{ number_format($distribution->items->sum('total_price')) }}
                                                </strong>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="viewDistribution({{ $distribution->id }})" 
                                                            title="Lihat Detail">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    @if($distribution->status == 'planned')
                                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                                onclick="updateStatus({{ $distribution->id }}, 'in_progress')" 
                                                                title="Mulai Distribusi">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                    @endif
                                                    @if($distribution->status == 'in_progress')
                                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                                onclick="updateStatus({{ $distribution->id }}, 'completed')" 
                                                                title="Selesaikan">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    @endif
                                                    @if(in_array($distribution->status, ['planned', 'in_progress']))
                                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                onclick="updateStatus({{ $distribution->id }}, 'cancelled')" 
                                                                title="Batalkan">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $distributions->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Belum ada distribusi</h5>
                            <p class="text-muted">Distribusi yang dibuat akan muncul di sini</p>
                            <a href="{{ route('expiry-recommendations.index') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Buat Distribusi Pertama
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail Distribusi -->
<div class="modal fade" id="distributionDetailModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle"></i> Detail Distribusi
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="distributionDetailContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function viewDistribution(id) {
    // Load distribution detail via AJAX
    fetch(`/distributions/${id}/detail`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('distributionDetailContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('distributionDetailModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Gagal memuat detail distribusi');
        });
}

function updateStatus(id, status) {
    const statusText = {
        'in_progress': 'memulai',
        'completed': 'menyelesaikan', 
        'cancelled': 'membatalkan'
    }[status];
    
    if (confirm(`Apakah Anda yakin ingin ${statusText} distribusi ini?`)) {
        fetch(`/distributions/${id}/update-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Gagal mengupdate status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan saat mengupdate status');
        });
    }
}

function exportDistributions() {
    window.location.href = '/distributions/export?' + new URLSearchParams(window.location.search);
}

function printDistributions() {
    window.open('/distributions/print?' + new URLSearchParams(window.location.search), '_blank');
}
</script>
@endpush
