<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PreventTimeout
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Set unlimited execution time for critical operations
        set_time_limit(0);
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '512M');
        
        // Increase PHP timeout settings
        ini_set('default_socket_timeout', 120);
        
        // Log the operation start
        \Log::info('Timeout prevention middleware activated', [
            'url' => $request->url(),
            'method' => $request->method(),
            'user_id' => auth()->id(),
            'ip' => $request->ip()
        ]);
        
        $startTime = microtime(true);
        
        try {
            $response = $next($request);
            
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            // Log successful operation
            \Log::info('Operation completed successfully', [
                'url' => $request->url(),
                'duration_ms' => $duration,
                'user_id' => auth()->id()
            ]);
            
            return $response;
            
        } catch (\Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            // Log failed operation
            \Log::error('Operation failed', [
                'url' => $request->url(),
                'duration_ms' => $duration,
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Check if it's a timeout error
            if (strpos($e->getMessage(), 'Maximum execution time') !== false) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'error' => 'Operasi memakan waktu terlalu lama. Silakan coba lagi.',
                        'retry_after' => 30
                    ], 408);
                }
                
                return redirect()->back()
                    ->with('error', 'Operasi memakan waktu terlalu lama. Silakan refresh halaman dan coba lagi.');
            }
            
            throw $e;
        }
    }
}
