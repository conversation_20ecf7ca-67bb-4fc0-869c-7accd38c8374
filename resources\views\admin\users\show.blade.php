@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-user"></i>
        <span>Detail User</span>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user"></i> Detail User: {{ $user->name }}
                        </h5>
                        <div>
                            @if($user->deleted_at)
                                <span class="badge bg-danger fs-6">Terhapus</span>
                            @else
                                <span class="badge bg-success fs-6">Aktif</span>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- User Avatar and Basic Info -->
                        <div class="col-md-4 text-center">
                            <div class="avatar-lg mx-auto mb-3">
                                <div class="avatar-title bg-primary rounded-circle" style="width: 120px; height: 120px; line-height: 120px; font-size: 48px;">
                                    {{ strtoupper(substr($user->name, 0, 1)) }}
                                </div>
                            </div>
                            <h4 class="mb-1">{{ $user->name }}</h4>
                            <p class="text-muted mb-3">{{ $user->email }}</p>
                            
                            <div class="mb-3">
                                <span class="badge bg-{{ $user->role === 'admin' ? 'danger' : 'primary' }} fs-6">
                                    <i class="fas fa-{{ $user->role === 'admin' ? 'user-shield' : 'user-tie' }}"></i>
                                    {{ $user->role === 'admin' ? 'Admin' : 'Karyawan' }}
                                </span>
                            </div>

                            @if(!$user->deleted_at)
                                <div class="d-grid gap-2">
                                    <a href="{{ route('users.edit', $user->id) }}" class="btn btn-warning">
                                        <i class="fas fa-edit"></i> Edit User
                                    </a>
                                    
                                    @if($user->id != auth()->id())
                                        <button type="button" class="btn btn-danger" onclick="deleteUser({{ $user->id }}, '{{ $user->name }}')">
                                            <i class="fas fa-trash"></i> Hapus User
                                        </button>
                                    @endif
                                </div>
                            @else
                                <div class="d-grid gap-2">
                                    <form action="{{ route('users.restore', $user->id) }}" method="POST">
                                        @csrf
                                        <button type="submit" class="btn btn-success w-100" onclick="return confirm('Yakin ingin memulihkan user ini?')">
                                            <i class="fas fa-undo"></i> Pulihkan User
                                        </button>
                                    </form>
                                    
                                    @if($user->role !== 'admin')
                                        <form action="{{ route('users.force-delete', $user->id) }}" method="POST">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-dark w-100" onclick="return confirm('PERINGATAN: Ini akan menghapus user secara permanen! Yakin?')">
                                                <i class="fas fa-trash-alt"></i> Hapus Permanen
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            @endif
                        </div>

                        <!-- User Details -->
                        <div class="col-md-8">
                            <h5 class="mb-3">
                                <i class="fas fa-info-circle"></i> Informasi Detail
                            </h5>
                            
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong><i class="fas fa-id-card"></i> ID User:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-secondary">{{ $user->id }}</span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong><i class="fas fa-user"></i> Nama Lengkap:</strong>
                                </div>
                                <div class="col-sm-8">
                                    {{ $user->name }}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong><i class="fas fa-envelope"></i> Email:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <a href="mailto:{{ $user->email }}" class="text-decoration-none">
                                        {{ $user->email }}
                                    </a>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong><i class="fas fa-user-tag"></i> Role:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-{{ $user->role === 'admin' ? 'danger' : 'primary' }}">
                                        {{ $user->role === 'admin' ? 'Admin' : 'Karyawan' }}
                                    </span>
                                    <small class="text-muted ms-2">
                                        @if($user->role === 'admin')
                                            (Akses penuh ke semua fitur)
                                        @else
                                            (Akses terbatas sesuai tugas)
                                        @endif
                                    </small>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong><i class="fas fa-calendar-plus"></i> Dibuat:</strong>
                                </div>
                                <div class="col-sm-8">
                                    {{ \Carbon\Carbon::parse($user->created_at)->format('d F Y, H:i') }}
                                    <small class="text-muted">
                                        ({{ \Carbon\Carbon::parse($user->created_at)->diffForHumans() }})
                                    </small>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong><i class="fas fa-calendar-edit"></i> Terakhir Update:</strong>
                                </div>
                                <div class="col-sm-8">
                                    {{ \Carbon\Carbon::parse($user->updated_at)->format('d F Y, H:i') }}
                                    <small class="text-muted">
                                        ({{ \Carbon\Carbon::parse($user->updated_at)->diffForHumans() }})
                                    </small>
                                </div>
                            </div>

                            @if($user->deleted_at)
                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <strong><i class="fas fa-calendar-times text-danger"></i> Dihapus:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="text-danger">
                                            {{ \Carbon\Carbon::parse($user->deleted_at)->format('d F Y, H:i') }}
                                            <small>
                                                ({{ \Carbon\Carbon::parse($user->deleted_at)->diffForHumans() }})
                                            </small>
                                        </span>
                                    </div>
                                </div>
                            @endif

                            <hr>

                            <h5 class="mb-3">
                                <i class="fas fa-shield-alt"></i> Hak Akses
                            </h5>
                            
                            <div class="row">
                                <div class="col-12">
                                    @if($user->role === 'admin')
                                        <div class="alert alert-danger">
                                            <i class="fas fa-crown"></i>
                                            <strong>Administrator</strong>
                                            <ul class="mb-0 mt-2">
                                                <li>Akses penuh ke semua modul sistem</li>
                                                <li>Dapat mengelola user lain</li>
                                                <li>Dapat mengakses laporan keuangan</li>
                                                <li>Dapat mengatur konfigurasi sistem</li>
                                            </ul>
                                        </div>
                                    @else
                                        <div class="alert alert-primary">
                                            <i class="fas fa-user-tie"></i>
                                            <strong>Karyawan</strong>
                                            <ul class="mb-0 mt-2">
                                                <li>Akses ke modul operasional</li>
                                                <li>Dapat mengelola inventori</li>
                                                <li>Dapat memproses transaksi</li>
                                                <li>Akses terbatas ke laporan</li>
                                            </ul>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ route('users.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali ke Daftar
                        </a>
                        
                        @if(!$user->deleted_at)
                            <a href="{{ route('users.edit', $user->id) }}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Edit User
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteUser(userId, userName) {
    if (!confirm(`Yakin ingin menghapus user "${userName}"?`)) {
        return;
    }
    
    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/users/${userId}`;
    form.style.display = 'none';
    
    // Add CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken.getAttribute('content');
        form.appendChild(csrfInput);
    }
    
    // Add method override
    const methodInput = document.createElement('input');
    methodInput.type = 'hidden';
    methodInput.name = '_method';
    methodInput.value = 'DELETE';
    form.appendChild(methodInput);
    
    // Submit form
    document.body.appendChild(form);
    form.submit();
}
</script>
@endsection
