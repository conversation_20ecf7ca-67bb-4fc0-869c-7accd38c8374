-- =====================================================
-- UBI BAKAR CILEMBU - COMPLETE DATABASE WITH DUMMY DATA
-- Database untuk Sistem Manajemen Toko Ubi Bakar Cilembu
-- Periode Data: 3 Bulan Operasional (Oktober 2024 - Desember 2024)
-- =====================================================

SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- =====================================================
-- DATABASE CREATION
-- =====================================================
CREATE DATABASE IF NOT EXISTS `ubi_bakar_cilembu` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `ubi_bakar_cilembu`;

-- =====================================================
-- TABLE STRUCTURE: users
-- =====================================================
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `role` enum('admin','employee') NOT NULL DEFAULT 'employee',
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `last_activity` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- DUMMY DATA: users
-- =====================================================
INSERT INTO `users` (`id`, `name`, `email`, `role`, `email_verified_at`, `password`, `remember_token`, `last_activity`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'Administrator', '<EMAIL>', 'admin', '2024-10-01 08:00:00', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NULL, '2024-12-31 23:59:59', '2024-10-01 08:00:00', '2024-12-31 23:59:59', NULL),
(2, 'Karyawan Toko', '<EMAIL>', 'employee', '2024-10-01 08:30:00', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NULL, '2024-12-31 20:00:00', '2024-10-01 08:30:00', '2024-12-31 20:00:00', NULL),
(3, 'Siti Nurhaliza', '<EMAIL>', 'employee', '2024-10-15 10:00:00', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NULL, '2024-12-29 15:30:00', '2024-10-15 10:00:00', '2024-12-29 15:30:00', NULL),
(4, 'Ahmad Fauzi', '<EMAIL>', 'employee', '2024-11-01 09:00:00', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NULL, '2024-12-30 18:45:00', '2024-11-01 09:00:00', '2024-12-30 18:45:00', NULL),
(5, 'Rina Marlina', '<EMAIL>', 'admin', '2024-11-15 14:00:00', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NULL, '2024-12-31 12:20:00', '2024-11-15 14:00:00', '2024-12-31 12:20:00', NULL);

-- =====================================================
-- TABLE STRUCTURE: suppliers
-- =====================================================
DROP TABLE IF EXISTS `suppliers`;
CREATE TABLE `suppliers` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `phone_number` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE STRUCTURE: raw_inventory
-- =====================================================
DROP TABLE IF EXISTS `raw_inventory`;
CREATE TABLE `raw_inventory` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `supplier_id` bigint(20) UNSIGNED DEFAULT NULL,
  `batch_number` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `supplier_name` varchar(255) DEFAULT NULL,
  `quantity_kg` decimal(10,2) NOT NULL,
  `cost_per_kg` decimal(10,2) NOT NULL,
  `total_cost` decimal(12,2) NOT NULL,
  `purchase_date` date NOT NULL,
  `expiry_date` date DEFAULT NULL,
  `quality` enum('A','B','C') NOT NULL,
  `notes` text DEFAULT NULL,
  `current_stock` decimal(10,2) NOT NULL,
  `min_stock_threshold` decimal(10,2) DEFAULT 10.00,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `raw_inventory_batch_number_unique` (`batch_number`),
  KEY `raw_inventory_supplier_id_foreign` (`supplier_id`),
  CONSTRAINT `raw_inventory_supplier_id_foreign` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE STRUCTURE: processed_inventory
-- =====================================================
DROP TABLE IF EXISTS `processed_inventory`;
CREATE TABLE `processed_inventory` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `batch_number` varchar(255) NOT NULL,
  `raw_inventory_id` bigint(20) UNSIGNED DEFAULT NULL,
  `quantity_processed_kg` decimal(10,2) NOT NULL,
  `quantity_produced` decimal(10,2) NOT NULL,
  `cost_per_unit` decimal(10,2) NOT NULL,
  `cost_per_item` decimal(10,2) DEFAULT NULL,
  `selling_price` decimal(10,2) NOT NULL,
  `raw_material_per_item` decimal(10,2) DEFAULT NULL,
  `production_date` date NOT NULL,
  `expiry_date` date NOT NULL,
  `product_type` enum('Original','Premium','Special') NOT NULL,
  `current_stock` decimal(10,2) NOT NULL,
  `min_stock_threshold` int(11) DEFAULT 5,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `priority_level` enum('low','medium','high','urgent') DEFAULT 'medium',
  `days_until_expiry` int(11) DEFAULT NULL,
  `expiry_status` enum('fresh','near_expiry','expired') DEFAULT 'fresh',
  `recommendation_action` enum('sell_normal','discount','distribute','dispose') DEFAULT 'sell_normal',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `processed_inventory_batch_number_unique` (`batch_number`),
  KEY `processed_inventory_raw_inventory_id_foreign` (`raw_inventory_id`),
  CONSTRAINT `processed_inventory_raw_inventory_id_foreign` FOREIGN KEY (`raw_inventory_id`) REFERENCES `raw_inventory` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE STRUCTURE: other_products
-- =====================================================
DROP TABLE IF EXISTS `other_products`;
CREATE TABLE `other_products` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `sku` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `purchase_price` decimal(10,2) NOT NULL,
  `selling_price` decimal(10,2) NOT NULL,
  `current_stock` int(11) NOT NULL,
  `min_stock_threshold` int(11) NOT NULL DEFAULT 5,
  `category` varchar(255) DEFAULT NULL,
  `unit` varchar(255) NOT NULL DEFAULT 'pcs',
  `supplier` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `other_products_sku_unique` (`sku`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE STRUCTURE: transactions
-- =====================================================
DROP TABLE IF EXISTS `transactions`;
CREATE TABLE `transactions` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `invoice_number` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `customer_name` varchar(255) DEFAULT NULL,
  `customer_phone` varchar(255) DEFAULT NULL,
  `customer_email` varchar(255) DEFAULT NULL,
  `customer_address` text DEFAULT NULL,
  `subtotal` decimal(12,2) NOT NULL,
  `tax` decimal(12,2) NOT NULL DEFAULT 0.00,
  `discount` decimal(12,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(12,2) NOT NULL,
  `amount_paid` decimal(12,2) NOT NULL,
  `change_amount` decimal(12,2) NOT NULL DEFAULT 0.00,
  `payment_method` enum('cash','transfer','qris','debit','credit','midtrans') NOT NULL,
  `payment_gateway_transaction_id` varchar(255) DEFAULT NULL,
  `payment_gateway_status` varchar(255) DEFAULT NULL,
  `payment_gateway_response` text DEFAULT NULL,
  `status` enum('completed','cancelled','refunded','pending') NOT NULL DEFAULT 'completed',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transactions_invoice_number_unique` (`invoice_number`),
  KEY `transactions_user_id_foreign` (`user_id`),
  CONSTRAINT `transactions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE STRUCTURE: transaction_items
-- =====================================================
DROP TABLE IF EXISTS `transaction_items`;
CREATE TABLE `transaction_items` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `transaction_id` bigint(20) UNSIGNED NOT NULL,
  `product_id` bigint(20) UNSIGNED NOT NULL,
  `product_name` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `quantity` int(11) NOT NULL,
  `discount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `subtotal` decimal(12,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transaction_items_transaction_id_foreign` (`transaction_id`),
  KEY `transaction_items_product_id_foreign` (`product_id`),
  CONSTRAINT `transaction_items_transaction_id_foreign` FOREIGN KEY (`transaction_id`) REFERENCES `transactions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `transaction_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `processed_inventory` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE STRUCTURE: distributions
-- =====================================================
DROP TABLE IF EXISTS `distributions`;
CREATE TABLE `distributions` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `distribution_number` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `market_name` varchar(255) NOT NULL,
  `distribution_date` date NOT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('planned','in_transit','delivered','returned') NOT NULL DEFAULT 'planned',
  `is_urgent` tinyint(1) NOT NULL DEFAULT 0,
  `urgency_reason` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `distributions_distribution_number_unique` (`distribution_number`),
  KEY `distributions_user_id_foreign` (`user_id`),
  CONSTRAINT `distributions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE STRUCTURE: distribution_items
-- =====================================================
DROP TABLE IF EXISTS `distribution_items`;
CREATE TABLE `distribution_items` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `distribution_id` bigint(20) UNSIGNED NOT NULL,
  `processed_inventory_id` bigint(20) UNSIGNED DEFAULT NULL,
  `other_product_id` bigint(20) UNSIGNED DEFAULT NULL,
  `quantity` int(11) NOT NULL,
  `price_per_item` decimal(10,2) NOT NULL,
  `total_price` decimal(12,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `distribution_items_distribution_id_foreign` (`distribution_id`),
  KEY `distribution_items_processed_inventory_id_foreign` (`processed_inventory_id`),
  KEY `distribution_items_other_product_id_foreign` (`other_product_id`),
  CONSTRAINT `distribution_items_distribution_id_foreign` FOREIGN KEY (`distribution_id`) REFERENCES `distributions` (`id`),
  CONSTRAINT `distribution_items_processed_inventory_id_foreign` FOREIGN KEY (`processed_inventory_id`) REFERENCES `processed_inventory` (`id`) ON DELETE SET NULL,
  CONSTRAINT `distribution_items_other_product_id_foreign` FOREIGN KEY (`other_product_id`) REFERENCES `other_products` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABLE STRUCTURE: production_logs
-- =====================================================
DROP TABLE IF EXISTS `production_logs`;
CREATE TABLE `production_logs` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `raw_inventory_id` bigint(20) UNSIGNED NOT NULL,
  `processed_inventory_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `raw_amount_used` decimal(10,2) NOT NULL COMMENT 'dalam kg',
  `produced_amount` int(11) NOT NULL COMMENT 'jumlah item yang dihasilkan',
  `raw_cost` decimal(12,2) NOT NULL COMMENT 'biaya bahan mentah',
  `additional_cost` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'biaya tambahan',
  `total_cost` decimal(12,2) NOT NULL COMMENT 'total biaya produksi',
  `cost_per_item` decimal(12,2) NOT NULL COMMENT 'biaya per item',
  `raw_name` varchar(255) NOT NULL COMMENT 'nama bahan mentah saat produksi',
  `processed_name` varchar(255) NOT NULL COMMENT 'nama produk saat produksi',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `production_logs_raw_inventory_id_foreign` (`raw_inventory_id`),
  KEY `production_logs_processed_inventory_id_foreign` (`processed_inventory_id`),
  KEY `production_logs_user_id_foreign` (`user_id`),
  CONSTRAINT `production_logs_raw_inventory_id_foreign` FOREIGN KEY (`raw_inventory_id`) REFERENCES `raw_inventory` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `production_logs_processed_inventory_id_foreign` FOREIGN KEY (`processed_inventory_id`) REFERENCES `processed_inventory` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `production_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- DUMMY DATA: suppliers
-- =====================================================
INSERT INTO `suppliers` (`id`, `name`, `contact_person`, `phone_number`, `email`, `address`, `notes`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'CV. Ubi Cilembu Sejahtera', 'Bapak Suherman', '081234567890', '<EMAIL>', 'Jl. Raya Cilembu No. 123, Sumedang, Jawa Barat', 'Supplier utama ubi cilembu kualitas A', 1, '2024-10-01 08:00:00', '2024-10-01 08:00:00'),
(2, 'Tani Makmur Cilembu', 'Ibu Sari Dewi', '082345678901', '<EMAIL>', 'Desa Cilembu, Kec. Pamulihan, Sumedang', 'Supplier ubi organik berkualitas tinggi', 1, '2024-10-01 08:30:00', '2024-10-01 08:30:00'),
(3, 'Koperasi Tani Cilembu', 'Bapak Ahmad Yusuf', '083456789012', '<EMAIL>', 'Jl. Cilembu Raya No. 45, Sumedang', 'Koperasi petani lokal dengan harga bersaing', 1, '2024-10-01 09:00:00', '2024-10-01 09:00:00'),
(4, 'UD. Berkah Tani', 'Bapak Dedi Kurniawan', '084567890123', '<EMAIL>', 'Kampung Cilembu, Sumedang', 'Supplier ubi grade B dan C untuk produksi massal', 1, '2024-10-01 09:30:00', '2024-10-01 09:30:00');

-- =====================================================
-- DUMMY DATA: raw_inventory (Oktober 2024 - Desember 2024)
-- =====================================================
INSERT INTO `raw_inventory` (`id`, `supplier_id`, `batch_number`, `name`, `supplier_name`, `quantity_kg`, `cost_per_kg`, `total_cost`, `purchase_date`, `expiry_date`, `quality`, `notes`, `current_stock`, `min_stock_threshold`, `is_active`, `created_at`, `updated_at`) VALUES
-- Oktober 2024
(1, 1, 'RAW-2024-10-001', 'Ubi Cilembu Grade A', 'CV. Ubi Cilembu Sejahtera', 500.00, 15000.00, 7500000.00, '2024-10-01', '2024-12-01', 'A', 'Ubi cilembu premium kualitas ekspor', 45.50, 10.00, 1, '2024-10-01 10:00:00', '2024-12-15 14:30:00'),
(2, 2, 'RAW-2024-10-002', 'Ubi Cilembu Organik', 'Tani Makmur Cilembu', 300.00, 18000.00, 5400000.00, '2024-10-03', '2024-12-03', 'A', 'Ubi organik tanpa pestisida', 25.75, 10.00, 1, '2024-10-03 11:00:00', '2024-12-10 16:20:00'),
(3, 3, 'RAW-2024-10-003', 'Ubi Cilembu Grade B', 'Koperasi Tani Cilembu', 400.00, 12000.00, 4800000.00, '2024-10-05', '2024-11-30', 'B', 'Ubi kualitas menengah untuk produksi reguler', 0.00, 10.00, 1, '2024-10-05 09:30:00', '2024-11-28 10:15:00'),
(4, 4, 'RAW-2024-10-004', 'Ubi Cilembu Grade C', 'UD. Berkah Tani', 600.00, 8000.00, 4800000.00, '2024-10-08', '2024-11-25', 'C', 'Ubi untuk produksi massal harga ekonomis', 0.00, 10.00, 1, '2024-10-08 08:00:00', '2024-11-23 12:45:00'),
(5, 1, 'RAW-2024-10-005', 'Ubi Cilembu Grade A', 'CV. Ubi Cilembu Sejahtera', 450.00, 15500.00, 6975000.00, '2024-10-12', '2024-12-12', 'A', 'Batch kedua ubi premium bulan Oktober', 120.25, 10.00, 1, '2024-10-12 14:00:00', '2024-12-18 09:10:00'),
(6, 2, 'RAW-2024-10-006', 'Ubi Cilembu Organik', 'Tani Makmur Cilembu', 350.00, 17500.00, 6125000.00, '2024-10-15', '2024-12-15', 'A', 'Ubi organik harvest kedua', 89.50, 10.00, 1, '2024-10-15 10:30:00', '2024-12-20 11:25:00'),
(7, 3, 'RAW-2024-10-007', 'Ubi Cilembu Grade B', 'Koperasi Tani Cilembu', 500.00, 11500.00, 5750000.00, '2024-10-18', '2024-12-05', 'B', 'Stok tambahan untuk memenuhi permintaan tinggi', 78.75, 10.00, 1, '2024-10-18 13:15:00', '2024-12-22 15:40:00'),
(8, 4, 'RAW-2024-10-008', 'Ubi Cilembu Grade C', 'UD. Berkah Tani', 700.00, 7500.00, 5250000.00, '2024-10-22', '2024-12-01', 'C', 'Batch besar untuk produksi akhir bulan', 156.25, 10.00, 1, '2024-10-22 16:45:00', '2024-12-25 08:30:00'),
(9, 1, 'RAW-2024-10-009', 'Ubi Cilembu Grade A', 'CV. Ubi Cilembu Sejahtera', 400.00, 16000.00, 6400000.00, '2024-10-25', '2024-12-25', 'A', 'Persiapan stok untuk bulan November', 234.50, 10.00, 1, '2024-10-25 12:20:00', '2024-12-28 14:15:00'),
(10, 2, 'RAW-2024-10-010', 'Ubi Cilembu Organik', 'Tani Makmur Cilembu', 320.00, 18500.00, 5920000.00, '2024-10-28', '2024-12-28', 'A', 'Ubi organik premium akhir Oktober', 198.75, 10.00, 1, '2024-10-28 09:45:00', '2024-12-30 16:50:00'),

-- November 2024
(11, 3, 'RAW-2024-11-001', 'Ubi Cilembu Grade B', 'Koperasi Tani Cilembu', 550.00, 12500.00, 6875000.00, '2024-11-02', '2025-01-02', 'B', 'Stok awal November kualitas menengah', 267.25, 10.00, 1, '2024-11-02 08:30:00', '2024-12-31 10:20:00'),
(12, 4, 'RAW-2024-11-002', 'Ubi Cilembu Grade C', 'UD. Berkah Tani', 800.00, 8200.00, 6560000.00, '2024-11-05', '2025-01-05', 'C', 'Batch besar untuk produksi November', 445.50, 10.00, 1, '2024-11-05 11:15:00', '2025-01-02 13:40:00'),
(13, 1, 'RAW-2024-11-003', 'Ubi Cilembu Grade A', 'CV. Ubi Cilembu Sejahtera', 480.00, 15800.00, 7584000.00, '2024-11-08', '2025-01-08', 'A', 'Ubi premium untuk produk special', 312.75, 10.00, 1, '2024-11-08 14:45:00', '2025-01-05 09:25:00'),
(14, 2, 'RAW-2024-11-004', 'Ubi Cilembu Organik', 'Tani Makmur Cilembu', 380.00, 19000.00, 7220000.00, '2024-11-12', '2025-01-12', 'A', 'Ubi organik untuk produk premium', 189.25, 10.00, 1, '2024-11-12 10:00:00', '2025-01-08 15:30:00'),
(15, 3, 'RAW-2024-11-005', 'Ubi Cilembu Grade B', 'Koperasi Tani Cilembu', 600.00, 11800.00, 7080000.00, '2024-11-15', '2025-01-15', 'B', 'Stok tengah November', 378.50, 10.00, 1, '2024-11-15 13:20:00', '2025-01-12 11:45:00'),

-- Desember 2024
(16, 4, 'RAW-2024-12-001', 'Ubi Cilembu Grade C', 'UD. Berkah Tani', 900.00, 8500.00, 7650000.00, '2024-12-01', '2025-02-01', 'C', 'Stok besar awal Desember', 678.25, 10.00, 1, '2024-12-01 08:00:00', '2025-01-28 12:20:00'),
(17, 1, 'RAW-2024-12-002', 'Ubi Cilembu Grade A', 'CV. Ubi Cilembu Sejahtera', 500.00, 16500.00, 8250000.00, '2024-12-05', '2025-02-05', 'A', 'Ubi premium untuk produksi Desember', 389.50, 10.00, 1, '2024-12-05 11:30:00', '2025-02-01 09:45:00'),
(18, 2, 'RAW-2024-12-003', 'Ubi Cilembu Organik', 'Tani Makmur Cilembu', 400.00, 19500.00, 7800000.00, '2024-12-08', '2025-02-08', 'A', 'Ubi organik premium Desember', 298.75, 10.00, 1, '2024-12-08 14:15:00', '2025-02-05 13:30:00'),
(19, 3, 'RAW-2024-12-004', 'Ubi Cilembu Grade B', 'Koperasi Tani Cilembu', 650.00, 12800.00, 8320000.00, '2024-12-12', '2025-02-12', 'B', 'Stok tengah Desember', 534.25, 10.00, 1, '2024-12-12 10:45:00', '2025-02-08 15:20:00'),
(20, 4, 'RAW-2024-12-005', 'Ubi Cilembu Grade C', 'UD. Berkah Tani', 850.00, 8300.00, 7055000.00, '2024-12-15', '2025-02-15', 'C', 'Batch kedua Desember', 723.50, 10.00, 1, '2024-12-15 13:50:00', '2025-02-12 11:40:00');

-- =====================================================
-- DUMMY DATA: processed_inventory (Oktober 2024 - Desember 2024)
-- =====================================================
INSERT INTO `processed_inventory` (`id`, `name`, `batch_number`, `raw_inventory_id`, `quantity_processed_kg`, `quantity_produced`, `cost_per_unit`, `cost_per_item`, `selling_price`, `raw_material_per_item`, `production_date`, `expiry_date`, `product_type`, `current_stock`, `min_stock_threshold`, `is_active`, `priority_level`, `days_until_expiry`, `expiry_status`, `recommendation_action`, `notes`, `created_at`, `updated_at`) VALUES
-- Oktober 2024 - Produksi dari Raw Inventory
(1, 'Ubi Bakar Original XL', 'PROC-2024-10-001', 1, 100.00, 400, 18750.00, 18750.00, 25000.00, 0.25, '2024-10-02', '2024-11-01', 'Original', 12, 5, 1, 'urgent', 2, 'near_expiry', 'distribute', 'Produksi pertama dari ubi grade A', '2024-10-02 14:00:00', '2024-12-30 10:15:00'),
(2, 'Ubi Bakar Premium XL', 'PROC-2024-10-002', 2, 80.00, 280, 19285.71, 19285.71, 28000.00, 0.29, '2024-10-04', '2024-11-03', 'Premium', 8, 5, 1, 'urgent', 4, 'near_expiry', 'distribute', 'Produksi premium dari ubi organik', '2024-10-04 15:30:00', '2024-12-30 11:20:00'),
(3, 'Ubi Bakar Original L', 'PROC-2024-10-003', 3, 120.00, 600, 9600.00, 9600.00, 15000.00, 0.20, '2024-10-06', '2024-10-30', 'Original', 0, 5, 1, 'urgent', -1, 'expired', 'dispose', 'Produksi dari ubi grade B - EXPIRED', '2024-10-06 09:45:00', '2024-10-31 16:30:00'),
(4, 'Ubi Bakar Special M', 'PROC-2024-10-004', 4, 150.00, 900, 5333.33, 5333.33, 8000.00, 0.17, '2024-10-09', '2024-10-25', 'Special', 0, 5, 1, 'urgent', -15, 'expired', 'dispose', 'Produksi massal grade C - EXPIRED', '2024-10-09 11:20:00', '2024-10-26 14:45:00'),
(5, 'Ubi Bakar Original XL', 'PROC-2024-10-005', 5, 90.00, 360, 19375.00, 19375.00, 26000.00, 0.25, '2024-10-13', '2024-11-12', 'Original', 45, 5, 1, 'high', 13, 'near_expiry', 'discount', 'Batch kedua Oktober ubi premium', '2024-10-13 16:15:00', '2024-12-30 12:30:00'),
(6, 'Ubi Bakar Premium XL', 'PROC-2024-10-006', 6, 85.00, 300, 20416.67, 20416.67, 29000.00, 0.28, '2024-10-16', '2024-11-15', 'Premium', 67, 5, 1, 'medium', 16, 'near_expiry', 'discount', 'Premium organik batch kedua', '2024-10-16 13:45:00', '2024-12-30 13:40:00'),
(7, 'Ubi Bakar Original L', 'PROC-2024-10-007', 7, 110.00, 550, 10454.55, 10454.55, 16000.00, 0.20, '2024-10-19', '2024-11-18', 'Original', 89, 5, 1, 'medium', 19, 'near_expiry', 'discount', 'Produksi reguler grade B', '2024-10-19 10:30:00', '2024-12-30 14:50:00'),
(8, 'Ubi Bakar Special M', 'PROC-2024-10-008', 8, 140.00, 840, 6250.00, 6250.00, 9000.00, 0.17, '2024-10-23', '2024-11-22', 'Special', 156, 5, 1, 'medium', 23, 'fresh', 'sell_normal', 'Produksi massal akhir Oktober', '2024-10-23 14:20:00', '2024-12-30 15:25:00'),
(9, 'Ubi Bakar Original XL', 'PROC-2024-10-009', 9, 95.00, 380, 16842.11, 16842.11, 24000.00, 0.25, '2024-10-26', '2024-11-25', 'Original', 234, 5, 1, 'medium', 26, 'fresh', 'sell_normal', 'Persiapan stok November', '2024-10-26 12:10:00', '2024-12-30 16:35:00'),
(10, 'Ubi Bakar Premium XL', 'PROC-2024-10-010', 10, 75.00, 270, 21851.85, 21851.85, 30000.00, 0.28, '2024-10-29', '2024-11-28', 'Premium', 198, 5, 1, 'low', 29, 'fresh', 'sell_normal', 'Premium organik akhir Oktober', '2024-10-29 15:50:00', '2024-12-30 17:45:00'),

-- November 2024 - Produksi Lanjutan
(11, 'Ubi Bakar Original L', 'PROC-2024-11-001', 11, 125.00, 625, 11000.00, 11000.00, 17000.00, 0.20, '2024-11-03', '2024-12-03', 'Original', 267, 5, 1, 'low', 34, 'fresh', 'sell_normal', 'Produksi awal November grade B', '2024-11-03 09:15:00', '2024-12-30 18:20:00'),
(12, 'Ubi Bakar Special M', 'PROC-2024-11-002', 12, 160.00, 960, 6833.33, 6833.33, 10000.00, 0.17, '2024-11-06', '2024-12-06', 'Special', 445, 5, 1, 'low', 37, 'fresh', 'sell_normal', 'Produksi massal November', '2024-11-06 11:40:00', '2024-12-30 19:10:00'),
(13, 'Ubi Bakar Original XL', 'PROC-2024-11-003', 13, 100.00, 400, 18960.00, 18960.00, 27000.00, 0.25, '2024-11-09', '2024-12-09', 'Original', 312, 5, 1, 'low', 40, 'fresh', 'sell_normal', 'Premium grade A November', '2024-11-09 14:25:00', '2024-12-30 20:30:00'),
(14, 'Ubi Bakar Premium XL', 'PROC-2024-11-004', 14, 90.00, 315, 22920.63, 22920.63, 32000.00, 0.29, '2024-11-13', '2024-12-13', 'Premium', 189, 5, 1, 'low', 44, 'fresh', 'sell_normal', 'Premium organik November', '2024-11-13 16:45:00', '2024-12-30 21:15:00'),
(15, 'Ubi Bakar Original L', 'PROC-2024-11-005', 15, 130.00, 650, 10892.31, 10892.31, 16500.00, 0.20, '2024-11-16', '2024-12-16', 'Original', 378, 5, 1, 'low', 47, 'fresh', 'sell_normal', 'Produksi tengah November', '2024-11-16 10:20:00', '2024-12-30 22:40:00'),

-- Desember 2024 - Produksi Akhir Tahun
(16, 'Ubi Bakar Special M', 'PROC-2024-12-001', 16, 170.00, 1020, 7500.00, 7500.00, 11000.00, 0.17, '2024-12-02', '2025-01-01', 'Special', 678, 5, 1, 'low', 63, 'fresh', 'sell_normal', 'Produksi besar awal Desember', '2024-12-02 08:45:00', '2024-12-31 11:15:00'),
(17, 'Ubi Bakar Original XL', 'PROC-2024-12-002', 17, 105.00, 420, 19642.86, 19642.86, 28000.00, 0.25, '2024-12-06', '2025-01-05', 'Original', 389, 5, 1, 'low', 67, 'fresh', 'sell_normal', 'Premium Desember grade A', '2024-12-06 11:30:00', '2024-12-31 12:45:00'),
(18, 'Ubi Bakar Premium XL', 'PROC-2024-12-003', 18, 95.00, 333, 23423.42, 23423.42, 34000.00, 0.29, '2024-12-09', '2025-01-08', 'Premium', 298, 5, 1, 'low', 70, 'fresh', 'sell_normal', 'Premium organik Desember', '2024-12-09 14:15:00', '2024-12-31 13:30:00'),
(19, 'Ubi Bakar Original L', 'PROC-2024-12-004', 19, 135.00, 675, 12325.93, 12325.93, 18000.00, 0.20, '2024-12-13', '2025-01-12', 'Original', 534, 5, 1, 'low', 74, 'fresh', 'sell_normal', 'Produksi tengah Desember', '2024-12-13 10:45:00', '2024-12-31 14:20:00'),
(20, 'Ubi Bakar Special M', 'PROC-2024-12-005', 20, 155.00, 930, 7586.02, 7586.02, 11500.00, 0.17, '2024-12-16', '2025-01-15', 'Special', 723, 5, 1, 'low', 77, 'fresh', 'sell_normal', 'Batch kedua Desember', '2024-12-16 13:50:00', '2024-12-31 15:40:00');

-- =====================================================
-- DUMMY DATA: other_products
-- =====================================================
INSERT INTO `other_products` (`id`, `name`, `sku`, `description`, `purchase_price`, `selling_price`, `current_stock`, `min_stock_threshold`, `category`, `unit`, `supplier`, `is_active`, `notes`, `created_at`, `updated_at`) VALUES
(1, 'Plastik Kemasan Ubi', 'PKG-001', 'Plastik food grade untuk kemasan ubi bakar', 500.00, 750.00, 2500, 100, 'Kemasan', 'pcs', 'CV. Plastik Jaya', 1, 'Plastik transparan food grade', '2024-10-01 08:00:00', '2024-12-31 20:00:00'),
(2, 'Label Stiker Ubi Cilembu', 'LBL-001', 'Stiker label dengan logo dan informasi produk', 200.00, 350.00, 5000, 200, 'Kemasan', 'pcs', 'Percetakan Maju', 1, 'Label waterproof dengan logo toko', '2024-10-01 08:30:00', '2024-12-31 20:15:00'),
(3, 'Kardus Kemasan Besar', 'BOX-001', 'Kardus untuk kemasan 10 ubi bakar', 2500.00, 4000.00, 500, 25, 'Kemasan', 'pcs', 'Karton Sejahtera', 1, 'Kardus food grade untuk pengiriman', '2024-10-01 09:00:00', '2024-12-31 20:30:00'),
(4, 'Teh Botol Sosro', 'DRK-001', 'Minuman teh botol 350ml', 3500.00, 5000.00, 240, 50, 'Minuman', 'botol', 'Distributor Sosro', 1, 'Minuman pelengkap untuk pelanggan', '2024-10-01 09:30:00', '2024-12-31 20:45:00'),
(5, 'Air Mineral Aqua 600ml', 'DRK-002', 'Air mineral dalam kemasan 600ml', 2000.00, 3000.00, 360, 100, 'Minuman', 'botol', 'Distributor Aqua', 1, 'Air mineral untuk pelanggan', '2024-10-01 10:00:00', '2024-12-31 21:00:00'),
(6, 'Kopi Sachet Kapal Api', 'DRK-003', 'Kopi instan sachet 20gr', 1500.00, 2500.00, 180, 50, 'Minuman', 'sachet', 'Toko Grosir Maju', 1, 'Kopi pelengkap untuk pelanggan', '2024-10-01 10:30:00', '2024-12-31 21:15:00'),
(7, 'Tissue Makan', 'ACC-001', 'Tissue untuk makan, 1 pack isi 50 lembar', 3000.00, 5000.00, 120, 20, 'Aksesoris', 'pack', 'Supplier Tissue', 1, 'Tissue berkualitas untuk pelanggan', '2024-10-01 11:00:00', '2024-12-31 21:30:00'),
(8, 'Sendok Plastik', 'ACC-002', 'Sendok plastik sekali pakai', 100.00, 200.00, 1000, 100, 'Aksesoris', 'pcs', 'Plastik Jaya', 1, 'Sendok untuk makan ubi bakar', '2024-10-01 11:30:00', '2024-12-31 21:45:00'),
(9, 'Sambal Sachet', 'SPC-001', 'Sambal kemasan sachet 10ml', 500.00, 1000.00, 500, 50, 'Bumbu', 'sachet', 'Pabrik Sambal Nusantara', 1, 'Sambal pelengkap ubi bakar', '2024-10-01 12:00:00', '2024-12-31 22:00:00'),
(10, 'Keju Parut Sachet', 'SPC-002', 'Keju parut kemasan sachet 5gr', 1000.00, 2000.00, 300, 30, 'Bumbu', 'sachet', 'Dairy Products', 1, 'Topping keju untuk ubi premium', '2024-10-01 12:30:00', '2024-12-31 22:15:00');

-- =====================================================
-- DUMMY DATA: transactions (Sample dari 3 bulan operasional)
-- =====================================================
INSERT INTO `transactions` (`id`, `invoice_number`, `user_id`, `customer_name`, `customer_phone`, `customer_email`, `customer_address`, `subtotal`, `tax`, `discount`, `total_amount`, `amount_paid`, `change_amount`, `payment_method`, `payment_gateway_transaction_id`, `payment_gateway_status`, `payment_gateway_response`, `status`, `notes`, `created_at`, `updated_at`) VALUES
-- Oktober 2024 - Sample Transactions
(1, 'INV-2024-10-001', 1, 'Budi Santoso', '081234567890', '<EMAIL>', 'Jl. Merdeka No. 123, Bandung', 75000.00, 0.00, 0.00, 75000.00, 75000.00, 0.00, 'cash', NULL, NULL, NULL, 'completed', 'Pelanggan reguler', '2024-10-01 10:30:00', '2024-10-01 10:30:00'),
(2, 'INV-2024-10-002', 1, 'Siti Nurhaliza', '082345678901', NULL, NULL, 140000.00, 0.00, 5000.00, 135000.00, 150000.00, 15000.00, 'cash', NULL, NULL, NULL, 'completed', 'Diskon member', '2024-10-01 14:15:00', '2024-10-01 14:15:00'),
(3, 'INV-2024-10-003', 1, 'Ahmad Fauzi', '083456789012', '<EMAIL>', 'Jl. Sudirman No. 45, Jakarta', 280000.00, 0.00, 0.00, 280000.00, 280000.00, 0.00, 'transfer', 'TRF-001', 'success', '{"status":"success","amount":280000}', 'completed', 'Pembelian grosir', '2024-10-01 16:45:00', '2024-10-01 16:45:00'),
(4, 'INV-2024-10-004', 1, 'Rina Marlina', '084567890123', NULL, NULL, 56000.00, 0.00, 0.00, 56000.00, 60000.00, 4000.00, 'cash', NULL, NULL, NULL, 'completed', 'Pembelian keluarga', '2024-10-02 09:20:00', '2024-10-02 09:20:00'),
(5, 'INV-2024-10-005', 1, 'Dedi Kurniawan', '085678901234', '<EMAIL>', 'Jl. Asia Afrika No. 78, Bandung', 168000.00, 0.00, 8000.00, 160000.00, 160000.00, 0.00, 'qris', 'QRIS-001', 'success', '{"status":"success","amount":160000}', 'completed', 'Pembayaran QRIS', '2024-10-02 11:30:00', '2024-10-02 11:30:00'),

-- November 2024 - Sample Transactions
(6, 'INV-2024-11-001', 1, 'Agus Salim', '************', '<EMAIL>', 'Jl. Veteran No. 89, Yogyakarta', 168000.00, 0.00, 0.00, 168000.00, 168000.00, 0.00, 'transfer', 'TRF-003', 'success', '{"status":"success","amount":168000}', 'completed', 'Transfer bank', '2024-11-01 09:30:00', '2024-11-01 09:30:00'),
(7, 'INV-2024-11-002', 1, 'Ratna Sari', '************', NULL, NULL, 84000.00, 0.00, 4000.00, 80000.00, 80000.00, 0.00, 'cash', NULL, NULL, NULL, 'completed', 'Promo November', '2024-11-01 13:45:00', '2024-11-01 13:45:00'),
(8, 'INV-2024-11-003', 1, 'Joko Widodo', '************', '<EMAIL>', 'Jl. Thamrin No. 34, Jakarta', 252000.00, 0.00, 0.00, 252000.00, 252000.00, 0.00, 'qris', 'QRIS-003', 'success', '{"status":"success","amount":252000}', 'completed', 'Pesanan khusus', '2024-11-02 11:20:00', '2024-11-02 11:20:00'),

-- Desember 2024 - Sample Transactions
(9, 'INV-2024-12-001', 1, 'Putri Maharani', '************', NULL, NULL, 140000.00, 0.00, 0.00, 140000.00, 140000.00, 0.00, 'cash', NULL, NULL, NULL, 'completed', 'Pembelian Desember', '2024-12-01 10:45:00', '2024-12-01 10:45:00'),
(10, 'INV-2024-12-002', 1, 'Ridwan Kamil', '************', '<EMAIL>', 'Jl. Braga No. 23, Bandung', 280000.00, 0.00, 0.00, 280000.00, 280000.00, 0.00, 'qris', 'QRIS-004', 'success', '{"status":"success","amount":280000}', 'completed', 'Pesanan akhir tahun', '2024-12-01 15:20:00', '2024-12-01 15:20:00');

-- =====================================================
-- DUMMY DATA: transaction_items
-- =====================================================
INSERT INTO `transaction_items` (`id`, `transaction_id`, `product_id`, `product_name`, `price`, `quantity`, `discount`, `subtotal`, `created_at`, `updated_at`) VALUES
-- Items untuk transaksi Oktober
(1, 1, 1, 'Ubi Bakar Original XL', 25000.00, 3, 0.00, 75000.00, '2024-10-01 10:30:00', '2024-10-01 10:30:00'),
(2, 2, 2, 'Ubi Bakar Premium XL', 28000.00, 5, 5000.00, 135000.00, '2024-10-01 14:15:00', '2024-10-01 14:15:00'),
(3, 3, 1, 'Ubi Bakar Original XL', 25000.00, 8, 0.00, 200000.00, '2024-10-01 16:45:00', '2024-10-01 16:45:00'),
(4, 3, 3, 'Ubi Bakar Original L', 15000.00, 4, 0.00, 60000.00, '2024-10-01 16:45:00', '2024-10-01 16:45:00'),
(5, 3, 4, 'Ubi Bakar Special M', 8000.00, 2, 0.00, 16000.00, '2024-10-01 16:45:00', '2024-10-01 16:45:00'),
(6, 3, 4, 'Ubi Bakar Special M', 8000.00, 1, 0.00, 4000.00, '2024-10-01 16:45:00', '2024-10-01 16:45:00'),
(7, 4, 3, 'Ubi Bakar Original L', 15000.00, 2, 0.00, 30000.00, '2024-10-02 09:20:00', '2024-10-02 09:20:00'),
(8, 4, 1, 'Ubi Bakar Original XL', 25000.00, 1, 0.00, 25000.00, '2024-10-02 09:20:00', '2024-10-02 09:20:00'),
(9, 4, 5, 'Ubi Bakar Original XL', 26000.00, 1, 1000.00, 1000.00, '2024-10-02 09:20:00', '2024-10-02 09:20:00'),
(10, 5, 6, 'Ubi Bakar Premium XL', 29000.00, 4, 0.00, 116000.00, '2024-10-02 11:30:00', '2024-10-02 11:30:00'),
(11, 5, 7, 'Ubi Bakar Original L', 16000.00, 2, 0.00, 32000.00, '2024-10-02 11:30:00', '2024-10-02 11:30:00'),
(12, 5, 8, 'Ubi Bakar Special M', 9000.00, 2, 0.00, 18000.00, '2024-10-02 11:30:00', '2024-10-02 11:30:00'),
(13, 5, 4, 'Ubi Bakar Special M', 8000.00, 1, 0.00, 2000.00, '2024-10-02 11:30:00', '2024-10-02 11:30:00'),

-- Items untuk transaksi November
(14, 6, 11, 'Ubi Bakar Original L', 17000.00, 6, 0.00, 102000.00, '2024-11-01 09:30:00', '2024-11-01 09:30:00'),
(15, 6, 13, 'Ubi Bakar Original XL', 27000.00, 2, 0.00, 54000.00, '2024-11-01 09:30:00', '2024-11-01 09:30:00'),
(16, 6, 12, 'Ubi Bakar Special M', 10000.00, 1, 0.00, 10000.00, '2024-11-01 09:30:00', '2024-11-01 09:30:00'),
(17, 6, 4, 'Ubi Bakar Special M', 8000.00, 1, 2000.00, 2000.00, '2024-11-01 09:30:00', '2024-11-01 09:30:00'),
(18, 7, 15, 'Ubi Bakar Original L', 16500.00, 3, 0.00, 49500.00, '2024-11-01 13:45:00', '2024-11-01 13:45:00'),
(19, 7, 14, 'Ubi Bakar Premium XL', 32000.00, 1, 0.00, 32000.00, '2024-11-01 13:45:00', '2024-11-01 13:45:00'),
(20, 7, 4, 'Ubi Bakar Special M', 8000.00, 1, 4000.00, 2500.00, '2024-11-01 13:45:00', '2024-11-01 13:45:00'),

-- Items untuk transaksi Desember
(21, 9, 17, 'Ubi Bakar Original XL', 28000.00, 3, 0.00, 84000.00, '2024-12-01 10:45:00', '2024-12-01 10:45:00'),
(22, 9, 19, 'Ubi Bakar Original L', 18000.00, 2, 0.00, 36000.00, '2024-12-01 10:45:00', '2024-12-01 10:45:00'),
(23, 9, 16, 'Ubi Bakar Special M', 11000.00, 2, 0.00, 20000.00, '2024-12-01 10:45:00', '2024-12-01 10:45:00'),
(24, 10, 18, 'Ubi Bakar Premium XL', 34000.00, 5, 0.00, 170000.00, '2024-12-01 15:20:00', '2024-12-01 15:20:00'),
(25, 10, 17, 'Ubi Bakar Original XL', 28000.00, 3, 0.00, 84000.00, '2024-12-01 15:20:00', '2024-12-01 15:20:00'),
(26, 10, 20, 'Ubi Bakar Special M', 11500.00, 2, 0.00, 23000.00, '2024-12-01 15:20:00', '2024-12-01 15:20:00'),
(27, 10, 4, 'Ubi Bakar Special M', 8000.00, 1, 0.00, 3000.00, '2024-12-01 15:20:00', '2024-12-01 15:20:00');

-- =====================================================
-- DUMMY DATA: production_logs (Sample)
-- =====================================================
INSERT INTO `production_logs` (`id`, `raw_inventory_id`, `processed_inventory_id`, `user_id`, `raw_amount_used`, `produced_amount`, `raw_cost`, `additional_cost`, `total_cost`, `cost_per_item`, `raw_name`, `processed_name`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 1, 100.00, 400, 1500000.00, 2000000.00, 3500000.00, 8750.00, 'Ubi Cilembu Grade A', 'Ubi Bakar Original XL', '2024-10-02 14:00:00', '2024-10-02 14:00:00'),
(2, 2, 2, 1, 80.00, 280, 1440000.00, 1960000.00, 3400000.00, 12142.86, 'Ubi Cilembu Organik', 'Ubi Bakar Premium XL', '2024-10-04 15:30:00', '2024-10-04 15:30:00'),
(3, 3, 3, 1, 120.00, 600, 1440000.00, 4320000.00, 5760000.00, 9600.00, 'Ubi Cilembu Grade B', 'Ubi Bakar Original L', '2024-10-06 09:45:00', '2024-10-06 09:45:00'),
(4, 4, 4, 1, 150.00, 900, 1200000.00, 3600000.00, 4800000.00, 5333.33, 'Ubi Cilembu Grade C', 'Ubi Bakar Special M', '2024-10-09 11:20:00', '2024-10-09 11:20:00'),
(5, 5, 5, 1, 90.00, 360, 1395000.00, 5580000.00, 6975000.00, 19375.00, 'Ubi Cilembu Grade A', 'Ubi Bakar Original XL', '2024-10-13 16:15:00', '2024-10-13 16:15:00'),
(6, 11, 11, 1, 125.00, 625, 1562500.00, 5312500.00, 6875000.00, 11000.00, 'Ubi Cilembu Grade B', 'Ubi Bakar Original L', '2024-11-03 09:15:00', '2024-11-03 09:15:00'),
(7, 16, 16, 1, 170.00, 1020, 1445000.00, 6205000.00, 7650000.00, 7500.00, 'Ubi Cilembu Grade C', 'Ubi Bakar Special M', '2024-12-02 08:45:00', '2024-12-02 08:45:00'),
(8, 17, 17, 1, 105.00, 420, 1732500.00, 6517500.00, 8250000.00, 19642.86, 'Ubi Cilembu Grade A', 'Ubi Bakar Original XL', '2024-12-06 11:30:00', '2024-12-06 11:30:00'),
(9, 18, 18, 1, 95.00, 333, 1852500.00, 5947500.00, 7800000.00, 23423.42, 'Ubi Cilembu Organik', 'Ubi Bakar Premium XL', '2024-12-09 14:15:00', '2024-12-09 14:15:00'),
(10, 19, 19, 1, 135.00, 675, 1728000.00, 6592000.00, 8320000.00, 12325.93, 'Ubi Cilembu Grade B', 'Ubi Bakar Original L', '2024-12-13 10:45:00', '2024-12-13 10:45:00');

-- =====================================================
-- DUMMY DATA: distributions
-- =====================================================
INSERT INTO `distributions` (`id`, `distribution_number`, `user_id`, `market_name`, `distribution_date`, `notes`, `status`, `is_urgent`, `urgency_reason`, `created_at`, `updated_at`) VALUES
(1, 'DIST-2024-10-001', 1, 'Pasar Baru Bandung', '2024-10-31', 'Distribusi produk mendekati kadaluarsa', 'delivered', 1, 'Produk mendekati expired', '2024-10-30 14:30:00', '2024-10-31 16:45:00'),
(2, 'DIST-2024-11-001', 1, 'Pasar Cicadas', '2024-11-30', 'Distribusi rutin akhir bulan', 'delivered', 0, NULL, '2024-11-29 10:15:00', '2024-11-30 15:20:00'),
(3, 'DIST-2024-12-001', 1, 'Pasar Kosambi', '2024-12-30', 'Distribusi akhir tahun', 'in_transit', 0, NULL, '2024-12-29 09:30:00', '2024-12-30 11:45:00'),
(4, 'DIST-2024-12-002', 1, 'Pasar Gedebage', '2024-12-31', 'Distribusi tahun baru', 'planned', 1, 'Stok berlebih', '2024-12-30 16:20:00', '2024-12-30 16:20:00');

-- =====================================================
-- DUMMY DATA: distribution_items
-- =====================================================
INSERT INTO `distribution_items` (`id`, `distribution_id`, `processed_inventory_id`, `other_product_id`, `quantity`, `price_per_item`, `total_price`, `created_at`, `updated_at`) VALUES
(1, 1, 1, NULL, 12, 20000.00, 240000.00, '2024-10-30 14:30:00', '2024-10-30 14:30:00'),
(2, 1, 2, NULL, 8, 22000.00, 176000.00, '2024-10-30 14:30:00', '2024-10-30 14:30:00'),
(3, 2, 5, NULL, 15, 21000.00, 315000.00, '2024-11-29 10:15:00', '2024-11-29 10:15:00'),
(4, 2, 6, NULL, 10, 24000.00, 240000.00, '2024-11-29 10:15:00', '2024-11-29 10:15:00'),
(5, 3, 16, NULL, 25, 9000.00, 225000.00, '2024-12-29 09:30:00', '2024-12-29 09:30:00'),
(6, 3, 17, NULL, 15, 23000.00, 345000.00, '2024-12-29 09:30:00', '2024-12-29 09:30:00'),
(7, 4, 18, NULL, 20, 28000.00, 560000.00, '2024-12-30 16:20:00', '2024-12-30 16:20:00'),
(8, 4, 19, NULL, 30, 15000.00, 450000.00, '2024-12-30 16:20:00', '2024-12-30 16:20:00');

-- =====================================================
-- CREATE INDEXES FOR BETTER PERFORMANCE
-- =====================================================
CREATE INDEX idx_raw_inventory_supplier ON raw_inventory(supplier_id);
CREATE INDEX idx_raw_inventory_purchase_date ON raw_inventory(purchase_date);
CREATE INDEX idx_raw_inventory_expiry_date ON raw_inventory(expiry_date);
CREATE INDEX idx_processed_inventory_production_date ON processed_inventory(production_date);
CREATE INDEX idx_processed_inventory_expiry_date ON processed_inventory(expiry_date);
CREATE INDEX idx_processed_inventory_expiry_status ON processed_inventory(expiry_status);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
CREATE INDEX idx_transactions_payment_method ON transactions(payment_method);
CREATE INDEX idx_transaction_items_product_id ON transaction_items(product_id);
CREATE INDEX idx_distributions_distribution_date ON distributions(distribution_date);
CREATE INDEX idx_distributions_status ON distributions(status);

-- =====================================================
-- ENABLE FOREIGN KEY CHECKS AND COMMIT
-- =====================================================
SET FOREIGN_KEY_CHECKS = 1;
COMMIT;

-- =====================================================
-- DATABASE SUMMARY - 3 MONTHS OPERATIONAL DATA
-- =====================================================
/*
SUPPLIERS: 4 active suppliers
RAW INVENTORY: 20 batches, total investment ~140M IDR
PROCESSED INVENTORY: 20 products, various expiry status
OTHER PRODUCTS: 10 supporting products (packaging, drinks, etc.)
TRANSACTIONS: 10 sample transactions (representing 600+ actual)
TRANSACTION ITEMS: 27 items across all transactions
PRODUCTION LOGS: 10 production records
DISTRIBUTIONS: 4 distribution records
DISTRIBUTION ITEMS: 8 distributed product batches

BUSINESS INSIGHTS:
- Total raw material cost: ~140,000,000 IDR
- Average production yield: 70-80% (kg to pieces conversion)
- Product mix: Original (40%), Premium (30%), Special (30%)
- Payment methods: Cash (50%), Transfer (25%), QRIS (25%)
- Expiry management: Active monitoring and distribution system
- Seasonal patterns: Higher sales in December (year-end)
*/

-- =====================================================
-- END OF DATABASE SCRIPT
-- =====================================================
