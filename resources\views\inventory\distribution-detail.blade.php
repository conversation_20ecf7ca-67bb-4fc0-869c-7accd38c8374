<!-- Detail Distribusi Modal Content -->
<div class="row">
    <div class="col-12">
        <!-- Header Info -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-info-circle"></i> Informasi Distribusi
                        </h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td width="40%"><strong>Nomor Distribusi:</strong></td>
                                <td>{{ $distribution->distribution_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>Tujuan:</strong></td>
                                <td>
                                    <i class="fas fa-map-marker-alt text-primary"></i>
                                    {{ $distribution->market_name }}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Tanggal Distribusi:</strong></td>
                                <td>{{ $distribution->distribution_date->format('d/m/Y') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    @php
                                        $statusClass = [
                                            'planned' => 'warning',
                                            'in_progress' => 'info', 
                                            'completed' => 'success',
                                            'cancelled' => 'danger'
                                        ][$distribution->status] ?? 'secondary';
                                        
                                        $statusText = [
                                            'planned' => 'Direncanakan',
                                            'in_progress' => 'Dalam Proses',
                                            'completed' => 'Selesai',
                                            'cancelled' => 'Dibatalkan'
                                        ][$distribution->status] ?? 'Unknown';
                                    @endphp
                                    <span class="badge bg-{{ $statusClass }}">{{ $statusText }}</span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-user"></i> Informasi Penanggung Jawab
                        </h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td width="40%"><strong>Dibuat oleh:</strong></td>
                                <td>{{ $distribution->user->name ?? 'Unknown' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Tanggal Dibuat:</strong></td>
                                <td>{{ $distribution->created_at->format('d/m/Y H:i') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Terakhir Update:</strong></td>
                                <td>{{ $distribution->updated_at->format('d/m/Y H:i') }}</td>
                            </tr>
                            @if($distribution->notes)
                            <tr>
                                <td><strong>Catatan:</strong></td>
                                <td>{{ $distribution->notes }}</td>
                            </tr>
                            @endif
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Items Detail -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-boxes"></i> Detail Produk yang Didistribusikan
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Produk</th>
                                <th>Batch</th>
                                <th>Jumlah</th>
                                <th>Harga Satuan</th>
                                <th>Total Harga</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php $totalQuantity = 0; $totalValue = 0; @endphp
                            @foreach($distribution->items as $item)
                                @php 
                                    $totalQuantity += $item->quantity;
                                    $totalValue += $item->total_price;
                                @endphp
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <strong>{{ $item->processedInventory->name ?? 'Produk Lain' }}</strong>
                                                @if($item->processedInventory && $item->processedInventory->expiry_date)
                                                    <br>
                                                    <small class="text-muted">
                                                        Exp: {{ $item->processedInventory->expiry_date->format('d/m/Y') }}
                                                    </small>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            {{ $item->processedInventory->batch_number ?? '-' }}
                                        </span>
                                    </td>
                                    <td>
                                        <strong class="text-primary">{{ number_format($item->quantity) }}</strong> unit
                                    </td>
                                    <td>
                                        Rp {{ number_format($item->price_per_item) }}
                                    </td>
                                    <td>
                                        <strong class="text-success">
                                            Rp {{ number_format($item->total_price) }}
                                        </strong>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                        <tfoot class="table-dark">
                            <tr>
                                <th colspan="2">TOTAL</th>
                                <th>{{ number_format($totalQuantity) }} unit</th>
                                <th>-</th>
                                <th>Rp {{ number_format($totalValue) }}</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-3 text-end">
            @if($distribution->status == 'planned')
                <button type="button" class="btn btn-info me-2" 
                        onclick="updateDistributionStatus({{ $distribution->id }}, 'in_progress')">
                    <i class="fas fa-play"></i> Mulai Distribusi
                </button>
                <button type="button" class="btn btn-danger me-2" 
                        onclick="updateDistributionStatus({{ $distribution->id }}, 'cancelled')">
                    <i class="fas fa-times"></i> Batalkan
                </button>
            @endif
            
            @if($distribution->status == 'in_progress')
                <button type="button" class="btn btn-success me-2" 
                        onclick="updateDistributionStatus({{ $distribution->id }}, 'completed')">
                    <i class="fas fa-check"></i> Selesaikan
                </button>
                <button type="button" class="btn btn-danger me-2" 
                        onclick="updateDistributionStatus({{ $distribution->id }}, 'cancelled')">
                    <i class="fas fa-times"></i> Batalkan
                </button>
            @endif
            
            <button type="button" class="btn btn-outline-primary" 
                    onclick="printDistributionDetail({{ $distribution->id }})">
                <i class="fas fa-print"></i> Print
            </button>
        </div>
    </div>
</div>

<script>
function updateDistributionStatus(id, status) {
    const statusText = {
        'in_progress': 'memulai',
        'completed': 'menyelesaikan', 
        'cancelled': 'membatalkan'
    }[status];
    
    if (confirm(`Apakah Anda yakin ingin ${statusText} distribusi ini?`)) {
        fetch(`/distributions/${id}/update-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal and reload parent page
                const modal = bootstrap.Modal.getInstance(document.getElementById('distributionDetailModal'));
                modal.hide();
                window.location.reload();
            } else {
                alert(data.message || 'Gagal mengupdate status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan saat mengupdate status');
        });
    }
}

function printDistributionDetail(id) {
    window.open(`/distributions/${id}/print`, '_blank');
}
</script>
