<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EmployeeDashboardController;
use App\Http\Controllers\RawInventoryController;
use App\Http\Controllers\ProcessedInventoryController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\OtherProductController;
use App\Http\Controllers\FinancialProjectionController;
use App\Http\Controllers\ProductionController;
use App\Http\Controllers\DistributionController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\FinancialReportController;
use App\Http\Controllers\ExpiryRecommendationController;
use App\Http\Controllers\Admin\AuditLogController;
use App\Http\Controllers\UserController;

// Landing page
Route::get('/', function () {
    return redirect()->route('login');
});

// Info login untuk debugging
Route::get('/login-info', function () {
    return view('auth.login-info');
});

// Database debugging
Route::get('/check-users', function () {
    return view('auth.check-user');
});

// Debug routes
Route::get('/routes', function () {
    $routeCollection = \Illuminate\Support\Facades\Route::getRoutes();
    echo "<h1>Routes List</h1>";
    echo "<table border='1'>";
    echo "<tr>";
    echo "<td><b>Method</b></td>";
    echo "<td><b>URI</b></td>";
    echo "<td><b>Name</b></td>";
    echo "</tr>";

    foreach ($routeCollection as $value) {
        echo "<tr>";
        echo "<td>" . implode('|', $value->methods()) . "</td>";
        echo "<td>" . $value->uri() . "</td>";
        echo "<td>" . $value->getName() . "</td>";
        echo "</tr>";
    }
    echo "</table>";
});

// Route API untuk mendapatkan role yang valid
Route::get('/api/valid-roles', function() {
    try {
        $roleInfo = \Illuminate\Support\Facades\DB::select("SHOW COLUMNS FROM users WHERE Field = 'role'");
        $roleType = $roleInfo[0]->Type;
        $validRoles = [];

        if (strpos($roleType, 'enum') !== false) {
            preg_match("/^enum\(\'(.*)\'\)$/", $roleType, $matches);
            $validRoles = explode("','", $matches[1]);
        }

        return response()->json([
            'status' => 'success',
            'role_type' => $roleType,
            'valid_roles' => $validRoles
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage()
        ]);
    }
});

// Super emergency login (sangat sederhana)
Route::get('/login-simple', function() {
    return 'Halaman login sederhana';
});

// Route untuk membuat user karyawan secara langsung
Route::get('/create-karyawan', function() {
    try {
        // Cek apakah database terhubung
        $db_name = \Illuminate\Support\Facades\DB::connection()->getDatabaseName();

        // Dapatkan nilai valid untuk role
        $roleInfo = \Illuminate\Support\Facades\DB::select("SHOW COLUMNS FROM users WHERE Field = 'role'");
        $roleType = $roleInfo[0]->Type;
        $validRoles = [];

        if (strpos($roleType, 'enum') !== false) {
            preg_match("/^enum\(\'(.*)\'\)$/", $roleType, $matches);
            $validRoles = explode("','", $matches[1]);
        }

        // Tampilkan informasi tentang nilai yang valid untuk role
        echo "<h2>Nilai Role yang Valid:</h2>";
        echo "<ul>";
        foreach ($validRoles as $role) {
            echo "<li>{$role}</li>";
        }
        echo "</ul>";

        // Gunakan 'employee' untuk karyawan
        $roleToUse = 'employee';

        // Buat user karyawan baru
        $user = new \App\Models\User();
        $user->name = 'Karyawan Toko';
        $user->email = '<EMAIL>';
        $user->password = \Illuminate\Support\Facades\Hash::make('karyawan123');
        $user->role = $roleToUse;
        $user->save();

        return "<h1>User karyawan berhasil dibuat!</h1>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> karyawan123</p>
                <p><strong>Role yang digunakan:</strong> {$roleToUse}</p>
                <p><strong>Database:</strong> {$db_name}</p>
                <p><a href='/emergency-login-karyawan'>Coba login sekarang</a></p>";
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

// Route untuk memeriksa struktur tabel users
Route::get('/check-user-table', function() {
    try {
        // Cek struktur tabel users
        $columns = \Illuminate\Support\Facades\Schema::getColumnListing('users');

        echo "<h1>Struktur Tabel Users</h1>";
        echo "<table border='1'>";
        echo "<tr><th>Kolom</th><th>Tipe</th><th>Null</th><th>Default</th></tr>";

        // Dapatkan informasi kolom dari database
        $columnsInfo = \Illuminate\Support\Facades\DB::select("SHOW COLUMNS FROM users");

        foreach ($columnsInfo as $column) {
            echo "<tr>";
            echo "<td>{$column->Field}</td>";
            echo "<td>{$column->Type}</td>";
            echo "<td>{$column->Null}</td>";
            echo "<td>{$column->Default}</td>";
            echo "</tr>";
        }

        echo "</table>";

        // Khusus periksa kolom role
        $roleInfo = \Illuminate\Support\Facades\DB::select("SHOW COLUMNS FROM users WHERE Field = 'role'");
        if (count($roleInfo) > 0) {
            $roleType = $roleInfo[0]->Type;
            echo "<h2>Informasi Kolom Role</h2>";
            echo "<p>Tipe: {$roleType}</p>";

            // Jika tipenya enum, tampilkan nilai-nilai yang valid
            if (strpos($roleType, 'enum') !== false) {
                preg_match("/^enum\(\'(.*)\'\)$/", $roleType, $matches);
                $validValues = explode("','", $matches[1]);

                echo "<p>Nilai yang valid untuk kolom role:</p>";
                echo "<ul>";
                foreach ($validValues as $value) {
                    echo "<li>{$value}</li>";
                }
                echo "</ul>";
            }
        }

        return "";
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

// Route untuk melihat semua user di database
Route::get('/show-users', function() {
    try {
        $users = \App\Models\User::all();
        echo "<h1>Daftar User</h1>";
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Nama</th><th>Email</th><th>Role</th></tr>";

        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user->id}</td>";
            echo "<td>{$user->name}</td>";
            echo "<td>{$user->email}</td>";
            echo "<td>{$user->role}</td>";
            echo "</tr>";
        }

        echo "</table>";
        echo "<p>Total user: " . count($users) . "</p>";

        // Tampilkan nama tabel users
        $table_name = (new \App\Models\User())->getTable();
        echo "<p>Nama tabel: {$table_name}</p>";

        // Tampilkan juga nama database
        $db_name = \Illuminate\Support\Facades\DB::connection()->getDatabaseName();
        echo "<p>Database: {$db_name}</p>";

    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

// Emergency login untuk karyawan (dengan info lengkap)
Route::get('/emergency-login-karyawan', function () {
    try {
        // Cek apakah tabel users ada
        $table_exists = \Illuminate\Support\Facades\Schema::hasTable('users');
        if (!$table_exists) {
            return 'Tabel users tidak ditemukan!';
        }

        // Cek jumlah user di database
        $users_count = \App\Models\User::count();

        // Cari user <NAME_EMAIL> tanpa melihat role
        $user = \App\Models\User::where('email', '<EMAIL>')->first();

        if ($user) {
            $userRole = $user->role;
            \Illuminate\Support\Facades\Auth::login($user);

            // Tampilkan info sebelum redirect
            return "
                <h1>Login berhasil!</h1>
                <p><strong>User:</strong> {$user->name}</p>
                <p><strong>Email:</strong> {$user->email}</p>
                <p><strong>Role:</strong> {$userRole}</p>
                <p>Mengalihkan ke dashboard dalam 3 detik...</p>
                <script>
                    setTimeout(function() {
                        window.location.href = '/home';
                    }, 3000);
                </script>
            ";
        }

        // Jika user tidak ditemukan, tampilkan info debugging
        return "
            <h1>User karyawan tidak ditemukan!</h1>
            <p>Total user di database: {$users_count}</p>
            <p>Silakan periksa dan buat user:</p>
            <ul>
                <li><a href='/check-user-table'>Periksa struktur tabel user</a></li>
                <li><a href='/create-karyawan'>Buat user karyawan baru</a></li>
                <li><a href='/show-users'>Lihat daftar semua user</a></li>
            </ul>
        ";
    } catch (\Exception $e) {
        return 'Error: ' . $e->getMessage();
    }
});

// Create test users
Route::post('/create-test-users', function () {
    // Create admin user
    App\Models\User::updateOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => 'Admin UBI',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'created_at' => now(),
            'updated_at' => now()
        ]
    );

    // Create karyawan user
    App\Models\User::updateOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => 'Karyawan Toko',
            'password' => Hash::make('karyawan123'),
            'role' => 'employee',
            'created_at' => now(),
            'updated_at' => now()
        ]
    );

    return redirect('/check-users')->with('status', 'Test users created successfully!');
});

// Route khusus untuk membuat admin baru - SOLUSI CEPAT
Route::get('/create-new-admin', function () {
    try {
        // Periksa struktur tabel users terlebih dahulu
        $columns = \DB::select("SHOW COLUMNS FROM users");
        $columnNames = array_column($columns, 'Field');

        // Tentukan data user berdasarkan kolom yang ada
        $userData = [
            'name' => 'Super Administrator',
            'password' => \Hash::make('superadmin123'),
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Tambahkan role jika kolom ada
        if (in_array('role', $columnNames)) {
            $userData['role'] = 'admin';
        }

        // Tambahkan last_activity jika kolom ada
        if (in_array('last_activity', $columnNames)) {
            $userData['last_activity'] = now();
        }

        // Buat admin user baru
        $admin = \App\Models\User::updateOrCreate(
            ['email' => '<EMAIL>'],
            $userData
        );

        // Buat admin alternatif dengan data yang sama
        $userData2 = [
            'name' => 'Admin Toko',
            'password' => \Hash::make('admin123'),
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Tambahkan role jika kolom ada
        if (in_array('role', $columnNames)) {
            $userData2['role'] = 'admin';
        }

        // Tambahkan last_activity jika kolom ada
        if (in_array('last_activity', $columnNames)) {
            $userData2['last_activity'] = now();
        }

        $admin2 = \App\Models\User::updateOrCreate(
            ['email' => '<EMAIL>'],
            $userData2
        );

        // Tampilkan informasi kolom yang tersedia
        $columnInfo = "Kolom tersedia: " . implode(', ', $columnNames);
        $adminRole = $admin->getAttribute('role') ?? 'admin (default)';
        $admin2Role = $admin2->getAttribute('role') ?? 'admin (default)';

        return "
            <h1>✅ Admin Baru Berhasil Dibuat!</h1>
            <div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                <h4>📊 Info Database:</h4>
                <p><strong>Tabel:</strong> users</p>
                <p><strong>{$columnInfo}</strong></p>
                <p><strong>Total Users:</strong> " . \App\Models\User::count() . "</p>
            </div>

            <div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                <h3>Kredensial Login Admin:</h3>
                <table border='1' style='border-collapse: collapse; width: 100%;'>
                    <tr style='background: #f8f9fa;'>
                        <th style='padding: 10px;'>Nama</th>
                        <th style='padding: 10px;'>Email</th>
                        <th style='padding: 10px;'>Password</th>
                        <th style='padding: 10px;'>Role</th>
                    </tr>
                    <tr>
                        <td style='padding: 10px;'>{$admin->name}</td>
                        <td style='padding: 10px; font-weight: bold; color: blue;'>{$admin->email}</td>
                        <td style='padding: 10px; font-weight: bold; color: red;'>superadmin123</td>
                        <td style='padding: 10px;'>{$adminRole}</td>
                    </tr>
                    <tr>
                        <td style='padding: 10px;'>{$admin2->name}</td>
                        <td style='padding: 10px; font-weight: bold; color: blue;'>{$admin2->email}</td>
                        <td style='padding: 10px; font-weight: bold; color: red;'>admin123</td>
                        <td style='padding: 10px;'>{$admin2Role}</td>
                    </tr>
                </table>
            </div>

            <div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                <h3>🔐 Cara Login:</h3>
                <ol>
                    <li>Kembali ke halaman login: <a href='/login' style='color: blue; text-decoration: underline;'>http://localhost:8000/login</a></li>
                    <li>Pilih <strong>Admin</strong></li>
                    <li>Gunakan salah satu email di atas</li>
                    <li>Masukkan password yang sesuai</li>
                </ol>
            </div>

            <div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                <h3>⚠️ Penting:</h3>
                <p>Simpan kredensial ini dengan aman. Setelah login, Anda bisa mengelola user lain melalui menu User Management.</p>
            </div>

            <p><a href='/login' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Login Sekarang</a></p>
            <p><a href='/show-users' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>👥 Lihat Semua User</a></p>
        ";

    } catch (\Exception $e) {
        return "
            <h1>❌ Error!</h1>
            <p style='color: red;'>Terjadi kesalahan: {$e->getMessage()}</p>
            <p><a href='/check-user-table'>Periksa Struktur Tabel</a></p>
        ";
    }
});

// ========================================
// DEBUG ROUTES - HAPUS SETELAH TESTING
// ========================================

// Debug route untuk cek data distribusi
Route::get('/debug-distributions', function () {
    try {
        $distributions = \App\Models\Distribution::with(['items', 'user'])->get();
        $distributionItems = \App\Models\DistributionItem::with(['distribution', 'processedInventory'])->get();

        $html = "<h1>🔍 Debug Data Distribusi</h1>";

        // Info tabel distributions
        $html .= "<div style='background: #e7f3ff; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        $html .= "<h3>📊 Tabel Distributions</h3>";
        $html .= "<p><strong>Total Records:</strong> " . $distributions->count() . "</p>";

        if ($distributions->count() > 0) {
            $html .= "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            $html .= "<tr style='background: #f8f9fa;'>";
            $html .= "<th style='padding: 8px;'>ID</th>";
            $html .= "<th style='padding: 8px;'>Distribution Number</th>";
            $html .= "<th style='padding: 8px;'>Market Name</th>";
            $html .= "<th style='padding: 8px;'>Distribution Date</th>";
            $html .= "<th style='padding: 8px;'>Status</th>";
            $html .= "<th style='padding: 8px;'>User</th>";
            $html .= "<th style='padding: 8px;'>Items Count</th>";
            $html .= "</tr>";

            foreach ($distributions as $dist) {
                $html .= "<tr>";
                $html .= "<td style='padding: 8px;'>{$dist->id}</td>";
                $html .= "<td style='padding: 8px;'>{$dist->distribution_number}</td>";
                $html .= "<td style='padding: 8px;'>{$dist->market_name}</td>";
                $html .= "<td style='padding: 8px;'>{$dist->distribution_date}</td>";
                $html .= "<td style='padding: 8px;'>{$dist->status}</td>";
                $html .= "<td style='padding: 8px;'>" . ($dist->user ? $dist->user->name : 'N/A') . "</td>";
                $html .= "<td style='padding: 8px;'>{$dist->items->count()}</td>";
                $html .= "</tr>";
            }
            $html .= "</table>";
        } else {
            $html .= "<p style='color: red;'>❌ Tidak ada data distribusi!</p>";
        }
        $html .= "</div>";

        // Info tabel distribution_items
        $html .= "<div style='background: #fff3cd; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        $html .= "<h3>📦 Tabel Distribution Items</h3>";
        $html .= "<p><strong>Total Records:</strong> " . $distributionItems->count() . "</p>";

        if ($distributionItems->count() > 0) {
            $html .= "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            $html .= "<tr style='background: #f8f9fa;'>";
            $html .= "<th style='padding: 8px;'>ID</th>";
            $html .= "<th style='padding: 8px;'>Distribution ID</th>";
            $html .= "<th style='padding: 8px;'>Product</th>";
            $html .= "<th style='padding: 8px;'>Quantity</th>";
            $html .= "<th style='padding: 8px;'>Price</th>";
            $html .= "<th style='padding: 8px;'>Total</th>";
            $html .= "</tr>";

            foreach ($distributionItems as $item) {
                $productName = $item->processedInventory ? $item->processedInventory->name : 'N/A';
                $html .= "<tr>";
                $html .= "<td style='padding: 8px;'>{$item->id}</td>";
                $html .= "<td style='padding: 8px;'>{$item->distribution_id}</td>";
                $html .= "<td style='padding: 8px;'>{$productName}</td>";
                $html .= "<td style='padding: 8px;'>{$item->quantity}</td>";
                $html .= "<td style='padding: 8px;'>Rp " . number_format($item->price_per_item, 0, ',', '.') . "</td>";
                $html .= "<td style='padding: 8px;'>Rp " . number_format($item->total_price, 0, ',', '.') . "</td>";
                $html .= "</tr>";
            }
            $html .= "</table>";
        } else {
            $html .= "<p style='color: red;'>❌ Tidak ada data distribution items!</p>";
        }
        $html .= "</div>";

        // Test query yang digunakan di laporan
        $startDate = now()->startOfMonth()->format('Y-m-d');
        $endDate = now()->format('Y-m-d');

        $reportDistributions = \App\Models\Distribution::whereBetween('distribution_date', [$startDate, $endDate])
            ->with(['items.processedInventory' => function($query) {
                $query->whereRaw('expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)');
            }])
            ->whereHas('items.processedInventory', function($query) {
                $query->whereRaw('expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)');
            })
            ->get();

        $html .= "<div style='background: #f8d7da; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        $html .= "<h3>🔍 Test Query Laporan</h3>";
        $html .= "<p><strong>Period:</strong> {$startDate} to {$endDate}</p>";
        $html .= "<p><strong>Query Result:</strong> " . $reportDistributions->count() . " distributions</p>";
        $html .= "<p><strong>Filter:</strong> Hanya distribusi dengan produk hampir kadaluarsa (≤ 7 hari)</p>";
        $html .= "</div>";

        $html .= "<div style='margin: 20px 0;'>";
        $html .= "<a href='/expiry-recommendations/distribution-report' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Lihat Laporan Distribusi</a>";
        $html .= "<a href='/distributions' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📋 Kelola Distribusi</a>";
        $html .= "</div>";

        return $html;

    } catch (\Exception $e) {
        return "<h1>❌ Error!</h1><p style='color: red;'>Error: {$e->getMessage()}</p>";
    }
});

// Route untuk membuat data distribusi test
Route::get('/create-test-distributions', function () {
    try {
        // Pastikan ada user
        $user = \App\Models\User::first();
        if (!$user) {
            return "❌ Error: Tidak ada user! Buat user terlebih dahulu.";
        }

        // Pastikan ada processed inventory
        $products = \App\Models\ProcessedInventory::where('current_stock', '>', 0)->take(3)->get();
        if ($products->count() == 0) {
            return "❌ Error: Tidak ada processed inventory! Import database lengkap terlebih dahulu.";
        }

        // Buat distribusi test
        $distributions = [];

        // Distribusi 1 - Hari ini
        $dist1 = \App\Models\Distribution::create([
            'distribution_number' => 'DIST-' . date('Ymd') . '-0001',
            'user_id' => $user->id,
            'market_name' => 'Pasar Baru Bandung',
            'distribution_date' => now()->format('Y-m-d'),
            'notes' => 'Distribusi produk hampir kadaluarsa hari ini',
            'status' => 'delivered'
        ]);

        // Tambahkan items untuk distribusi 1
        foreach ($products->take(2) as $product) {
            \App\Models\DistributionItem::create([
                'distribution_id' => $dist1->id,
                'processed_inventory_id' => $product->id,
                'other_product_id' => null,
                'quantity' => rand(5, 15),
                'price_per_item' => $product->selling_price * 0.8, // Diskon 20%
                'total_price' => (rand(5, 15)) * ($product->selling_price * 0.8)
            ]);
        }

        // Distribusi 2 - Kemarin
        $dist2 = \App\Models\Distribution::create([
            'distribution_number' => 'DIST-' . date('Ymd', strtotime('-1 day')) . '-0001',
            'user_id' => $user->id,
            'market_name' => 'Pasar Cicadas',
            'distribution_date' => now()->subDay()->format('Y-m-d'),
            'notes' => 'Distribusi rutin kemarin',
            'status' => 'delivered'
        ]);

        // Tambahkan items untuk distribusi 2
        foreach ($products->take(3) as $product) {
            \App\Models\DistributionItem::create([
                'distribution_id' => $dist2->id,
                'processed_inventory_id' => $product->id,
                'other_product_id' => null,
                'quantity' => rand(8, 20),
                'price_per_item' => $product->selling_price * 0.9, // Diskon 10%
                'total_price' => (rand(8, 20)) * ($product->selling_price * 0.9)
            ]);
        }

        // Distribusi 3 - Minggu lalu
        $dist3 = \App\Models\Distribution::create([
            'distribution_number' => 'DIST-' . date('Ymd', strtotime('-7 days')) . '-0001',
            'user_id' => $user->id,
            'market_name' => 'Pasar Kosambi',
            'distribution_date' => now()->subDays(7)->format('Y-m-d'),
            'notes' => 'Distribusi minggu lalu',
            'status' => 'delivered'
        ]);

        // Tambahkan items untuk distribusi 3
        foreach ($products as $product) {
            \App\Models\DistributionItem::create([
                'distribution_id' => $dist3->id,
                'processed_inventory_id' => $product->id,
                'other_product_id' => null,
                'quantity' => rand(10, 25),
                'price_per_item' => $product->selling_price,
                'total_price' => (rand(10, 25)) * $product->selling_price
            ]);
        }

        $distributions = [$dist1, $dist2, $dist3];

        $html = "<h1>✅ Data Distribusi Test Berhasil Dibuat!</h1>";
        $html .= "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        $html .= "<h3>📊 Distribusi yang Dibuat:</h3>";
        $html .= "<ul>";
        foreach ($distributions as $dist) {
            $itemCount = $dist->items()->count();
            $html .= "<li><strong>{$dist->distribution_number}</strong> - {$dist->market_name} ({$dist->distribution_date}) - {$itemCount} items</li>";
        }
        $html .= "</ul>";
        $html .= "</div>";

        $html .= "<div style='margin: 20px 0;'>";
        $html .= "<a href='/debug-distributions' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔍 Debug Data</a>";
        $html .= "<a href='/expiry-recommendations/distribution-report' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Lihat Laporan</a>";
        $html .= "<a href='/distributions' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📋 Kelola Distribusi</a>";
        $html .= "</div>";

        return $html;

    } catch (\Exception $e) {
        return "<h1>❌ Error!</h1><p style='color: red;'>Error: {$e->getMessage()}</p><pre>{$e->getTraceAsString()}</pre>";
    }
});

// Route untuk melihat log error
Route::get('/view-logs', function () {
    $logFile = storage_path('logs/laravel.log');
    if (file_exists($logFile)) {
        $logs = file_get_contents($logFile);
        $recentLogs = substr($logs, -5000); // Last 5000 characters
        return "<h1>📋 Recent Logs</h1><pre style='background: #f8f9fa; padding: 20px; border-radius: 5px; font-size: 12px;'>" . htmlspecialchars($recentLogs) . "</pre>";
    }
    return "<h1>📋 No Log File Found</h1>";
});

// Route untuk membuat distribusi test langsung
Route::get('/create-direct-distribution', function () {
    try {
        $item = \App\Models\ProcessedInventory::first();
        if (!$item) {
            return "❌ Tidak ada processed inventory! Import database terlebih dahulu.";
        }

        // Create distribution langsung
        $distribution = \App\Models\Distribution::create([
            'distribution_number' => 'DIRECT-' . date('Ymd-His'),
            'user_id' => 1, // Default user ID
            'market_name' => 'Pasar Test Langsung',
            'distribution_date' => now()->format('Y-m-d'),
            'notes' => 'Distribusi test dibuat langsung via route',
            'status' => 'planned'
        ]);

        // Create distribution item
        $distribution->items()->create([
            'processed_inventory_id' => $item->id,
            'other_product_id' => null,
            'quantity' => 5,
            'price_per_item' => 15000,
            'total_price' => 5 * 15000
        ]);

        $html = "<h1>✅ Distribusi Test Berhasil Dibuat!</h1>";
        $html .= "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        $html .= "<h3>📊 Detail Distribusi:</h3>";
        $html .= "<p><strong>Nomor:</strong> {$distribution->distribution_number}</p>";
        $html .= "<p><strong>Pasar:</strong> {$distribution->market_name}</p>";
        $html .= "<p><strong>Tanggal:</strong> {$distribution->distribution_date}</p>";
        $html .= "<p><strong>Produk:</strong> {$item->name}</p>";
        $html .= "<p><strong>Jumlah:</strong> 5 unit</p>";
        $html .= "</div>";

        $html .= "<div style='margin: 20px 0;'>";
        $html .= "<a href='/debug-distributions' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔍 Debug Data</a>";
        $html .= "<a href='/expiry-recommendations/distribution-report' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📊 Lihat Laporan</a>";
        $html .= "</div>";

        return $html;

    } catch (\Exception $e) {
        return "<h1>❌ Error!</h1><p style='color: red;'>Error: {$e->getMessage()}</p><pre>{$e->getTraceAsString()}</pre>";
    }
});

// Route debug untuk test form distribusi
Route::get('/test-distribution-form', function () {
    $item = \App\Models\ProcessedInventory::first();
    if (!$item) {
        return "❌ Tidak ada processed inventory! Import database terlebih dahulu.";
    }

    $markets = ['Pasar Baru Bandung', 'Pasar Cicadas', 'Pasar Kosambi'];

    return view('inventory.create-distribution', compact('item', 'markets'));
});

// Route untuk test submit distribusi
Route::post('/test-save-distribution', function (Request $request) {
    try {
        \Log::info('Test Distribution Submit:', $request->all());

        $item = \App\Models\ProcessedInventory::findOrFail($request->processed_inventory_id);

        // Create distribution
        $distribution = \App\Models\Distribution::create([
            'distribution_number' => 'TEST-' . date('Ymd-His'),
            'user_id' => auth()->id() ?? 1,
            'market_name' => $request->market_name,
            'distribution_date' => $request->distribution_date,
            'notes' => $request->notes,
            'status' => 'planned'
        ]);

        // Create distribution item
        $distribution->items()->create([
            'processed_inventory_id' => $item->id,
            'other_product_id' => null,
            'quantity' => $request->quantity,
            'price_per_item' => 15000, // Default price
            'total_price' => $request->quantity * 15000
        ]);

        return redirect()->back()->with('success', '✅ Test distribusi berhasil dibuat!');

    } catch (\Exception $e) {
        \Log::error('Test Distribution Error:', ['error' => $e->getMessage()]);
        return redirect()->back()->with('error', '❌ Error: ' . $e->getMessage());
    }
});

// Route untuk laporan distribusi yang diperbaiki
Route::get('/fixed-distribution-report', function (Request $request) {
    $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
    $endDate = $request->get('end_date', now()->format('Y-m-d'));

    // Query yang diperbaiki - tampilkan SEMUA distribusi dalam periode
    $distributions = \App\Models\Distribution::whereBetween('distribution_date', [$startDate, $endDate])
        ->with(['items.processedInventory', 'items.otherProduct', 'user'])
        ->orderBy('distribution_date', 'desc')
        ->get();

    // Hitung statistik
    $totalDistributions = $distributions->count();
    $totalItems = $distributions->sum(function($distribution) {
        return $distribution->items->sum('quantity');
    });
    $totalValue = $distributions->sum(function($distribution) {
        return $distribution->items->sum('total_price');
    });

    // Market performance
    $marketPerformance = $distributions->groupBy('market_name')->map(function($items, $market) {
        return [
            'market' => $market,
            'distributions' => $items->count(),
            'total_items' => $items->sum(function($distribution) {
                return $distribution->items->sum('quantity');
            }),
            'total_value' => $items->sum(function($distribution) {
                return $distribution->items->sum('total_price');
            })
        ];
    })->values();

    return view('inventory.distribution-report', compact(
        'distributions',
        'startDate',
        'endDate',
        'totalDistributions',
        'totalItems',
        'totalValue',
        'marketPerformance'
    ));
});

// Khusus login karyawan
Route::get('/login-karyawan', [App\Http\Controllers\KaryawanLoginController::class, 'showLoginForm'])->name('karyawan.login.form');
Route::post('/login-karyawan', [App\Http\Controllers\KaryawanLoginController::class, 'login'])->name('karyawan.login');

// Login langsung tanpa validasi role
Route::post('/login-direct', function(\Illuminate\Http\Request $request) {
    try {
        // Validasi input minimal
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        // Cari user berdasarkan email saja
        $user = \App\Models\User::where('email', $request->email)->first();

        if (!$user) {
            return back()->withErrors([
                'email' => 'Email tidak ditemukan di database.'
            ])->withInput();
        }

        // Cek password
        if (!\Illuminate\Support\Facades\Hash::check($request->password, $user->password)) {
            return back()->withErrors([
                'password' => 'Password salah.'
            ])->withInput();
        }

        // Login berhasil
        \Illuminate\Support\Facades\Auth::login($user, $request->boolean('remember'));

        // Informasi login berhasil
        $role = $user->role;
        $name = $user->name;

        // Redirect berdasarkan role
        if ($role === 'admin') {
            return redirect()->route('dashboard')
                ->with('success', "Selamat datang, {$name}! Anda berhasil login sebagai {$role}.");
        } else if ($role === 'employee' || $role === 'karyawan') {
            return redirect()->route('employee.dashboard')
                ->with('success', "Selamat datang, {$name}! Anda berhasil login sebagai Karyawan.");
        }

        // Default fallback
        return redirect()->route('employee.dashboard')
            ->with('success', "Selamat datang, {$name}! Anda berhasil login sebagai {$role}.");
    } catch (\Exception $e) {
        return back()->withErrors([
            'general' => 'Terjadi kesalahan: ' . $e->getMessage()
        ]);
    }
});

// Authentication routes
Auth::routes();

// Home routes (setelah login)
Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

// Routes yang bisa diakses oleh admin dan employee
Route::middleware(['auth'])->group(function () {
    // Dashboard routes
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/sales-report', [DashboardController::class, 'salesReport'])->name('dashboard.sales-report');
    Route::get('/dashboard/inventory-report', [DashboardController::class, 'inventoryReport'])->name('dashboard.inventory-report');

    // Inventory routes
    Route::prefix('inventory')->group(function () {
        Route::get('/', [InventoryController::class, 'index'])->name('inventory.index');
        Route::post('/raw', [InventoryController::class, 'addRawStock'])->name('inventory.add-raw');
        Route::post('/process', [InventoryController::class, 'processStock'])->name('inventory.process');
        Route::get('/low-stock-alert', [InventoryController::class, 'getLowStockAlert'])->name('inventory.low-stock-alert');
    });

    // Raw Inventory routes
    Route::resource('raw-inventory', RawInventoryController::class);
    Route::post('/raw-inventory/{rawInventory}/add-stock', [RawInventoryController::class, 'addStock'])->name('raw-inventory.add-stock');

    // Processed Inventory routes
    Route::resource('processed-inventory', ProcessedInventoryController::class);
    Route::get('/processed-inventory-process', [ProcessedInventoryController::class, 'showProcessForm'])->name('processed-inventory.show-process-form');
    Route::post('/processed-inventory-process', [ProcessedInventoryController::class, 'process'])->name('processed-inventory.process');
    Route::get('/production-reports', [ProcessedInventoryController::class, 'productionReports'])->name('production.reports');
    Route::get('/production-reports/export', [ProductionController::class, 'exportReports'])->name('production.reports.export');

    // Transaction routes
    Route::resource('transactions', TransactionController::class)->except(['edit', 'update']);
    Route::get('/transactions/{transaction}/receipt', [TransactionController::class, 'receipt'])->name('transactions.receipt');
    Route::get('/transactions/{transaction}/print', [TransactionController::class, 'print'])->name('transactions.print');
    Route::get('/transactions/{transaction}/export-pdf', [TransactionController::class, 'exportPdf'])->name('transactions.export-pdf');
    Route::get('/transactions/{transaction}/export-html', [TransactionController::class, 'exportHtml'])->name('transactions.export-html');
    Route::get('/transactions-export', [TransactionController::class, 'export'])->name('transactions.export');
    Route::post('/transactions/{transaction}/cancel', [TransactionController::class, 'cancel'])->name('transactions.cancel');
    Route::get('/pos', [TransactionController::class, 'create'])->name('pos');

    // Payment Gateway routes (authenticated)
    Route::prefix('payment')->group(function () {
        Route::match(['GET', 'POST'], '/create/{transaction}', [App\Http\Controllers\PaymentController::class, 'createPayment'])->name('payment.create');
        Route::get('/status/{transaction}', [App\Http\Controllers\PaymentController::class, 'checkStatus'])->name('payment.status');
        Route::post('/update-status/{transaction}', [App\Http\Controllers\PaymentController::class, 'updateStatus'])->name('payment.update-status');
    });

    // Other Products routes
    Route::resource('other-products', OtherProductController::class);

    // Route khusus untuk proyeksi keuangan yang dapat diakses oleh semua user
    Route::prefix('reports/financial-projection')->group(function () {
        Route::get('/', [FinancialProjectionController::class, 'index'])->name('reports.financial-projection');
        Route::get('/export-excel', [FinancialProjectionController::class, 'exportExcel'])->name('reports.financial-projection.export-excel');
        Route::get('/export-pdf', [FinancialProjectionController::class, 'exportPdf'])->name('reports.financial-projection.export-pdf');
    });
    
    // Route untuk rekomendasi ubi matang yang perlu segera dijual
    Route::prefix('expiry-recommendations')->group(function () {
        Route::get('/', [ExpiryRecommendationController::class, 'index'])->name('expiry-recommendations.index');
        Route::get('/update/{id}', [ExpiryRecommendationController::class, 'updateRecommendation'])->name('expiry-recommendations.update');
        Route::get('/update-all', [ExpiryRecommendationController::class, 'updateAllRecommendations'])->name('expiry-recommendations.update-all');
        Route::get('/export-excel', [ExpiryRecommendationController::class, 'exportExcel'])->name('expiry-recommendations.export-excel');
        Route::get('/export-pdf', [ExpiryRecommendationController::class, 'exportPdf'])->name('expiry-recommendations.export-pdf');
        Route::get('/distribution-report', [ExpiryRecommendationController::class, 'distributionReport'])->name('expiry-recommendations.distribution-report');
        Route::get('/create-distribution/{id}', [ExpiryRecommendationController::class, 'createDistribution'])->name('expiry-recommendations.create-distribution');
        Route::get('/sales-report', [ExpiryRecommendationController::class, 'salesReport'])->name('expiry-recommendations.sales-report');
    });

    // Route POST untuk distribusi (di luar grup untuk menghindari konflik)
    Route::any('save-distribution-data', [ExpiryRecommendationController::class, 'storeDistribution'])->middleware('auth')->name('save.distribution.data');

    // Route test untuk debug
    Route::get('test-distribution', function() {
        return 'Route test berhasil!';
    });
});

// Payment Gateway callback routes (no auth required for Midtrans callbacks)
Route::prefix('payment')->group(function () {
    Route::post('/notification', [App\Http\Controllers\PaymentController::class, 'handleNotification'])->name('payment.notification');
    Route::get('/finish', [App\Http\Controllers\PaymentController::class, 'paymentFinish'])->name('payment.finish');
    Route::get('/success', [App\Http\Controllers\PaymentController::class, 'paymentSuccess'])->name('payment.success');
    Route::get('/unfinish', [App\Http\Controllers\PaymentController::class, 'paymentUnfinish'])->name('payment.unfinish');
    Route::get('/error', [App\Http\Controllers\PaymentController::class, 'paymentError'])->name('payment.error');
});



// Routes khusus untuk employee/karyawan lama (backward compatibility)
Route::middleware(['auth', \App\Http\Middleware\EmployeeMiddleware::class])->group(function () {
    Route::get('/employee-dashboard', [EmployeeDashboardController::class, 'index'])->name('employee.dashboard');
});

// Routes yang hanya bisa diakses oleh admin
Route::middleware(['auth', \App\Http\Middleware\AdminMiddleware::class])->group(function () {
    // User Management Routes
    Route::resource('users', UserController::class);
    Route::post('/users/{id}/restore', [UserController::class, 'restore'])->name('users.restore');
    Route::delete('/users/{id}/force-delete', [UserController::class, 'forceDelete'])->name('users.force-delete');

    // Production Process Routes (admin juga bisa akses)
    Route::resource('production', ProductionController::class);
    Route::get('/production/report', [ProductionController::class, 'report'])->name('production.report');

    // Distribution Routes (admin juga bisa akses)
    Route::resource('distributions', DistributionController::class);
    Route::get('/distributions/report', [DistributionController::class, 'report'])->name('distributions.report');
    Route::post('/distributions/{distribution}/update-status', [DistributionController::class, 'updateStatus'])->name('distributions.update-status');
    
    // Expense Routes
    Route::resource('expense', ExpenseController::class);
    Route::get('/expense/report', [ExpenseController::class, 'report'])->name('expense.report');
    
    // Financial Reports
    Route::get('/financial', [FinancialReportController::class, 'index'])->name('financial.index');
    Route::get('/financial/income-statement', [FinancialReportController::class, 'incomeStatement'])->name('financial.income-statement');
    Route::get('/financial/inventory-valuation', [FinancialReportController::class, 'inventoryValuation'])->name('financial.inventory-valuation');
    Route::get('/financial/projection', [FinancialReportController::class, 'projection'])->name('financial.projection');
    
    // Reports (legacy - dipertahankan untuk kompatibilitas)
    Route::get('/reports/sales', [DashboardController::class, 'salesReport'])->name('reports.sales');
    Route::get('/reports/inventory', [DashboardController::class, 'inventoryReport'])->name('reports.inventory');
    Route::get('/reports/financial', [DashboardController::class, 'financialReport'])->name('reports.financial');
    Route::get('/reports/export/{type}', [DashboardController::class, 'exportReport'])->name('reports.export');

    // Daily sales report
    Route::get('/reports/daily', [TransactionController::class, 'dailyReport'])->name('reports.daily');

    // Audit Logs
    Route::get('/admin/audit-logs', [AuditLogController::class, 'index'])->name('admin.audit-logs.index');
    Route::get('/admin/audit-logs/{auditLog}', [AuditLogController::class, 'show'])->name('admin.audit-logs.show');
});

// Fix role route - automatically update role values to match the expected enum
Route::get('/fix-role-values', function() {
    try {
        // Check what are the valid values for role in the database
        $roleInfo = \Illuminate\Support\Facades\DB::select("SHOW COLUMNS FROM users WHERE Field = 'role'");
        $roleType = $roleInfo[0]->Type;
        $validRoles = [];

        if (strpos($roleType, 'enum') !== false) {
            preg_match("/^enum\(\'(.*)\'\)$/", $roleType, $matches);
            $validRoles = explode("','", $matches[1]);
        }

        echo "<h1>Fixing Role Values</h1>";
        echo "<p>Valid role values in database: " . implode(', ', $validRoles) . "</p>";

        // Check users with role 'employee'
        $employeeUsers = \App\Models\User::where('role', 'employee')->get();
        $updatedEmployeeCount = 0;

        // If 'karyawan' is a valid role but 'employee' is not, update those users
        if (in_array('karyawan', $validRoles) && !in_array('employee', $validRoles)) {
            foreach($employeeUsers as $user) {
                $user->role = 'karyawan';
                $user->save();
                $updatedEmployeeCount++;
            }
            echo "<p>Updated {$updatedEmployeeCount} users from 'employee' to 'karyawan'</p>";
        }

        // Check users with role 'karyawan'
        $karyawanUsers = \App\Models\User::where('role', 'karyawan')->get();
        $updatedKaryawanCount = 0;

        // If 'employee' is a valid role but 'karyawan' is not, update those users
        if (in_array('employee', $validRoles) && !in_array('karyawan', $validRoles)) {
            foreach($karyawanUsers as $user) {
                $user->role = 'employee';
                $user->save();
                $updatedKaryawanCount++;
            }
            echo "<p>Updated {$updatedKaryawanCount} users from 'karyawan' to 'employee'</p>";
        }

        // Create a new karyawan user with correct role
        $role = in_array('karyawan', $validRoles) ? 'karyawan' : 'employee';
        \App\Models\User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Karyawan Toko',
                'password' => \Illuminate\Support\Facades\Hash::make('karyawan123'),
                'role' => $role,
                'created_at' => now(),
                'updated_at' => now()
            ]
        );

        echo "<p>Created/updated karyawan user with role: {$role}</p>";
        echo "<p>Login credentials:</p>";
        echo "<ul>";
        echo "<li>Email: <EMAIL></li>";
        echo "<li>Password: karyawan123</li>";
        echo "</ul>";

        return "<p><a href='/emergency-login-karyawan' class='btn btn-success'>Try Login Now</a></p>";
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});
