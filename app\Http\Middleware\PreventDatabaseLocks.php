<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class PreventDatabaseLocks
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Apply database optimizations for this request
        $this->applyDatabaseOptimizations();
        
        // Check for concurrent operations
        if ($this->shouldPreventConcurrentOperation($request)) {
            return response()->json([
                'error' => 'Operasi sedang berlangsung. Silakan tunggu beberapa saat dan coba lagi.',
                'retry_after' => 3
            ], 429);
        }
        
        // Set operation lock if needed
        $lockKey = $this->setOperationLock($request);
        
        try {
            $response = $next($request);
            
            // Log successful operation
            $this->logOperation($request, 'success');
            
            return $response;
            
        } catch (\Exception $e) {
            // Log failed operation
            $this->logOperation($request, 'failed', $e->getMessage());
            
            // Check if it's a lock timeout error
            if (strpos($e->getMessage(), 'Lock wait timeout') !== false) {
                Log::error('Database lock timeout detected', [
                    'url' => $request->url(),
                    'method' => $request->method(),
                    'user_id' => auth()->id(),
                    'ip' => $request->ip(),
                    'error' => $e->getMessage()
                ]);
                
                return response()->json([
                    'error' => 'Sistem sedang sibuk. Silakan coba lagi dalam beberapa saat.',
                    'retry_after' => 5
                ], 503);
            }
            
            throw $e;
            
        } finally {
            // Always release operation lock
            if ($lockKey) {
                Cache::forget($lockKey);
            }
        }
    }

    /**
     * Apply database optimizations for this request
     */
    private function applyDatabaseOptimizations(): void
    {
        try {
            // Apply session-level optimizations
            DB::statement('SET SESSION innodb_lock_wait_timeout = 5');
            DB::statement('SET SESSION lock_wait_timeout = 5');
            DB::statement('SET SESSION autocommit = 1');
            DB::statement('SET SESSION transaction_isolation = "READ-COMMITTED"');
            
        } catch (\Exception $e) {
            Log::warning('Could not apply database optimizations', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Check if we should prevent concurrent operation
     */
    private function shouldPreventConcurrentOperation(Request $request): bool
    {
        // Define operations that should be prevented from running concurrently
        $criticalOperations = [
            'POST:/transactions',
            'DELETE:/transactions/*',
            'POST:/payment/callback',
            'POST:/payment/create',
        ];
        
        $currentOperation = $request->method() . ':' . $request->path();
        
        foreach ($criticalOperations as $operation) {
            if (fnmatch($operation, $currentOperation)) {
                $lockKey = $this->getOperationLockKey($request);
                return Cache::has($lockKey);
            }
        }
        
        return false;
    }

    /**
     * Set operation lock to prevent concurrent access
     */
    private function setOperationLock(Request $request): ?string
    {
        $criticalOperations = [
            'POST:/transactions',
            'DELETE:/transactions/*',
            'POST:/payment/callback',
            'POST:/payment/create',
            'DELETE:/users/*',
            'POST:/users/*/force-delete',
            'POST:/users/*/restore',
        ];
        
        $currentOperation = $request->method() . ':' . $request->path();
        
        foreach ($criticalOperations as $operation) {
            if (fnmatch($operation, $currentOperation)) {
                $lockKey = $this->getOperationLockKey($request);
                
                // Set lock for 30 seconds
                Cache::put($lockKey, [
                    'user_id' => auth()->id(),
                    'started_at' => now(),
                    'operation' => $currentOperation,
                    'ip' => $request->ip()
                ], 30);
                
                return $lockKey;
            }
        }
        
        return null;
    }

    /**
     * Generate operation lock key
     */
    private function getOperationLockKey(Request $request): string
    {
        $operation = $request->method() . ':' . $request->path();
        $userId = auth()->id() ?? 'guest';
        
        // For transaction operations, use user-specific lock
        if (strpos($operation, 'transactions') !== false) {
            return "operation_lock:transactions:user_{$userId}";
        }
        
        // For payment operations, use global lock
        if (strpos($operation, 'payment') !== false) {
            return "operation_lock:payment:global";
        }
        
        return "operation_lock:" . md5($operation . $userId);
    }

    /**
     * Log operation for monitoring
     */
    private function logOperation(Request $request, string $status, ?string $error = null): void
    {
        $logData = [
            'operation' => $request->method() . ':' . $request->path(),
            'status' => $status,
            'user_id' => auth()->id(),
            'ip' => $request->ip(),
            'duration' => microtime(true) - LARAVEL_START,
            'memory_usage' => memory_get_peak_usage(true),
        ];
        
        if ($error) {
            $logData['error'] = $error;
        }
        
        if ($status === 'success') {
            Log::info('Database operation completed successfully', $logData);
        } else {
            Log::warning('Database operation failed', $logData);
        }
    }
}
