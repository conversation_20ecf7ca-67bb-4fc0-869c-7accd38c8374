<!DOCTYPE html>
<html>
<head>
    <title>Auto Login Test</title>
    <meta name="csrf-token" content="">
</head>
<body>
    <h1>Testing Login Functionality</h1>
    
    <div id="results"></div>
    
    <script>
    async function testLogin() {
        const results = document.getElementById('results');
        
        // Test 1: Admin Login
        results.innerHTML += '<h2>Testing Admin Login...</h2>';
        
        try {
            // Get CSRF token first
            const tokenResponse = await fetch('http://127.0.0.1:8000/login');
            const tokenText = await tokenResponse.text();
            const tokenMatch = tokenText.match(/name="csrf-token" content="([^"]+)"/);
            const csrfToken = tokenMatch ? tokenMatch[1] : '';
            
            // Attempt login
            const loginData = new FormData();
            loginData.append('email', '<EMAIL>');
            loginData.append('password', 'admin123');
            loginData.append('_token', csrfToken);
            
            const loginResponse = await fetch('http://127.0.0.1:8000/login', {
                method: 'POST',
                body: loginData,
                credentials: 'include'
            });
            
            if (loginResponse.ok) {
                results.innerHTML += '<p>✅ Admin login successful</p>';
                
                // Test dashboard access
                const dashboardResponse = await fetch('http://127.0.0.1:8000/dashboard', {
                    credentials: 'include'
                });
                
                if (dashboardResponse.ok) {
                    results.innerHTML += '<p>✅ Dashboard access successful</p>';
                } else {
                    results.innerHTML += '<p>❌ Dashboard access failed</p>';
                }
                
            } else {
                results.innerHTML += '<p>❌ Admin login failed</p>';
            }
            
        } catch (error) {
            results.innerHTML += '<p>❌ Login test error: ' + error.message + '</p>';
        }
    }
    
    // Run test
    testLogin();
    </script>
</body>
</html>
