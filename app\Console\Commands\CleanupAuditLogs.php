<?php

namespace App\Console\Commands;

use App\Models\AuditLog;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CleanupAuditLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'audit:cleanup {--days=365 : Number of days to keep audit logs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old audit logs based on retention policy';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $cutoffDate = Carbon::now()->subDays($days);

        $this->info("Cleaning up audit logs older than {$days} days (before {$cutoffDate->format('Y-m-d H:i:s')})...");

        // Count logs to be deleted
        $count = AuditLog::where('created_at', '<', $cutoffDate)->count();

        if ($count === 0) {
            $this->info('No audit logs found to clean up.');
            return;
        }

        if ($this->confirm("This will delete {$count} audit log(s). Do you want to continue?")) {
            // Delete in chunks to avoid memory issues
            $deleted = 0;
            $chunkSize = 1000;

            do {
                $deletedChunk = AuditLog::where('created_at', '<', $cutoffDate)
                    ->limit($chunkSize)
                    ->delete();
                
                $deleted += $deletedChunk;
                
                if ($deletedChunk > 0) {
                    $this->info("Deleted {$deletedChunk} audit logs... (Total: {$deleted})");
                }
                
            } while ($deletedChunk > 0);

            $this->info("Successfully deleted {$deleted} audit log(s).");
        } else {
            $this->info('Cleanup cancelled.');
        }
    }
}
