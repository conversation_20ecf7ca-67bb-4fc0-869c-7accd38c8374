<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            // Add performance indexes
            $table->index('user_id', 'transactions_user_id_index');
            $table->index('created_at', 'transactions_created_at_index');
            $table->index('status', 'transactions_status_index');
            $table->index('payment_status', 'transactions_payment_status_index');
            $table->index(['user_id', 'created_at'], 'transactions_user_created_index');
            $table->index(['status', 'payment_status'], 'transactions_status_payment_index');
        });

        Schema::table('transaction_items', function (Blueprint $table) {
            // Add performance indexes
            $table->index('transaction_id', 'transaction_items_transaction_id_index');
            $table->index('processed_inventory_id', 'transaction_items_processed_inventory_index');
            $table->index('product_id', 'transaction_items_product_id_index');
        });

        Schema::table('processed_inventories', function (Blueprint $table) {
            // Add performance indexes
            $table->index('current_stock', 'processed_inventories_current_stock_index');
            $table->index('min_stock_threshold', 'processed_inventories_min_stock_index');
            $table->index(['current_stock', 'min_stock_threshold'], 'processed_inventories_stock_threshold_index');
        });

        Schema::table('other_products', function (Blueprint $table) {
            // Add performance indexes
            $table->index('current_stock', 'other_products_current_stock_index');
            $table->index('min_stock_threshold', 'other_products_min_stock_index');
            $table->index(['current_stock', 'min_stock_threshold'], 'other_products_stock_threshold_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropIndex('transactions_user_id_index');
            $table->dropIndex('transactions_created_at_index');
            $table->dropIndex('transactions_status_index');
            $table->dropIndex('transactions_payment_status_index');
            $table->dropIndex('transactions_user_created_index');
            $table->dropIndex('transactions_status_payment_index');
        });

        Schema::table('transaction_items', function (Blueprint $table) {
            $table->dropIndex('transaction_items_transaction_id_index');
            $table->dropIndex('transaction_items_processed_inventory_index');
            $table->dropIndex('transaction_items_product_id_index');
        });

        Schema::table('processed_inventories', function (Blueprint $table) {
            $table->dropIndex('processed_inventories_current_stock_index');
            $table->dropIndex('processed_inventories_min_stock_index');
            $table->dropIndex('processed_inventories_stock_threshold_index');
        });

        Schema::table('other_products', function (Blueprint $table) {
            $table->dropIndex('other_products_current_stock_index');
            $table->dropIndex('other_products_min_stock_index');
            $table->dropIndex('other_products_stock_threshold_index');
        });
    }
};
