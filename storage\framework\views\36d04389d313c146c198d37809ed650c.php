<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-truck me-2"></i>Daftar Distribusi
                        </h4>
                        <div>
                            <a href="<?php echo e(route('distributions.export')); ?>" class="btn btn-outline-light btn-sm me-2">
                                <i class="fas fa-download me-1"></i>Export CSV
                            </a>
                            <a href="<?php echo e(route('distributions.create')); ?>" class="btn btn-light btn-sm">
                                <i class="fas fa-plus me-1"></i>Distribusi Baru
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Statistics Cards - Simplified -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h6 class="card-title mb-0">Total Distribusi</h6>
                                            <h4 class="mb-0"><?php echo e($stats['total'] ?? 0); ?></h4>
                                        </div>
                                        <div class="ms-3">
                                            <i class="fas fa-truck fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h6 class="card-title mb-0">Bulan Ini</h6>
                                            <h4 class="mb-0"><?php echo e($stats['this_month'] ?? 0); ?></h4>
                                        </div>
                                        <div class="ms-3">
                                            <i class="fas fa-calendar-month fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h6 class="card-title mb-0">Hari Ini</h6>
                                            <h4 class="mb-0"><?php echo e($stats['today'] ?? 0); ?></h4>
                                        </div>
                                        <div class="ms-3">
                                            <i class="fas fa-calendar-day fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter & Search -->
                    <form method="GET" action="<?php echo e(route('distributions.index')); ?>" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" name="search" placeholder="Cari distribusi..." value="<?php echo e(request('search')); ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" name="start_date" placeholder="Dari tanggal" value="<?php echo e(request('start_date')); ?>">
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" name="end_date" placeholder="Sampai tanggal" value="<?php echo e(request('end_date')); ?>">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                            </div>
                            <div class="col-md-1">
                                <a href="<?php echo e(route('distributions.index')); ?>" class="btn btn-secondary w-100">Reset</a>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-12 text-end">
                                <span class="badge bg-primary fs-6 px-3 py-2">
                                    Total: <?php echo e($distributions->total()); ?> Distribusi
                                </span>
                            </div>
                        </div>
                    </form>

                    <!-- Distributions Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th width="10%">#</th>
                                    <th width="25%">No. Distribusi</th>
                                    <th width="25%">Tanggal</th>
                                    <th width="25%">Tujuan</th>
                                    <th width="15%">Total Item</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $distributions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $distribution): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($distributions->firstItem() + $index); ?></td>
                                    <td>
                                        <strong><?php echo e($distribution->distribution_number ?? 'DIST-' . $distribution->id); ?></strong>
                                    </td>
                                    <td>
                                        <?php echo e($distribution->distribution_date->format('d/m/Y')); ?>

                                        <br>
                                        <small class="text-muted"><?php echo e($distribution->distribution_date->format('H:i')); ?></small>
                                    </td>
                                    <td>
                                        <strong><?php echo e($distribution->market_name); ?></strong>
                                        <br>
                                        <small class="text-muted"><?php echo e($distribution->user->name ?? 'N/A'); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-success fs-6"><?php echo e($distribution->items->count()); ?> item</span>
                                        <br>
                                        <small class="text-muted"><?php echo e($distribution->items->sum('quantity')); ?> qty</small>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="5" class="text-center py-5">
                                        <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Belum ada distribusi yang tercatat</p>
                                        <a href="<?php echo e(route('distributions.create')); ?>" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i>Buat Distribusi Pertama
                                        </a>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if($distributions->hasPages()): ?>
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($distributions->links('custom.pagination')); ?>

                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: none;
}

.table th {
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
    border-color: #e9ecef;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
}

.btn {
    border-radius: 6px;
    font-weight: 500;
}

.input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.alert {
    border: none;
    border-radius: 8px;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/distributions/index.blade.php ENDPATH**/ ?>