<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Audit log commands will be auto-discovered by <PERSON>vel

// Database maintenance scheduled tasks
Schedule::command('db:clear-locks')
    ->hourly()
    ->between('08:00', '22:00') // Only during business hours
    ->withoutOverlapping()
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/db-maintenance.log'));

Schedule::command('db:maintenance --clear-locks --analyze')
    ->dailyAt('02:00') // Run at 2 AM daily
    ->withoutOverlapping()
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/db-maintenance.log'));

Schedule::command('db:maintenance --optimize')
    ->weekly() // Run weekly optimization
    ->sundays()
    ->at('03:00')
    ->withoutOverlapping()
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/db-maintenance.log'));

// Audit log cleanup - monthly
Schedule::command('audit:cleanup --days=365')
    ->monthly() // Run monthly cleanup
    ->at('04:00')
    ->withoutOverlapping()
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/audit-cleanup.log'));
