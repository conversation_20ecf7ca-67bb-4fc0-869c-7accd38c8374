<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Database maintenance scheduled tasks
Schedule::command('db:clear-locks')
    ->hourly()
    ->between('08:00', '22:00') // Only during business hours
    ->withoutOverlapping()
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/db-maintenance.log'));

Schedule::command('db:maintenance --clear-locks --analyze')
    ->dailyAt('02:00') // Run at 2 AM daily
    ->withoutOverlapping()
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/db-maintenance.log'));

Schedule::command('db:maintenance --optimize')
    ->weekly() // Run weekly optimization
    ->sundays()
    ->at('03:00')
    ->withoutOverlapping()
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/db-maintenance.log'));
