<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-user"></i>
        <span>Detail User: <?php echo e($user->name); ?></span>
    </div>

    <div class="row">
        <!-- User Profile Card -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Profil User</h5>
                </div>
                <div class="card-body text-center">
                    <div class="avatar-lg mb-3">
                        <div class="avatar-title bg-primary rounded-circle">
                            <?php echo e(strtoupper(substr($user->name, 0, 2))); ?>

                        </div>
                    </div>
                    <h4><?php echo e($user->name); ?></h4>
                    <p class="text-muted"><?php echo e($user->email); ?></p>
                    
                    <div class="mb-3">
                        <span class="badge bg-<?php echo e($user->role === 'admin' ? 'danger' : 'primary'); ?> fs-6">
                            <?php echo e($user->role_display); ?>

                        </span>
                    </div>

                    <div class="mb-3">
                        <?php if($user->deleted_at): ?>
                            <span class="badge bg-danger fs-6">Tidak Aktif</span>
                        <?php else: ?>
                            <span class="badge bg-success fs-6">Aktif</span>
                        <?php endif; ?>
                    </div>

                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('users.edit', $user)); ?>" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit User
                        </a>
                        <?php if($user->id !== auth()->id() && !$user->deleted_at): ?>
                            <form action="<?php echo e(route('users.destroy', $user)); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-danger w-100"
                                        onclick="return confirm('Yakin ingin menghapus user ini?')">
                                    <i class="fas fa-trash"></i> Hapus User
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Information -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Informasi Detail</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>ID User:</strong></td>
                                    <td><?php echo e($user->id); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Nama Lengkap:</strong></td>
                                    <td><?php echo e($user->name); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td><?php echo e($user->email); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Role:</strong></td>
                                    <td>
                                        <span class="badge bg-<?php echo e($user->role === 'admin' ? 'danger' : 'primary'); ?>">
                                            <?php echo e($user->role_display); ?>

                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Dibuat:</strong></td>
                                    <td><?php echo e($user->created_at->format('d/m/Y H:i')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Terakhir Update:</strong></td>
                                    <td><?php echo e($user->updated_at->format('d/m/Y H:i')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Terakhir Aktif:</strong></td>
                                    <td>
                                        <?php if($user->last_activity): ?>
                                            <?php echo e($user->last_activity->format('d/m/Y H:i')); ?>

                                            <br><small class="text-muted">(<?php echo e($user->last_activity->diffForHumans()); ?>)</small>
                                        <?php else: ?>
                                            <span class="text-muted">Belum pernah login</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <?php if($user->deleted_at): ?>
                                            <span class="badge bg-danger">Tidak Aktif</span>
                                            <br><small class="text-muted">Dihapus: <?php echo e($user->deleted_at->format('d/m/Y H:i')); ?></small>
                                        <?php else: ?>
                                            <span class="badge bg-success">Aktif</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Statistik Aktivitas</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stats-card-small">
                                <div class="stats-icon-small primary">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="stats-info-small">
                                    <div class="stats-title-small">Total Transaksi</div>
                                    <h4 class="stats-value-small"><?php echo e($stats['total_transactions']); ?></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card-small">
                                <div class="stats-icon-small success">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="stats-info-small">
                                    <div class="stats-title-small">Total Penjualan</div>
                                    <h4 class="stats-value-small">Rp <?php echo e(number_format($stats['total_sales'], 0, ',', '.')); ?></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card-small">
                                <div class="stats-icon-small info">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="stats-info-small">
                                    <div class="stats-title-small">Umur Akun</div>
                                    <h4 class="stats-value-small"><?php echo e($stats['account_age']); ?></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card-small">
                                <div class="stats-icon-small warning">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stats-info-small">
                                    <div class="stats-title-small">Login Terakhir</div>
                                    <h4 class="stats-value-small">
                                        <?php if($stats['last_login']): ?>
                                            <?php echo e($stats['last_login']->diffForHumans()); ?>

                                        <?php else: ?>
                                            Belum pernah
                                        <?php endif; ?>
                                    </h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <?php if($user->transactions->count() > 0): ?>
            <div class="card">
                <div class="card-header">
                    <h5>Transaksi Terbaru</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Invoice</th>
                                    <th>Tanggal</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $user->transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($transaction->invoice_number); ?></td>
                                    <td><?php echo e($transaction->created_at->format('d/m/Y H:i')); ?></td>
                                    <td>Rp <?php echo e(number_format($transaction->total_amount, 0, ',', '.')); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo e($transaction->status === 'completed' ? 'success' : 'warning'); ?>">
                                            <?php echo e(ucfirst($transaction->status)); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?php echo e(route('transactions.show', $transaction)); ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <?php if($stats['total_transactions'] > 10): ?>
                        <div class="text-center mt-3">
                            <a href="<?php echo e(route('transactions.index', ['user_id' => $user->id])); ?>" class="btn btn-outline-primary">
                                Lihat Semua Transaksi (<?php echo e($stats['total_transactions']); ?>)
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <a href="<?php echo e(route('users.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali ke Daftar User
            </a>
        </div>
    </div>
</div>

<style>
.page-title {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: bold;
    color: #495057;
}

.page-title i {
    margin-right: 15px;
    color: #17a2b8;
}

.avatar-lg {
    width: 80px;
    height: 80px;
    margin: 0 auto;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    font-size: 24px;
}

.stats-card-small {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    height: 100%;
}

.stats-icon-small {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    font-size: 18px;
    color: white;
}

.stats-icon-small.primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stats-icon-small.success { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stats-icon-small.info { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.stats-icon-small.warning { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }

.stats-title-small {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
}

.stats-value-small {
    font-size: 18px;
    font-weight: bold;
    color: #495057;
    margin: 0;
}

.card {
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 10px;
}

.card-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    border: none;
}

.table-borderless td {
    border: none;
    padding: 8px 0;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/admin/users/show.blade.php ENDPATH**/ ?>