<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-title d-flex justify-content-between align-items-center mb-3">
        <div>
            <i class="fas fa-chart-line"></i>
            <span><PERSON><PERSON><PERSON><PERSON></span>
        </div>
        <div class="action-buttons">
            <button class="btn btn-outline-success" onclick="window.print()">
                <i class="fas fa-print"></i> Cetak
            </button>
            <button class="btn btn-outline-primary" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i> Export Excel
            </button>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <span>Parameter Proyeksi</span>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('reports.financial-projection')); ?>" method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="months" class="form-label">Jumlah Bulan</label>
                            <select class="form-select" id="months" name="months">
                                <option value="3" <?php echo e($months == 3 ? 'selected' : ''); ?>>3 Bulan</option>
                                <option value="6" <?php echo e($months == 6 ? 'selected' : ''); ?>>6 Bulan</option>
                                <option value="12" <?php echo e($months == 12 ? 'selected' : ''); ?>>12 Bulan</option>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label for="growth_rate" class="form-label">Tingkat Pertumbuhan (%)</label>
                            <input type="number" step="0.1" class="form-control" id="growth_rate" name="growth_rate" value="<?php echo e($growthRate); ?>" min="0" max="100">
                            <div class="form-text">Proyeksi pertumbuhan pendapatan per bulan</div>
                        </div>

                        <div class="col-md-3">
                            <label for="cost_increase_rate" class="form-label">Tingkat Kenaikan Biaya (%)</label>
                            <input type="number" step="0.1" class="form-control" id="cost_increase_rate" name="cost_increase_rate" value="<?php echo e($costIncreaseRate); ?>" min="0" max="100">
                            <div class="form-text">Proyeksi kenaikan biaya per bulan</div>
                        </div>

                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-calculator me-2"></i> Hitung Ulang
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <span>Ringkasan Proyeksi (<?php echo e($months); ?> Bulan Kedepan)</span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="stats-card">
                                <div class="stats-icon success">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="stats-info">
                                    <div class="stats-title">Total Pendapatan Proyeksi</div>
                                    <h3 class="stats-value">Rp <?php echo e(number_format($summary['total_projected_revenue'], 0, ',', '.')); ?></h3>
                                    <div class="stats-subtitle">Rata-rata Rp <?php echo e(number_format($summary['average_monthly_revenue'], 0, ',', '.')); ?>/bulan</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="stats-card">
                                <div class="stats-icon primary">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="stats-info">
                                    <div class="stats-title">Total Laba Proyeksi</div>
                                    <h3 class="stats-value">Rp <?php echo e(number_format($summary['total_projected_profit'], 0, ',', '.')); ?></h3>
                                    <div class="stats-subtitle">Rata-rata Rp <?php echo e(number_format($summary['average_monthly_profit'], 0, ',', '.')); ?>/bulan</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="stats-card">
                                <div class="stats-icon warning">
                                    <i class="fas fa-percentage"></i>
                                </div>
                                <div class="stats-info">
                                    <div class="stats-title">Margin Laba Rata-rata</div>
                                    <h3 class="stats-value"><?php echo e(number_format($summary['average_profit_margin'], 1)); ?>%</h3>
                                    <div class="stats-subtitle">Dengan pertumbuhan <?php echo e($growthRate); ?>% per bulan</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Grafik Proyeksi -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <span>Grafik Proyeksi Keuangan (<?php echo e($months); ?> Bulan)</span>
                                    <div>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-primary active" id="viewRevenue">Pendapatan</button>
                                            <button type="button" class="btn btn-outline-primary" id="viewExpense">Biaya</button>
                                            <button type="button" class="btn btn-outline-primary" id="viewProfit">Laba</button>
                                            <button type="button" class="btn btn-outline-primary" id="viewAll">Semua</button>
                                        </div>
                                        <div class="btn-group btn-group-sm ms-2" role="group">
                                            <button type="button" class="btn btn-outline-secondary active" id="chartLine">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" id="chartBar">
                                                <i class="fas fa-chart-bar"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" id="chartArea">
                                                <i class="fas fa-chart-area"></i>
                                            </button>
                                        </div>
                                        <div class="btn-group btn-group-sm ms-2" role="group">
                                            <button type="button" class="btn btn-outline-info" id="compareScenarios">
                                                <i class="fas fa-random me-1"></i> Bandingkan Skenario
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="chart-container" style="position: relative; height: 300px;">
                                        <canvas id="projectionChart" height="300"></canvas>
                                    </div>
                                    <div id="chart-error" class="alert alert-danger text-center" style="display: none;">
                                        <p><i class="fas fa-exclamation-triangle me-2"></i> <span id="error-message">Error saat membuat grafik</span></p>
                                        <button id="refresh-chart" class="btn btn-sm btn-danger mt-2">
                                            <i class="fas fa-sync-alt me-1"></i> Refresh Halaman
                                        </button>
                                    </div>
                                </div>
                                <div class="card-footer bg-white p-2">
                                    <div class="chart-legend d-flex justify-content-center flex-wrap">
                                        <div class="px-3 py-1"><span class="legend-indicator" style="background-color: rgba(76, 175, 80, 1)"></span> Pendapatan</div>
                                        <div class="px-3 py-1"><span class="legend-indicator" style="background-color: rgba(220, 53, 69, 1)"></span> Biaya</div>
                                        <div class="px-3 py-1"><span class="legend-indicator" style="background-color: rgba(13, 110, 253, 1)"></span> Laba</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Skenario Proyeksi -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <span>Skenario Proyeksi</span>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100 border-success">
                                                <div class="card-header bg-success text-white">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-arrow-trend-up me-2"></i>
                                                        <span class="fw-bold">Skenario Optimis</span>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3 pb-2 border-bottom">
                                                        <div class="small text-muted mb-1">Asumsi:</div>
                                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                                            <span>Pertumbuhan Pendapatan:</span>
                                                            <span class="badge bg-success"><?php echo e($scenarios['optimistic']['revenue_growth']); ?>%</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span>Kenaikan Biaya:</span>
                                                            <span class="badge bg-success"><?php echo e($scenarios['optimistic']['cost_increase']); ?>%</span>
                                                        </div>
                                                    </div>
                                                    <div class="small text-muted mb-1">Hasil Proyeksi:</div>
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <span>Total Pendapatan:</span>
                                                        <span class="fw-bold text-success">Rp <?php echo e(number_format($scenarios['optimistic']['total_revenue'], 0, ',', '.')); ?></span>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <span>Total Laba:</span>
                                                        <span class="fw-bold text-success">Rp <?php echo e(number_format($scenarios['optimistic']['total_profit'], 0, ',', '.')); ?></span>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <span>Margin Laba:</span>
                                                        <span class="fw-bold text-success"><?php echo e(number_format(($scenarios['optimistic']['total_profit'] / $scenarios['optimistic']['total_revenue']) * 100, 1)); ?>%</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100 border-primary">
                                                <div class="card-header bg-primary text-white">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-arrow-right me-2"></i>
                                                        <span class="fw-bold">Skenario Normal</span>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3 pb-2 border-bottom">
                                                        <div class="small text-muted mb-1">Asumsi:</div>
                                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                                            <span>Pertumbuhan Pendapatan:</span>
                                                            <span class="badge bg-primary"><?php echo e($scenarios['normal']['revenue_growth']); ?>%</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span>Kenaikan Biaya:</span>
                                                            <span class="badge bg-primary"><?php echo e($scenarios['normal']['cost_increase']); ?>%</span>
                                                        </div>
                                                    </div>
                                                    <div class="small text-muted mb-1">Hasil Proyeksi:</div>
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <span>Total Pendapatan:</span>
                                                        <span class="fw-bold text-primary">Rp <?php echo e(number_format($scenarios['normal']['total_revenue'], 0, ',', '.')); ?></span>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <span>Total Laba:</span>
                                                        <span class="fw-bold text-primary">Rp <?php echo e(number_format($scenarios['normal']['total_profit'], 0, ',', '.')); ?></span>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <span>Margin Laba:</span>
                                                        <span class="fw-bold text-primary"><?php echo e(number_format(($scenarios['normal']['total_profit'] / $scenarios['normal']['total_revenue']) * 100, 1)); ?>%</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100 border-danger">
                                                <div class="card-header bg-danger text-white">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-arrow-trend-down me-2"></i>
                                                        <span class="fw-bold">Skenario Pesimis</span>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3 pb-2 border-bottom">
                                                        <div class="small text-muted mb-1">Asumsi:</div>
                                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                                            <span>Pertumbuhan Pendapatan:</span>
                                                            <span class="badge bg-danger"><?php echo e($scenarios['pessimistic']['revenue_growth']); ?>%</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span>Kenaikan Biaya:</span>
                                                            <span class="badge bg-danger"><?php echo e($scenarios['pessimistic']['cost_increase']); ?>%</span>
                                                        </div>
                                                    </div>
                                                    <div class="small text-muted mb-1">Hasil Proyeksi:</div>
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <span>Total Pendapatan:</span>
                                                        <span class="fw-bold text-danger">Rp <?php echo e(number_format($scenarios['pessimistic']['total_revenue'], 0, ',', '.')); ?></span>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <span>Total Laba:</span>
                                                        <span class="fw-bold text-danger">Rp <?php echo e(number_format($scenarios['pessimistic']['total_profit'], 0, ',', '.')); ?></span>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <span>Margin Laba:</span>
                                                        <span class="fw-bold text-danger"><?php echo e(number_format(($scenarios['pessimistic']['total_profit'] / $scenarios['pessimistic']['total_revenue']) * 100, 1)); ?>%</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabel Proyeksi Detail -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <span>Detail Proyeksi Bulanan</span>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-secondary btn-sm" onclick="sortTable('asc')">
                                            <i class="fas fa-sort-amount-up-alt"></i> Urutkan Naik
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" onclick="sortTable('desc')">
                                            <i class="fas fa-sort-amount-down"></i> Urutkan Turun
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover" id="projectionTable">
                                            <thead class="table-light">
                                                <tr>
                                                    <th class="text-center">Bulan</th>
                                                    <th class="text-end">Pendapatan</th>
                                                    <th class="text-end">HPP</th>
                                                    <th class="text-end">Laba Kotor</th>
                                                    <th class="text-end">Biaya Operasional</th>
                                                    <th class="text-end">Laba Bersih</th>
                                                    <th class="text-center">Margin (%)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $projections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $projection): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td class="text-center fw-medium"><?php echo e($projection['month']); ?></td>
                                                    <td class="text-end">Rp <?php echo e(number_format($projection['revenue'], 0, ',', '.')); ?></td>
                                                    <td class="text-end">Rp <?php echo e(number_format($projection['cogs'], 0, ',', '.')); ?></td>
                                                    <td class="text-end">Rp <?php echo e(number_format($projection['gross_profit'], 0, ',', '.')); ?></td>
                                                    <td class="text-end">Rp <?php echo e(number_format($projection['operating_expense'], 0, ',', '.')); ?></td>
                                                    <td class="text-end <?php echo e($projection['net_profit'] > 0 ? 'text-success' : 'text-danger'); ?> fw-bold">Rp <?php echo e(number_format($projection['net_profit'], 0, ',', '.')); ?></td>
                                                    <td class="text-center">
                                                        <span class="badge <?php echo e($projection['profit_margin'] >= 15 ? 'bg-success' : ($projection['profit_margin'] >= 10 ? 'bg-primary' : ($projection['profit_margin'] >= 5 ? 'bg-warning' : 'bg-danger'))); ?>">
                                                            <?php echo e($projection['profit_margin']); ?>%
                                                        </span>
                                                    </td>
                                                </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                            <tfoot class="table-light fw-bold">
                                                <tr>
                                                    <td class="text-center">Total</td>
                                                    <td class="text-end">Rp <?php echo e(number_format(collect($projections)->sum('revenue'), 0, ',', '.')); ?></td>
                                                    <td class="text-end">Rp <?php echo e(number_format(collect($projections)->sum('cogs'), 0, ',', '.')); ?></td>
                                                    <td class="text-end">Rp <?php echo e(number_format(collect($projections)->sum('gross_profit'), 0, ',', '.')); ?></td>
                                                    <td class="text-end">Rp <?php echo e(number_format(collect($projections)->sum('operating_expense'), 0, ',', '.')); ?></td>
                                                    <td class="text-end <?php echo e(collect($projections)->sum('net_profit') > 0 ? 'text-success' : 'text-danger'); ?>">Rp <?php echo e(number_format(collect($projections)->sum('net_profit'), 0, ',', '.')); ?></td>
                                                    <td class="text-center">
                                                        <span class="badge <?php echo e(collect($projections)->avg('profit_margin') >= 15 ? 'bg-success' : (collect($projections)->avg('profit_margin') >= 10 ? 'bg-primary' : (collect($projections)->avg('profit_margin') >= 5 ? 'bg-warning' : 'bg-danger'))); ?>">
                                                            <?php echo e(number_format(collect($projections)->avg('profit_margin'), 1)); ?>%
                                                        </span>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<!-- Ensure Chart.js is loaded - with fallback options -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script>
    // Global variable for the chart instance to make sure we can properly destroy it
    let projectionChart = null;
    
    // Check if Chart.js was loaded successfully
    if (typeof Chart === 'undefined') {
        console.error('Chart.js not loaded! Loading fallback...');
        // Create a new script element for fallback
        const script = document.createElement('script');
        script.src = '<?php echo e(asset("assets/js/chart.min.js")); ?>';
        script.async = true;
        script.onload = function() {
            console.log('Fallback Chart.js loaded successfully');
            setTimeout(initializeChartAfterLoad, 300);
        };
        script.onerror = function() {
            console.error('Failed to load fallback Chart.js');
            showChartError('Failed to load Chart.js library');
        };
        document.head.appendChild(script);
    } else {
        console.log('Chart.js loaded successfully');
        // Delay slightly for DOM to be fully ready
        setTimeout(initializeChartAfterLoad, 300);
    }
    
    // Handle refresh button click
    document.getElementById('refresh-chart').addEventListener('click', function() {
        window.location.reload();
    });

    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, preparing chart initialization...');

        // If Chart.js was already loaded, initialize directly with a delay to ensure readiness
        if (typeof Chart !== 'undefined') {
            // Set a more reliable delay to ensure DOM and Chart.js are fully ready
            setTimeout(initializeChartAfterLoad, 300);
        }
    });

    // Function to handle chart initialization
    function initializeChartAfterLoad() {
        try {
            // Check if canvas element exists
            const canvas = document.getElementById('projectionChart');
            if (!canvas) {
                console.error('Canvas element "projectionChart" not found!');
                showChartError('Canvas element not found');
                return;
            }

            console.log('Canvas found, preparing chart data...');

            // Data proyeksi
            const projectionChartData = <?php echo json_encode($projectionChartData, 15, 512) ?>;

            if (!projectionChartData || !projectionChartData.labels || !projectionChartData.revenue) {
                console.error('Invalid chart data!', projectionChartData);
                showChartError('Invalid chart data');
                return;
            }

            console.log('Chart data loaded:', projectionChartData);

            // Warna-warna untuk grafik dengan peningkatan opacity dan gradients
            const chartColors = {
                revenue: {
                    backgroundColor: 'rgba(76, 175, 80, 0.2)',
                    borderColor: 'rgba(76, 175, 80, 1)',
                    hoverBackgroundColor: 'rgba(76, 175, 80, 0.4)',
                    pointBackgroundColor: 'rgba(76, 175, 80, 1)',
                    pointHoverBorderColor: 'rgba(76, 175, 80, 0.8)'
                },
                expense: {
                    backgroundColor: 'rgba(220, 53, 69, 0.2)',
                    borderColor: 'rgba(220, 53, 69, 1)',
                    hoverBackgroundColor: 'rgba(220, 53, 69, 0.4)',
                    pointBackgroundColor: 'rgba(220, 53, 69, 1)',
                    pointHoverBorderColor: 'rgba(220, 53, 69, 0.8)'
                },
                profit: {
                    backgroundColor: 'rgba(13, 110, 253, 0.2)',
                    borderColor: 'rgba(13, 110, 253, 1)',
                    hoverBackgroundColor: 'rgba(13, 110, 253, 0.4)',
                    pointBackgroundColor: 'rgba(13, 110, 253, 1)',
                    pointHoverBorderColor: 'rgba(13, 110, 253, 0.8)'
                },
                // Warna untuk skenario dengan gradients yang lebih menarik
                scenarios: {
                    optimistic: {
                        backgroundColor: 'rgba(40, 167, 69, 0.15)',
                        borderColor: 'rgba(40, 167, 69, 0.8)',
                        borderDash: []
                    },
                    normal: {
                        backgroundColor: 'rgba(0, 123, 255, 0.15)',
                        borderColor: 'rgba(0, 123, 255, 0.8)',
                        borderDash: []
                    },
                    pessimistic: {
                        backgroundColor: 'rgba(220, 53, 69, 0.15)',
                        borderColor: 'rgba(220, 53, 69, 0.8)',
                        borderDash: []
                    }
                }
            };

            // Variables for chart state
            let currentChartType = 'line';
            let currentView = 'revenue';
            let isComparingScenarios = false;
            // Gunakan global projectionChart yang sudah dideklarasikan di awal
            // untuk memastikan kita selalu bisa menghancurkan instance chart sebelumnya

            // Initialize everything
            console.log('Setting up event listeners and initializing chart...');
            initializeChart();
            setupEventListeners();

            // Fungsi untuk inisialisasi grafik dengan animasi lebih menarik
            function initializeChart() {
                try {
                    // Properly destroy existing chart if it exists
                    if (projectionChart) {
                        console.log('Destroying existing chart...');
                        projectionChart.destroy();
                        projectionChart = null;
                    }
                    
                    // Clear chart container and recreate canvas element
                    const chartContainer = document.getElementById('chart-container');
                    chartContainer.innerHTML = '';
                    const newCanvas = document.createElement('canvas');
                    newCanvas.id = 'projectionChart';
                    newCanvas.height = 300;
                    chartContainer.appendChild(newCanvas);
                    
                    // Get fresh context from new canvas
                    try {
                        const canvas = document.getElementById('projectionChart');
                        if (!canvas) {
                            throw new Error('Canvas element not found');
                        }
                        
                        // Explicitly declare ctx as a variable in the current scope
                        window.ctx = canvas.getContext('2d');
                        if (!window.ctx) {
                            throw new Error('Could not get canvas context');
                        }
                        
                        console.log('Canvas context successfully obtained');
                    } catch (error) {
                        console.error('Canvas context error:', error);
                        showChartError('Canvas context not available: ' + error.message);
                        return;
                    }
                    
                    // Hide error message if it was showing
                    document.getElementById('chart-error').style.display = 'none';
                    
                    console.log('Canvas prepared for new chart...');
                } catch (e) {
                    console.error('Error initializing chart:', e);
                    showChartError('Error initializing chart: ' + e.message);
                    return;
                }
                
                // Dataset default with improved styling
                // Gunakan variabel ctx yang telah didefinisikan di window
                var ctx = window.ctx;
                const datasets = [
                    {
                        label: 'Pendapatan',
                        data: projectionChartData.revenue,
                        backgroundColor: chartColors.revenue.backgroundColor,
                        borderColor: chartColors.revenue.borderColor,
                        borderWidth: 2,
                        tension: 0.4, // Increased for smoother curves
                        pointBackgroundColor: chartColors.revenue.pointBackgroundColor,
                        pointBorderColor: 'rgba(255, 255, 255, 1)',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: chartColors.revenue.pointHoverBorderColor,
                        pointRadius: 4,
                        pointHoverRadius: 7,
                        fill: currentChartType === 'area'
                    },
                    {
                        label: 'Biaya',
                        data: projectionChartData.expense,
                        backgroundColor: chartColors.expense.backgroundColor,
                        borderColor: chartColors.expense.borderColor,
                        borderWidth: 2,
                        tension: 0.4,
                        pointBackgroundColor: chartColors.expense.pointBackgroundColor,
                        pointBorderColor: 'rgba(255, 255, 255, 1)',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: chartColors.expense.pointHoverBorderColor,
                        pointRadius: 4,
                        pointHoverRadius: 7,
                        fill: currentChartType === 'area'
                    },
                    {
                        label: 'Laba',
                        data: projectionChartData.profit,
                        backgroundColor: chartColors.profit.backgroundColor,
                        borderColor: chartColors.profit.borderColor,
                        borderWidth: 2,
                        tension: 0.4,
                        pointBackgroundColor: chartColors.profit.pointBackgroundColor,
                        pointBorderColor: 'rgba(255, 255, 255, 1)',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: chartColors.profit.pointHoverBorderColor,
                        pointRadius: 4,
                        pointHoverRadius: 7,
                        fill: currentChartType === 'area'
                    }
                ];

                // Filter dataset berdasarkan tampilan yang dipilih
                const filteredDatasets = filterDatasetsByView(datasets);

                // Prepare chart configuration with enhanced animations and styling
                const chartConfig = {
                    type: currentChartType === 'area' ? 'line' : currentChartType,
                    data: {
                        labels: projectionChartData.labels,
                        datasets: filteredDatasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: {
                            duration: 1200,
                            easing: 'easeOutQuart',
                            delay: function(context) {
                                // Add staggered animation for a more dynamic effect
                                return context.dataIndex * 50;
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)',
                                    drawBorder: false
                                },
                                ticks: {
                                    callback: function(value) {
                                        return 'Rp ' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
                                    },
                                    font: {
                                        size: 11
                                    },
                                    color: '#666'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 11
                                    },
                                    color: '#666'
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false,
                            },
                            tooltip: {
                                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                titleColor: '#333',
                                titleFont: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                bodyColor: '#555',
                                bodyFont: {
                                    size: 13
                                },
                                borderColor: 'rgba(200, 200, 200, 0.75)',
                                borderWidth: 1,
                                cornerRadius: 8,
                                padding: 12,
                                caretSize: 6,
                                boxWidth: 15,
                                boxHeight: 10,
                                boxPadding: 3,
                                usePointStyle: true,
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        label += 'Rp ' + context.parsed.y.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
                                        return label;
                                    }
                                }
                            }
                        },
                        interaction: {
                            mode: 'index',
                            intersect: false
                        },
                        hover: {
                            mode: 'nearest',
                            intersect: true
                        }
                    }
                };

                console.log('Creating chart with configuration:', chartConfig);

                try {
                    // Create and animate the new chart
                    projectionChart = new Chart(ctx, chartConfig);
                    console.log('Chart created successfully');

                    // Update the legend to match current view
                    updateLegend();
                } catch(err) {
                    console.error('Error creating chart:', err);
                    showChartError(err.message);
                }
            }

            // Filter datasets based on selected view
            function filterDatasetsByView(datasets) {
                if (currentView === 'all') {
                    return datasets;
                } else if (currentView === 'revenue') {
                    return [datasets[0]];
                } else if (currentView === 'expense') {
                    return [datasets[1]];
                } else if (currentView === 'profit') {
                    return [datasets[2]];
                }
                return datasets;
            }

            // Setup all event listeners for chart controls
            function setupEventListeners() {
                // Event handlers for view buttons
                const viewAll = document.getElementById('viewAll');
                const viewRevenue = document.getElementById('viewRevenue');
                const viewExpense = document.getElementById('viewExpense');
                const viewProfit = document.getElementById('viewProfit');

                setupButtonListener(viewAll, 'all');
                setupButtonListener(viewRevenue, 'revenue');
                setupButtonListener(viewExpense, 'expense');
                setupButtonListener(viewProfit, 'profit');

                // Chart type buttons
                const chartLine = document.getElementById('chartLine');
                const chartBar = document.getElementById('chartBar');
                const chartArea = document.getElementById('chartArea');

                setupChartTypeListener(chartLine, 'line');
                setupChartTypeListener(chartBar, 'bar');
                setupChartTypeListener(chartArea, 'area');

                // Scenario comparison button
                const compareScenarios = document.getElementById('compareScenarios');
                if (compareScenarios) {
                    compareScenarios.addEventListener('click', function() {
                        isComparingScenarios = !isComparingScenarios;

                        if (isComparingScenarios) {
                            this.classList.add('active');
                            showScenariosComparison();
                        } else {
                            this.classList.remove('active');
                            currentView = 'all';
                            if (viewAll) setActiveView(viewAll);
                            initializeChart();
                        }
                    });
                }

                console.log('Event listeners setup completed');
            }

            // Helper for view button setup
            function setupButtonListener(button, viewName) {
                if (button) {
                    button.addEventListener('click', function() {
                        setActiveView(this);
                        currentView = viewName;
                        isComparingScenarios = false;
                        initializeChart();
                    });
                }
            }

            // Helper for chart type button setup
            function setupChartTypeListener(button, chartType) {
                if (button) {
                    button.addEventListener('click', function() {
                        setActiveChartType(this);
                        currentChartType = chartType;
                        initializeChart();
                    });
                }
            }

            // Show comparison between scenarios with enhanced animations
            function showScenariosComparison() {
                const ctx = document.getElementById('projectionChart').getContext('2d');

                if (projectionChart) {
                    projectionChart.destroy();
                }

                // Generate scenario data with improved visual distinction
                const profitDataNormal = projectionChartData.profit;

                // Pesimis: 70% dari normal
                const profitDataPessimistic = profitDataNormal.map(value => value * 0.7);

                // Optimis: 130% dari normal
                const profitDataOptimistic = profitDataNormal.map(value => value * 1.3);

                const datasets = [
                    {
                        label: 'Skenario Optimis',
                        data: profitDataOptimistic,
                        backgroundColor: chartColors.scenarios.optimistic.backgroundColor,
                        borderColor: chartColors.scenarios.optimistic.borderColor,
                        borderWidth: 3,
                        borderDash: chartColors.scenarios.optimistic.borderDash,
                        tension: 0.4,
                        fill: true,
                        pointRadius: 5,
                        pointHoverRadius: 8
                    },
                    {
                        label: 'Skenario Normal',
                        data: profitDataNormal,
                        backgroundColor: chartColors.scenarios.normal.backgroundColor,
                        borderColor: chartColors.scenarios.normal.borderColor,
                        borderWidth: 3,
                        borderDash: chartColors.scenarios.normal.borderDash,
                        tension: 0.4,
                        fill: true,
                        pointRadius: 5,
                        pointHoverRadius: 8
                    },
                    {
                        label: 'Skenario Pesimis',
                        data: profitDataPessimistic,
                        backgroundColor: chartColors.scenarios.pessimistic.backgroundColor,
                        borderColor: chartColors.scenarios.pessimistic.borderColor,
                        borderWidth: 3,
                        borderDash: chartColors.scenarios.pessimistic.borderDash,
                        tension: 0.4,
                        fill: true,
                        pointRadius: 5,
                        pointHoverRadius: 8
                    }
                ];

                // Create enhanced chart for scenario comparison
                projectionChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: projectionChartData.labels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: {
                            duration: 1200,
                            easing: 'easeOutQuart',
                            delay: function(context) {
                                return context.datasetIndex * 150;
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)',
                                    drawBorder: false
                                },
                                ticks: {
                                    callback: function(value) {
                                        return 'Rp ' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
                                    },
                                    font: {
                                        size: 11
                                    },
                                    color: '#666'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 11
                                    },
                                    color: '#666'
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top',
                                labels: {
                                    usePointStyle: true,
                                    padding: 15,
                                    boxWidth: 10,
                                    boxHeight: 10
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                titleColor: '#333',
                                titleFont: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                bodyColor: '#555',
                                bodyFont: {
                                    size: 13
                                },
                                borderColor: 'rgba(200, 200, 200, 0.75)',
                                borderWidth: 1,
                                cornerRadius: 8,
                                padding: 12,
                                caretSize: 6,
                                boxWidth: 15,
                                boxHeight: 10,
                                boxPadding: 3,
                                usePointStyle: true,
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        label += 'Rp ' + context.parsed.y.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
                                        return label;
                                    }
                                }
                            }
                        },
                        interaction: {
                            mode: 'index',
                            intersect: false
                        },
                        hover: {
                            mode: 'nearest',
                            intersect: true
                        }
                    }
                });

                // Update the legend for scenarios
                updateLegendForScenarios();
            }

            // Set the active view button
            function setActiveView(element) {
                document.querySelectorAll('#viewRevenue, #viewExpense, #viewProfit, #viewAll').forEach(btn => {
                    if (btn) btn.classList.remove('active');
                });
                if (element) element.classList.add('active');

                // Reset scenario comparison if active
                if (isComparingScenarios) {
                    isComparingScenarios = false;
                    const compareBtn = document.getElementById('compareScenarios');
                    if (compareBtn) compareBtn.classList.remove('active');
                }

                // Update the legend
                updateLegend();
            }

            // Set the active chart type button
            function setActiveChartType(element) {
                document.querySelectorAll('#chartLine, #chartBar, #chartArea').forEach(btn => {
                    if (btn) btn.classList.remove('active');
                });
                if (element) element.classList.add('active');
            }

            // Update legend based on active view
            function updateLegend() {
                const legendContainer = document.querySelector('.chart-legend');
                if (!legendContainer) return;

                switch(currentView) {
                    case 'all':
                        legendContainer.innerHTML = `
                            <div class="px-3 py-1"><span class="legend-indicator" style="background-color: ${chartColors.revenue.borderColor}"></span> Pendapatan</div>
                            <div class="px-3 py-1"><span class="legend-indicator" style="background-color: ${chartColors.expense.borderColor}"></span> Biaya</div>
                            <div class="px-3 py-1"><span class="legend-indicator" style="background-color: ${chartColors.profit.borderColor}"></span> Laba</div>
                        `;
                        break;
                    case 'revenue':
                        legendContainer.innerHTML = `
                            <div class="px-3 py-1"><span class="legend-indicator" style="background-color: ${chartColors.revenue.borderColor}"></span> Pendapatan</div>
                        `;
                        break;
                    case 'expense':
                        legendContainer.innerHTML = `
                            <div class="px-3 py-1"><span class="legend-indicator" style="background-color: ${chartColors.expense.borderColor}"></span> Biaya</div>
                        `;
                        break;
                    case 'profit':
                        legendContainer.innerHTML = `
                            <div class="px-3 py-1"><span class="legend-indicator" style="background-color: ${chartColors.profit.borderColor}"></span> Laba</div>
                        `;
                        break;
                }
            }

            // Update legend for scenario comparison
            function updateLegendForScenarios() {
                const legendContainer = document.querySelector('.chart-legend');
                if (legendContainer) {
                    legendContainer.innerHTML = `
                        <div class="px-3 py-1"><span class="legend-indicator" style="background-color: ${chartColors.scenarios.optimistic.borderColor}"></span> Skenario Optimis</div>
                        <div class="px-3 py-1"><span class="legend-indicator" style="background-color: ${chartColors.scenarios.normal.borderColor}"></span> Skenario Normal</div>
                        <div class="px-3 py-1"><span class="legend-indicator" style="background-color: ${chartColors.scenarios.pessimistic.borderColor}"></span> Skenario Pesimis</div>
                    `;
                }
            }
        } catch(err) {
            console.error('Error in chart setup:', err);
            showChartError(err.message);
        }
    }

    // Display error message when chart fails to load
    function showChartError(errorMessage) {
        try {
            // Tampilkan pesan error
            const errorDiv = document.getElementById('chart-error');
            const errorMessageEl = document.getElementById('error-message');
            
            if (errorDiv && errorMessageEl) {
                errorMessageEl.textContent = errorMessage || 'Terjadi kesalahan saat membuat grafik';
                errorDiv.style.display = 'block';
                
                // Sembunyikan canvas jika masih ada
                const canvas = document.getElementById('projectionChart');
                if (canvas) {
                    canvas.style.display = 'none';
                }
            } else {
                console.error('Error elements not found in DOM');
            }
            
            // Log error untuk debugging
            console.error('Chart error:', errorMessage);
        } catch (e) {
            console.error('Error in showChartError function:', e);
        }
    }

    // Function to sort the projection table
    function sortTable(direction) {
        const table = document.getElementById('projectionTable');
        if (!table) return;

        const tbody = table.querySelector('tbody');
        if (!tbody) return;

        const rows = Array.from(tbody.querySelectorAll('tr'));

        // Sort the rows
        rows.sort((a, b) => {
            const aMonth = a.cells[0].textContent.trim();
            const bMonth = b.cells[0].textContent.trim();

            // Parse month names
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

            const aDate = new Date(aMonth.replace(/(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4})/, '$2-$1-01'));
            const bDate = new Date(bMonth.replace(/(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4})/, '$2-$1-01'));

            return direction === 'asc' ? aDate - bDate : bDate - aDate;
        });

        // Clear the table and append sorted rows
        rows.forEach(row => tbody.appendChild(row));

        // Update the color of the rows
        const allRows = tbody.querySelectorAll('tr');
        allRows.forEach((row, index) => {
            row.classList.remove('table-light');
            if (index % 2 === 0) {
                row.classList.add('table-light');
            }
        });
    }

    function exportToExcel() {
        // Get the table element
        const table = document.querySelector('.table');
        if (!table) return;

        // Create a workbook
        let csvContent = "data:text/csv;charset=utf-8,";

        // Add headers
        const headers = [];
        table.querySelectorAll('thead th').forEach(th => {
            headers.push(th.innerText);
        });
        csvContent += headers.join(",") + "\n";

        // Add rows
        table.querySelectorAll('tbody tr').forEach(tr => {
            const row = [];
            tr.querySelectorAll('td').forEach(td => {
                // Clean the content (remove currency symbols and formatting)
                let content = td.innerText.replace('Rp ', '').replace(/\./g, '');
                row.push(content);
            });
            csvContent += row.join(",") + "\n";
        });

        // Add footer
        const footer = [];
        table.querySelectorAll('tfoot td').forEach(td => {
            let content = td.innerText.replace('Rp ', '').replace(/\./g, '');
            footer.push(content);
        });
        csvContent += footer.join(",") + "\n";

        // Create a download link
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "proyeksi_keuangan_" + new Date().toISOString().split('T')[0] + ".csv");
        document.body.appendChild(link);

        // Download the file
        link.click();

        // Clean up
        document.body.removeChild(link);
    }
</script>

<style>
.stats-card {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 24px;
    color: white;
    transition: transform 0.3s ease;
}

.stats-card:hover .stats-icon {
    transform: scale(1.1);
}

.stats-icon.success {
    background-color: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.stats-icon.primary {
    background-color: rgba(13, 110, 253, 0.2);
    color: #0D6EFD;
}

.stats-icon.warning {
    background-color: rgba(255, 193, 7, 0.2);
    color: #FFC107;
}

.stats-info {
    flex: 1;
}

.stats-title {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 5px;
}

.stats-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-subtitle {
    font-size: 12px;
    color: #6c757d;
}

/* Card Header style */
.card-header {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* Action Buttons Style */
.action-buttons .btn {
    margin-left: 8px;
    transition: all 0.3s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
}

.page-title {
    margin-bottom: 20px;
}

/* Table Styles */
.table th, .table td {
    vertical-align: middle;
}

.badge {
    font-size: 12px;
    padding: 5px 7px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.badge:hover {
    transform: scale(1.1);
}

.fw-medium {
    font-weight: 500;
}

/* Chart Legend Styles */
.chart-legend {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 10px;
    animation: fadeIn 0.5s ease-out;
}

.legend-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 5px;
    border-radius: 50%;
    vertical-align: middle;
    transition: transform 0.2s ease;
}

.chart-legend div:hover .legend-indicator {
    transform: scale(1.3);
}

.card-footer {
    padding: 10px 20px;
    border-top: 1px solid #eee;
}

.btn-group-sm .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    transition: all 0.2s ease;
}

.btn-group-sm .btn:hover {
    transform: translateY(-2px);
}

.btn-outline-primary:not(.active) {
    color: #455a64;
    border-color: #d9d9d9;
}

.btn-outline-secondary:not(.active) {
    color: #455a64;
    border-color: #d9d9d9;
}

.btn-outline-info:not(.active) {
    color: #455a64;
    border-color: #d9d9d9;
}

.btn-outline-primary.active,
.btn-outline-secondary.active,
.btn-outline-info.active {
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* Animation for chart */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fadeIn {
    animation: fadeIn 0.4s ease-out forwards;
}

@media print {
    .btn, form, .action-buttons {
        display: none !important;
    }
    .container-fluid {
        padding: 0 !important;
    }
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        margin-bottom: 20px !important;
    }
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/reports/financial-projection.blade.php ENDPATH**/ ?>