<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF <PERSON>ken -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Ubi Bakar Cilembu') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=poppins:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/sass/app.scss', 'resources/js/app.js'])

    <!-- Additional Styles -->
    <style>
        :root {
            --primary-color: #8B4513;  /* Brown */
            --secondary-color: #FF8C00; /* Orange */
            --accent-color: #4CAF50;  /* Green */
            --light-color: #FFF8E1;
            --dark-color: #5D4037;
            --sidebar-width: 280px;
            --header-height: 70px;
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(to bottom, var(--primary-color), var(--dark-color));
            color: white;
            z-index: 1000;
            transition: var(--transition);
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar::-webkit-scrollbar {
            width: 5px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar-header {
            display: flex;
            align-items: center;
            padding: 20px;
            height: var(--header-height);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header .brand-icon {
            font-size: 24px;
            color: var(--secondary-color);
            margin-right: 10px;
        }

        .sidebar-header .brand-text {
            font-weight: 700;
            font-size: 18px;
            white-space: nowrap;
            overflow: hidden;
            transition: var(--transition);
        }

        .sidebar.collapsed .sidebar-header .brand-text {
            opacity: 0;
            width: 0;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-header {
            padding: 10px 20px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: rgba(255, 255, 255, 0.5);
            white-space: nowrap;
            overflow: hidden;
            transition: var(--transition);
        }

        .sidebar.collapsed .menu-header {
            opacity: 0;
            height: 0;
            padding: 0;
            margin: 0;
        }

        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: var(--transition);
            border-left: 3px solid transparent;
            position: relative;
        }

        .menu-item:hover {
            background: rgba(255, 255, 255, 0.05);
            color: white;
            text-decoration: none;
        }

        .menu-item.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-left-color: var(--secondary-color);
        }

        .menu-item i {
            width: 25px;
            text-align: center;
            margin-right: 10px;
            font-size: 18px;
        }

        .menu-item .menu-text {
            white-space: nowrap;
            overflow: hidden;
            transition: var(--transition);
        }

        .sidebar.collapsed .menu-item .menu-text {
            opacity: 0;
            width: 0;
        }

        .menu-item .menu-badge {
            position: absolute;
            right: 20px;
            padding: 2px 6px;
            border-radius: 50px;
            background: var(--secondary-color);
            color: white;
            font-size: 11px;
            font-weight: 600;
            transition: var(--transition);
        }

        .sidebar.collapsed .menu-item .menu-badge {
            opacity: 0;
            right: 5px;
        }

        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: var(--transition);
        }

        .submenu.open {
            max-height: 1000px;
        }

        .submenu-item {
            padding: 10px 20px 10px 55px;
            display: block;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            white-space: nowrap;
            transition: var(--transition);
        }

        .submenu-item:hover,
        .submenu-item.active {
            color: white;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar.collapsed .submenu {
            display: none;
        }

        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .sidebar-footer .user-info {
            display: flex;
            align-items: center;
        }

        .sidebar-footer .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 10px;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .sidebar-footer .user-details {
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            transition: var(--transition);
        }

        .sidebar.collapsed .sidebar-footer .user-details {
            opacity: 0;
            width: 0;
        }

        .sidebar-footer .user-name {
            font-weight: 600;
            font-size: 14px;
        }

        .sidebar-footer .user-role {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        /* Main Content Styles */
        .main-content {
            margin-left: var(--sidebar-width);
            transition: var(--transition);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        .main-content.expanded {
            margin-left: 70px;
        }

        /* Header Styles */
        .navbar {
            height: var(--header-height);
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 1px 5px rgba(0,0,0,0.05);
        }

        .menu-toggle {
            font-size: 20px;
            color: var(--dark-color);
            padding: 0.5rem;
        }

        .max-w-xs {
            max-width: 400px;
        }

        /* Dropdown Styles */
        .dropdown-menu {
            margin-top: 0.5rem;
            border-radius: 0.5rem;
        }

        .dropdown-item {
            font-size: 14px;
        }

        .dropdown-item i {
            width: 20px;
        }

        /* Content Area Styles */
        .content-wrapper {
            padding: 30px;
            flex: 1;
        }

        .page-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 30px;
            display: flex;
            align-items: center;
        }

        .page-title i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            border: none;
            margin-bottom: 30px;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            font-weight: 600;
            padding: 15px 20px;
            border-radius: 10px 10px 0 0 !important;
        }

        .card-body {
            padding: 20px;
        }

        /* Dashboard Cards */
        .stats-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            padding: 20px;
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 24px;
            color: white;
        }

        .stats-icon.primary {
            background: var(--primary-color);
        }

        .stats-icon.success {
            background: var(--accent-color);
        }

        .stats-icon.warning {
            background: var(--secondary-color);
        }

        .stats-icon.danger {
            background: #dc3545;
        }

        .stats-info {
            flex: 1;
        }

        .stats-title {
            font-size: 14px;
            color: #777;
            margin-bottom: 5px;
        }

        .stats-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
        }

        /* Table Styles */
        .table-responsive {
            overflow-x: auto;
        }

        .custom-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .custom-table th {
            background-color: #f8f9fa;
            padding: 12px 15px;
            font-weight: 600;
            color: var(--dark-color);
            border-bottom: 1px solid #dee2e6;
        }

        .custom-table td {
            padding: 12px 15px;
            vertical-align: middle;
            border-bottom: 1px solid #dee2e6;
        }

        .custom-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 5px 10px;
            border-radius: 50px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
        }

        .status-badge.success {
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--accent-color);
        }

        .status-badge.warning {
            background-color: rgba(255, 140, 0, 0.1);
            color: var(--secondary-color);
        }

        .status-badge.danger {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        /* Buttons */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--dark-color);
            border-color: var(--dark-color);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-accent {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
            color: white;
        }

        .btn-accent:hover {
            background-color: #3d9040;
            border-color: #3d9040;
            color: white;
        }

        /* Footer */
        .main-footer {
            padding: 20px 30px;
            text-align: center;
            font-size: 14px;
            color: #777;
            border-top: 1px solid #eee;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.mobile-show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.5);
                z-index: 999;
            }

            .overlay.active {
                display: block;
            }
        }
    </style>

    <!-- Custom Pagination Styles -->
    <link href="{{ asset('css/custom-pagination.css') }}" rel="stylesheet">

    <!-- Global Pagination Override -->
    <style>
        /* Override default pagination to be smaller */
        .pagination {
            --bs-pagination-padding-x: 0.5rem;
            --bs-pagination-padding-y: 0.25rem;
            --bs-pagination-font-size: 0.875rem;
            --bs-pagination-border-radius: 0.25rem;
        }

        .pagination .page-link {
            padding: var(--bs-pagination-padding-y) var(--bs-pagination-padding-x);
            font-size: var(--bs-pagination-font-size);
            line-height: 1.5;
        }

        .pagination .page-link i {
            font-size: 0.75rem;
        }

        /* Ensure consistent small size across all pagination */
        .pagination-sm .page-link {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
    </style>

    @stack('styles')
</head>
<body>
    <div id="app">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <i class="fas fa-fire brand-icon"></i>
                <div class="brand-text">Ubi Bakar Cilembu</div>
            </div>

            <div class="sidebar-menu">
                <div class="menu-header">Menu Utama</div>

                @if(Auth::user() && Auth::user()->isAdmin())
                <a href="{{ route('dashboard') }}" class="menu-item {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="menu-text">Dashboard Admin</span>
                </a>
                @else
                <a href="{{ route('employee.dashboard') }}" class="menu-item {{ request()->routeIs('employee.dashboard') ? 'active' : '' }}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="menu-text">Dashboard Karyawan</span>
                </a>
                @endif

                <a href="{{ route('transactions.index') }}" class="menu-item {{ request()->routeIs('transactions*') ? 'active' : '' }}">
                    <i class="fas fa-cash-register"></i>
                    <span class="menu-text">Transaksi</span>
                    @if(!Auth::user()->isAdmin())
                    <span class="menu-badge">Utama</span>
                    @endif
                </a>

                <div class="menu-header">Inventory</div>

                @if(Auth::user() && Auth::user()->isAdmin())
                <a href="{{ route('raw-inventory.index') }}" class="menu-item {{ request()->routeIs('raw-inventory*') ? 'active' : '' }}">
                    <i class="fas fa-seedling"></i>
                    <span class="menu-text">Ubi Mentah</span>
                </a>
                @endif

                <a href="{{ route('processed-inventory.index') }}" class="menu-item {{ request()->routeIs('processed-inventory*') ? 'active' : '' }}">
                    <i class="fas fa-fire-alt"></i>
                    <span class="menu-text">Ubi Bakar</span>
                </a>

                <a href="{{ route('other-products.index') }}" class="menu-item {{ request()->routeIs('other-products*') ? 'active' : '' }}">
                    <i class="fas fa-box"></i>
                    <span class="menu-text">Produk Lain</span>
                </a>
                
                <a href="{{ route('expiry-recommendations.index') }}" class="menu-item {{ request()->routeIs('expiry-recommendations*') ? 'active' : '' }}">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span class="menu-text">Ubi Hampir Kadaluarsa</span>
                </a>

                @if(Auth::user() && Auth::user()->isAdmin())
                <div class="menu-header">Administrasi</div>

                <a href="{{ route('users.index') }}" class="menu-item {{ request()->routeIs('users*') ? 'active' : '' }}">
                    <i class="fas fa-users"></i>
                    <span class="menu-text">Manajemen User</span>
                </a>

                <a href="{{ route('admin.audit-logs.index') }}" class="menu-item {{ request()->routeIs('admin.audit-logs*') ? 'active' : '' }}">
                    <i class="fas fa-history"></i>
                    <span class="menu-text">Catatan Aktivitas</span>
                </a>

                <div class="menu-header">Laporan</div>

                <a href="{{ route('reports.sales') }}" class="menu-item {{ request()->routeIs('reports.sales') ? 'active' : '' }}">
                    <i class="fas fa-chart-line"></i>
                    <span class="menu-text">Laporan Penjualan</span>
                </a>

                <a href="{{ route('reports.inventory') }}" class="menu-item {{ request()->routeIs('reports.inventory') ? 'active' : '' }}">
                    <i class="fas fa-boxes"></i>
                    <span class="menu-text">Laporan Inventory</span>
                </a>

                <a href="{{ route('reports.financial') }}" class="menu-item {{ request()->routeIs('reports.financial') ? 'active' : '' }}">
                    <i class="fas fa-calculator"></i>
                    <span class="menu-text">Laporan Keuangan</span>
                </a>

                <a href="{{ route('reports.financial-projection') }}" class="menu-item {{ request()->routeIs('reports.financial-projection') ? 'active' : '' }}">
                    <i class="fas fa-chart-area"></i>
                    <span class="menu-text">Proyeksi Keuangan</span>
                </a>
                @endif
            </div>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <div class="user-name">{{ Auth::user()->name ?? 'User' }}</div>
                        <div class="user-role">
                            @if(Auth::user() && Auth::user()->isAdmin())
                                Admin
                            @else
                                Karyawan
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Overlay -->
        <div class="overlay"></div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <nav class="navbar navbar-expand-lg bg-white border-bottom">
                <div class="container-fluid">
                    <button class="menu-toggle btn" type="button">
                        <i class="fas fa-bars"></i>
                    </button>

                    <div class="d-flex justify-content-end flex-grow-1 mx-3">
                        <!-- Kosongkan area ini -->
                    </div>

                    <div class="d-flex align-items-center">
                        <!-- Notification Icon with Dropdown -->
                        <div class="position-relative me-3">
                            <button class="btn position-relative" id="notificationDropdown">
                                <i class="fas fa-bell"></i>
                                <span id="notification-badge" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    3
                                </span>
                            </button>
                            <div class="dropdown-menu dropdown-menu-end notification-dropdown" style="width: 300px; max-height: 400px; overflow-y: auto; position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(-250px, 40px);">
                                <div class="dropdown-header d-flex justify-content-between align-items-center">
                                    <span>Notifikasi</span>
                                    <a href="#" class="text-decoration-none small" id="markAllAsRead">Tandai semua dibaca</a>
                                </div>
                                <div class="dropdown-divider"></div>
                                <div class="notification-items-container">
                                    <!-- Notifikasi stok rendah -->
                                    <div class="notification-item">
                                        <a class="dropdown-item" href="{{ route('inventory.low-stock-alert') }}">
                                            <div class="d-flex">
                                                <div class="flex-shrink-0">
                                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-2">
                                                    <p class="mb-0 fw-bold">Stok Hampir Habis!</p>
                                                    <p class="mb-0 small text-muted">5 produk di bawah batas minimum</p>
                                                    <p class="mb-0 small text-muted">1 jam yang lalu</p>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                    <!-- Notifikasi ubi hampir kadaluarsa -->
                                    <div class="notification-item">
                                        <a class="dropdown-item" href="{{ route('expiry-recommendations.index') }}">
                                            <div class="d-flex">
                                                <div class="flex-shrink-0">
                                                    <i class="fas fa-clock text-danger me-2"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-2">
                                                    <p class="mb-0 fw-bold">Ubi Segera Kadaluarsa!</p>
                                                    <p class="mb-0 small text-muted">3 batch ubi perlu segera dijual</p>
                                                    <p class="mb-0 small text-muted">3 jam yang lalu</p>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                    <!-- Notifikasi penjualan tinggi -->
                                    <div class="notification-item">
                                        <a class="dropdown-item" href="{{ route('dashboard.sales-report') }}">
                                            <div class="d-flex">
                                                <div class="flex-shrink-0">
                                                    <i class="fas fa-chart-line text-success me-2"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-2">
                                                    <p class="mb-0 fw-bold">Penjualan Meningkat!</p>
                                                    <p class="mb-0 small text-muted">Penjualan naik 15% dari minggu lalu</p>
                                                    <p class="mb-0 small text-muted">1 hari yang lalu</p>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                                <div class="dropdown-divider"></div>
                                <div class="text-center p-2">
                                    <a class="small text-muted" href="#">Lihat semua notifikasi</a>
                                </div>
                            </div>
                        </div>

                        <!-- User Dropdown -->
                        <div class="position-relative" id="userDropdownContainer">
                            <button id="userDropdownButton" class="btn d-flex align-items-center">
                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <div class="fw-bold" style="font-size: 14px;">{{ Auth::user()->name ?? 'User' }}</div>
                                    <div class="text-muted" style="font-size: 12px;">
                                        @if(Auth::user() && Auth::user()->isAdmin())
                                            Admin
                                        @else
                                            Karyawan
                                        @endif
                                    </div>
                                </div>
                                <i class="fas fa-chevron-down ms-2"></i>
                            </button>
                            <div id="userDropdownMenu" class="position-absolute end-0 mt-2 bg-white rounded shadow py-2" style="min-width: 200px; z-index: 1000; display: none;">
                                <a href="{{ route('logout') }}" class="d-block px-4 py-2 text-decoration-none text-danger"
                                   onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                    <i class="fas fa-sign-out-alt me-2"></i> {{ __('Logout') }}
                                </a>
                                <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                    @csrf
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Content Area -->
            <div class="content-wrapper">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif



                @yield('content')
            </div>

            <!-- Footer -->
            <div class="main-footer">
                <p>© {{ date('Y') }} Ubi Bakar Cilembu - Sistem Manajemen Inventory & Transaksi</p>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap Bundle with Popper -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
    
    <!-- App Scripts -->
    <script src="{{ asset('js/app.js') }}"></script>
    <script src="{{ asset('js/notifications.js') }}"></script>

    <!-- Simple Auto-refresh Prevention -->
    <script>
        // Simple prevention for auto-refresh on specific pages
        const currentPath = window.location.pathname;
        const noAutoRefreshPaths = [
            '/expiry-recommendations',
            '/dashboard',
            '/pos',
            '/distributions'
        ];

        if (noAutoRefreshPaths.some(path => currentPath.includes(path))) {
            console.log('Auto-refresh prevention active for:', currentPath);
        }
    </script>

    @stack('scripts')

    <script>
        // Manual Dropdown implementation - no dependencies
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM fully loaded');

            var userDropdownButton = document.getElementById('userDropdownButton');
            var userDropdownMenu = document.getElementById('userDropdownMenu');
            var userDropdownContainer = document.getElementById('userDropdownContainer');

            console.log('Button:', userDropdownButton);
            console.log('Menu:', userDropdownMenu);

            if (userDropdownButton && userDropdownMenu) {
                userDropdownButton.addEventListener('click', function(e) {
                    console.log('Button clicked');
                    e.preventDefault();
                    e.stopPropagation();

                    if (userDropdownMenu.style.display === 'block') {
                        userDropdownMenu.style.display = 'none';
                    } else {
                        userDropdownMenu.style.display = 'block';
                    }
                });

                // Close when clicking outside
                document.addEventListener('click', function(e) {
                    if (!userDropdownContainer.contains(e.target)) {
                        userDropdownMenu.style.display = 'none';
                    }
                });
            }

            // Sidebar toggle
            const menuToggle = document.querySelector('.menu-toggle');
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');
            const overlay = document.querySelector('.overlay');

            if (menuToggle) {
                menuToggle.addEventListener('click', function() {
                    if (window.innerWidth > 768) {
                        sidebar.classList.toggle('collapsed');
                        mainContent.classList.toggle('expanded');
                    } else {
                        sidebar.classList.toggle('mobile-show');
                        overlay.classList.toggle('active');
                    }
                });
            }

            if (overlay) {
                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('mobile-show');
                    overlay.classList.remove('active');
                });
            }

            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    sidebar.classList.remove('mobile-show');
                    overlay.classList.remove('active');
                }
            });
        });
    </script>

    @stack('scripts')
</body>
</html>
