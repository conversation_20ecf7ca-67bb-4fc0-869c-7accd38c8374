<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use Carbon\Carbon;

class ResetUserManagement extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:reset {--fresh : Truncate users table before seeding}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset and configure user management for new database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Resetting User Management...');

        try {
            // Check if users table exists
            if (!DB::getSchemaBuilder()->hasTable('users')) {
                $this->error('❌ Users table does not exist!');
                $this->info('Please run: php artisan migrate');
                return 1;
            }

            // Truncate if fresh option is provided
            if ($this->option('fresh')) {
                $this->info('🗑️ Truncating users table...');
                User::truncate();
            }

            // Check current users count
            $currentUsersCount = User::count();
            $this->info("📊 Current users count: {$currentUsersCount}");

            // Create default admin user
            $admin = User::updateOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'Administrator',
                    'role' => User::ROLE_ADMIN,
                    'password' => Hash::make('admin123'),
                    'email_verified_at' => Carbon::now(),
                    'last_activity' => Carbon::now(),
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]
            );

            // Create default employee user
            $employee = User::updateOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'Karyawan Toko',
                    'role' => User::ROLE_EMPLOYEE,
                    'password' => Hash::make('karyawan123'),
                    'email_verified_at' => Carbon::now(),
                    'last_activity' => Carbon::now(),
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]
            );

            $this->info('✅ Default users created/updated:');
            $this->table(
                ['Name', 'Email', 'Role', 'Password'],
                [
                    [$admin->name, $admin->email, $admin->role, 'admin123'],
                    [$employee->name, $employee->email, $employee->role, 'karyawan123'],
                ]
            );

            // Verify role enum values
            $this->info('🔍 Checking role enum values...');
            $roleInfo = DB::select("SHOW COLUMNS FROM users WHERE Field = 'role'");
            if (count($roleInfo) > 0) {
                $roleType = $roleInfo[0]->Type;
                $this->info("Role column type: {$roleType}");
                
                if (strpos($roleType, 'enum') !== false) {
                    preg_match("/^enum\(\'(.*)\'\)$/", $roleType, $matches);
                    $validRoles = explode("','", $matches[1]);
                    $this->info('Valid roles: ' . implode(', ', $validRoles));
                }
            }

            // Test authentication
            $this->info('🧪 Testing user authentication...');
            $testAdmin = User::where('email', '<EMAIL>')->first();
            $testEmployee = User::where('email', '<EMAIL>')->first();

            if ($testAdmin && $testAdmin->isAdmin()) {
                $this->info('✅ Admin user test passed');
            } else {
                $this->error('❌ Admin user test failed');
            }

            if ($testEmployee && $testEmployee->isEmployee()) {
                $this->info('✅ Employee user test passed');
            } else {
                $this->error('❌ Employee user test failed');
            }

            $this->info('🎉 User management reset completed!');
            $this->info('');
            $this->info('Login credentials:');
            $this->info('Admin: <EMAIL> / admin123');
            $this->info('Employee: <EMAIL> / karyawan123');

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Error: ' . $e->getMessage());
            return 1;
        }
    }
}
