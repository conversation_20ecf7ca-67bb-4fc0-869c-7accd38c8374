<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-truck"></i> Buat Distribusi untuk Produk Kadaluarsa</h1>
        <a href="<?php echo e(route('expiry-recommendations.index')); ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>Form Distribusi</h5>
                </div>
                <div class="card-body">
                    <!-- Form distribusi yang sudah diperbaiki -->
                    <div class="alert alert-success">
                        <strong>✅ Form Siap:</strong> Distribusi akan disimpan ke database
                    </div>

                    <form id="distributionForm" action="<?php echo e(route('save.distribution.data')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="processed_inventory_id" value="<?php echo e($item->id); ?>">
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="market_name" class="form-label">Nama Pasar Tujuan</label>
                                <select class="form-select <?php $__errorArgs = ['market_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        id="market_name" name="market_name" required>
                                    <option value="">Pilih Pasar</option>
                                    <?php $__currentLoopData = $markets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $market): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($market); ?>" <?php echo e(old('market_name') == $market ? 'selected' : ''); ?>>
                                            <?php echo e($market); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <option value="Pasar Baru Bandung">Pasar Baru Bandung</option>
                                    <option value="Pasar Cicadas">Pasar Cicadas</option>
                                    <option value="Pasar Kosambi">Pasar Kosambi</option>
                                    <option value="Pasar Caringin">Pasar Caringin</option>
                                </select>
                                <?php $__errorArgs = ['market_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="quantity" class="form-label">Jumlah Distribusi</label>
                                <input type="number" class="form-control <?php $__errorArgs = ['quantity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="quantity" name="quantity" value="<?php echo e(old('quantity', $item->current_stock)); ?>" 
                                       min="1" max="<?php echo e($item->current_stock); ?>" required>
                                <small class="text-muted">Maksimal: <?php echo e($item->current_stock); ?> unit</small>
                                <?php $__errorArgs = ['quantity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="distribution_date" class="form-label">Tanggal Distribusi</label>
                                <input type="date" class="form-control <?php $__errorArgs = ['distribution_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="distribution_date" name="distribution_date" 
                                       value="<?php echo e(old('distribution_date', date('Y-m-d'))); ?>" required>
                                <?php $__errorArgs = ['distribution_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">Catatan</label>
                            <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="notes" name="notes" rows="3"><?php echo e(old('notes', 'Distribusi urgent untuk produk yang akan kadaluarsa')); ?></textarea>
                            <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?php echo e(route('expiry-recommendations.index')); ?>" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Buat Distribusi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Detail Produk</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <?php if($item->image): ?>
                            <img src="<?php echo e(asset('storage/products/' . $item->image)); ?>" 
                                 alt="<?php echo e($item->name); ?>" class="img-fluid rounded" style="max-height: 150px;">
                        <?php else: ?>
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                 style="height: 150px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <table class="table table-sm">
                        <tr>
                            <td><strong>Nama Produk:</strong></td>
                            <td><?php echo e($item->name); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Batch:</strong></td>
                            <td><?php echo e($item->batch_number); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Stok Tersedia:</strong></td>
                            <td><?php echo e($item->current_stock); ?> unit</td>
                        </tr>
                        <tr>
                            <td><strong>Tanggal Kadaluarsa:</strong></td>
                            <td><?php echo e($item->expiry_date ? $item->expiry_date->format('d M Y') : '-'); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Harga Jual:</strong></td>
                            <td>Rp <?php echo e(number_format($item->selling_price, 0, ',', '.')); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Total Nilai:</strong></td>
                            <td>Rp <?php echo e(number_format($item->current_stock * $item->selling_price, 0, ',', '.')); ?></td>
                        </tr>
                    </table>
                    
                    <?php if($item->expiry_date && $item->expiry_date->isPast()): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Sudah Kadaluarsa!</strong>
                        </div>
                    <?php elseif($item->expiry_date && $item->expiry_date->diffInDays() <= 3): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-clock"></i>
                            <strong>Akan kadaluarsa dalam <?php echo e($item->expiry_date->diffInDays()); ?> hari</strong>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle custom market name
    const marketSelect = document.getElementById('market_name');
    
    marketSelect.addEventListener('change', function() {
        if (this.value === 'other') {
            const customMarket = prompt('Masukkan nama pasar:');
            if (customMarket) {
                const option = new Option(customMarket, customMarket, true, true);
                this.appendChild(option);
            } else {
                this.value = '';
            }
        }
    });
    
    // Calculate total value when quantity changes
    const quantityInput = document.getElementById('quantity');
    const unitPrice = <?php echo e($item->selling_price); ?>;
    
    quantityInput.addEventListener('input', function() {
        const quantity = parseInt(this.value) || 0;
        const totalValue = quantity * unitPrice;
        
        // Update display if needed
        console.log('Total value:', totalValue);
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/inventory/create-distribution.blade.php ENDPATH**/ ?>