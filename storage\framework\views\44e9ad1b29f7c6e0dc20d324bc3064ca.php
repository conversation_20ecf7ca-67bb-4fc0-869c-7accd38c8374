<?php $__env->startSection('title', '<PERSON>por<PERSON>'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line me-2"></i><PERSON><PERSON><PERSON>
        </h1>
        <div>
            <a href="<?php echo e(route('reports.export', ['type' => 'financial', 'start_date' => $startDate, 'end_date' => $endDate])); ?>" 
               class="btn btn-success">
                <i class="fas fa-file-excel me-1"></i>Export Excel
            </a>
        </div>
    </div>

    <!-- Filter Form -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Filter Periode</h5>
        </div>
        <div class="card-body">
            <form action="<?php echo e(route('reports.financial')); ?>" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="period" class="form-label">Periode</label>
                    <select class="form-select" id="period" name="period">
                        <option value="today" <?php echo e($period == 'today' ? 'selected' : ''); ?>>Hari Ini</option>
                        <option value="yesterday" <?php echo e($period == 'yesterday' ? 'selected' : ''); ?>>Kemarin</option>
                        <option value="this_week" <?php echo e($period == 'this_week' ? 'selected' : ''); ?>>Minggu Ini</option>
                        <option value="last_week" <?php echo e($period == 'last_week' ? 'selected' : ''); ?>>Minggu Lalu</option>
                        <option value="this_month" <?php echo e($period == 'this_month' ? 'selected' : ''); ?>>Bulan Ini</option>
                        <option value="last_month" <?php echo e($period == 'last_month' ? 'selected' : ''); ?>>Bulan Lalu</option>
                        <option value="custom" <?php echo e($period == 'custom' ? 'selected' : ''); ?>>Custom</option>
                    </select>
                </div>
                <div class="col-md-3 custom-date" style="display: <?php echo e($period == 'custom' ? 'block' : 'none'); ?>">
                    <label for="start_date" class="form-label">Tanggal Mulai</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo e($startDate); ?>">
                </div>
                <div class="col-md-3 custom-date" style="display: <?php echo e($period == 'custom' ? 'block' : 'none'); ?>">
                    <label for="end_date" class="form-label">Tanggal Akhir</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo e($endDate); ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-4 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Pendapatan
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp <?php echo e(number_format($summary['total_revenue'], 0, ',', '.')); ?>

                            </div>
                            <div class="text-xs text-muted">
                                <?php echo e($reportTitle); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Total Biaya
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp <?php echo e(number_format($summary['total_expense'], 0, ',', '.')); ?>

                            </div>
                            <div class="text-xs text-muted">
                                COGS + Operasional
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Laba Bersih
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp <?php echo e(number_format($summary['net_profit'], 0, ',', '.')); ?>

                            </div>
                            <div class="text-xs text-<?php echo e($summary['profit_change'] > 0 ? 'success' : 'danger'); ?>">
                                <i class="fas fa-<?php echo e($summary['profit_change'] > 0 ? 'arrow-up' : 'arrow-down'); ?>"></i>
                                <?php echo e(number_format(abs($summary['profit_change']), 1)); ?>% dari periode sebelumnya
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Revenue vs Expense Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Pendapatan vs Biaya</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="revenueExpenseChart" height="320"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cost Distribution Chart -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Distribusi Biaya</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="costDistributionChart" height="320"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue by Product Chart -->
    <div class="row">
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Pendapatan Berdasarkan Produk</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="revenueProductChart" height="320"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Analysis -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Analisis Kinerja</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-borderless">
                            <tbody>
                                <tr>
                                    <th>Margin Laba Kotor</th>
                                    <td class="text-end"><?php echo e(number_format($details['gross_margin'], 1)); ?>%</td>
                                    <td width="100">
                                        <div class="progress">
                                            <div class="progress-bar bg-success" style="width: <?php echo e(min($details['gross_margin'], 100)); ?>%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Margin Laba Bersih</th>
                                    <td class="text-end"><?php echo e(number_format($details['net_profit_margin'], 1)); ?>%</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar bg-primary" style="width: <?php echo e(min($details['net_profit_margin'], 100)); ?>%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Biaya Operasional</th>
                                    <td class="text-end"><?php echo e(number_format($details['operating_expense_ratio'], 1)); ?>%</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar bg-warning" style="width: <?php echo e(min($details['operating_expense_ratio'], 100)); ?>%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>ROI</th>
                                    <td class="text-end"><?php echo e(number_format($details['roi'], 1)); ?>%</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar bg-info" style="width: <?php echo e(min(abs($details['roi']), 100)); ?>%"></div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// 🎯 ULTRA SIMPLE FINANCIAL CHARTS
console.log('🚀 Loading Financial Charts...');

document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for everything to load
    setTimeout(function() {
        console.log('📊 Initializing charts...');

        if (typeof Chart === 'undefined') {
            console.error('❌ Chart.js not loaded');
            return;
        }

        console.log('✅ Chart.js loaded');

        // Create charts
        createSimpleCharts();

    }, 1000);
});

function createSimpleCharts() {
    console.log('🎯 Creating simple charts...');

    // Chart 1: Revenue vs Expense
    createChart1();

    // Chart 2: Cost Distribution
    createChart2();

    // Chart 3: Revenue by Product
    createChart3();
}

function createChart1() {
    console.log('📈 Creating Chart 1...');

    const canvas = document.getElementById('revenueExpenseChart');
    if (!canvas) {
        console.error('❌ Canvas 1 not found');
        return;
    }

    try {
        new Chart(canvas, {
            type: 'bar',
            data: {
                labels: ['Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab', 'Min'],
                datasets: [{
                    label: 'Pendapatan',
                    data: [500000, 750000, 600000, 800000, 900000, 1200000, 850000],
                    backgroundColor: '#28a745',
                    borderColor: '#28a745',
                    borderWidth: 1
                }, {
                    label: 'Biaya',
                    data: [300000, 450000, 360000, 480000, 540000, 720000, 510000],
                    backgroundColor: '#dc3545',
                    borderColor: '#dc3545',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        console.log('✅ Chart 1 created');

    } catch (error) {
        console.error('❌ Error creating chart 1:', error);
        canvas.parentElement.innerHTML = '<div class="alert alert-danger">Error loading chart 1</div>';
    }
}

function createChart2() {
    console.log('🍩 Creating Chart 2...');

    const canvas = document.getElementById('costDistributionChart');
    if (!canvas) {
        console.error('❌ Canvas 2 not found');
        return;
    }

    try {
        new Chart(canvas, {
            type: 'doughnut',
            data: {
                labels: ['Bahan Baku', 'Gaji', 'Sewa', 'Utilitas', 'Lainnya'],
                datasets: [{
                    data: [45, 25, 15, 10, 5],
                    backgroundColor: [
                        '#ff6384',
                        '#36a2eb',
                        '#ffce56',
                        '#4bc0c0',
                        '#9966ff'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        console.log('✅ Chart 2 created');

    } catch (error) {
        console.error('❌ Error creating chart 2:', error);
        canvas.parentElement.innerHTML = '<div class="alert alert-danger">Error loading chart 2</div>';
    }
}

function createChart3() {
    console.log('🥧 Creating Chart 3...');

    const canvas = document.getElementById('revenueProductChart');
    if (!canvas) {
        console.error('❌ Canvas 3 not found');
        return;
    }

    try {
        new Chart(canvas, {
            type: 'pie',
            data: {
                labels: ['Ubi Original', 'Ubi Keju', 'Ubi Coklat', 'Minuman'],
                datasets: [{
                    data: [40, 30, 20, 10],
                    backgroundColor: [
                        '#8b4513',
                        '#ff8c00',
                        '#d2b48c',
                        '#4682b4'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        console.log('✅ Chart 3 created');

    } catch (error) {
        console.error('❌ Error creating chart 3:', error);
        canvas.parentElement.innerHTML = '<div class="alert alert-danger">Error loading chart 3</div>';
    }
}

// Period selector
const periodSelect = document.getElementById('period');
const customDateFields = document.querySelectorAll('.custom-date');

if (periodSelect) {
    periodSelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            customDateFields.forEach(field => field.style.display = 'block');
        } else {
            customDateFields.forEach(field => field.style.display = 'none');
        }
    });
}

console.log('🎉 All charts loaded successfully!');
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/reports/financial.blade.php ENDPATH**/ ?>