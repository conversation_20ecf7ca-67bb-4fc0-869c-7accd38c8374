<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-calculator"></i>
        <span><PERSON><PERSON><PERSON></span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <span>Filter Laporan</span>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('reports.financial')); ?>" method="GET" class="row g-3">
                        <!-- Filter Type Selection -->
                        <div class="col-md-3">
                            <label for="filter_type" class="form-label">Jenis Filter</label>
                            <select class="form-select" id="filter_type" name="filter_type" onchange="toggleFilterInputs()">
                                <option value="monthly" <?php echo e(($filterType ?? 'monthly') == 'monthly' ? 'selected' : ''); ?>>Per Bulan</option>
                                <option value="custom" <?php echo e(($filterType ?? 'monthly') == 'custom' ? 'selected' : ''); ?>>Rentang Tanggal</option>
                            </select>
                        </div>

                        <!-- Monthly Filter -->
                        <div class="col-md-3" id="monthly_filter" style="<?php echo e(($filterType ?? 'monthly') == 'monthly' ? '' : 'display: none;'); ?>">
                            <label for="selected_month" class="form-label">Pilih Bulan</label>
                            <input type="month" class="form-control" id="selected_month" name="selected_month"
                                   value="<?php echo e($selectedMonth ?? date('Y-m')); ?>">
                        </div>

                        <!-- Custom Date Range Filter -->
                        <div class="col-md-3" id="custom_filter_start" style="<?php echo e(($filterType ?? 'monthly') == 'custom' ? '' : 'display: none;'); ?>">
                            <label for="start_date" class="form-label">Tanggal Awal</label>
                            <input type="date" class="form-control" id="start_date" name="start_date"
                                   value="<?php echo e($startDateFormatted ?? ''); ?>">
                        </div>

                        <div class="col-md-3" id="custom_filter_end" style="<?php echo e(($filterType ?? 'monthly') == 'custom' ? '' : 'display: none;'); ?>">
                            <label for="end_date" class="form-label">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="end_date" name="end_date"
                                   value="<?php echo e($endDateFormatted ?? ''); ?>">
                        </div>

                        <!-- Submit Button -->
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid gap-2 d-md-flex">
                                <button type="submit" class="btn btn-primary flex-fill">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="<?php echo e(route('reports.financial')); ?>" class="btn btn-outline-secondary flex-fill">
                                    <i class="fas fa-refresh"></i> Reset
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Ringkasan Keuangan (<?php echo e($reportTitle); ?>)</span>
                    <a href="<?php echo e(route('reports.export', array_merge(['type' => 'financial'], request()->all()))); ?>" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> Export Excel
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="stats-card">
                                <div class="stats-icon success">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="stats-info">
                                    <div class="stats-title">Total Pendapatan</div>
                                    <h3 class="stats-value">Rp <?php echo e(number_format($summary['total_revenue'], 0, ',', '.')); ?></h3>
                                    <div class="stats-change <?php echo e($summary['revenue_change'] > 0 ? 'text-success' : 'text-danger'); ?>">
                                        <i class="fas fa-<?php echo e($summary['revenue_change'] > 0 ? 'arrow-up' : 'arrow-down'); ?>"></i>
                                        <?php echo e(number_format(abs($summary['revenue_change']), 1)); ?>% dari periode sebelumnya
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="stats-card">
                                <div class="stats-icon danger">
                                    <i class="fas fa-wallet"></i>
                                </div>
                                <div class="stats-info">
                                    <div class="stats-title">Total Biaya</div>
                                    <h3 class="stats-value">Rp <?php echo e(number_format($summary['total_cost'], 0, ',', '.')); ?></h3>
                                    <div class="stats-subtitle"><?php echo e($summary['total_revenue'] > 0 ? number_format(($summary['total_cost'] / $summary['total_revenue']) * 100, 1) : 0); ?>% dari pendapatan</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="stats-card">
                                <div class="stats-icon primary">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="stats-info">
                                    <div class="stats-title">Laba Bersih</div>
                                    <h3 class="stats-value">Rp <?php echo e(number_format($summary['net_profit'], 0, ',', '.')); ?></h3>
                                    <div class="stats-change <?php echo e($summary['profit_change'] > 0 ? 'text-success' : 'text-danger'); ?>">
                                        <i class="fas fa-<?php echo e($summary['profit_change'] > 0 ? 'arrow-up' : 'arrow-down'); ?>"></i>
                                        <?php echo e(number_format(abs($summary['profit_change']), 1)); ?>% dari periode sebelumnya
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Kartu Analisis Keuangan -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    <span>Analisis Kinerja</span>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <tbody>
                                                <tr>
                                                    <th>Margin Laba Kotor</th>
                                                    <td class="text-end"><?php echo e(number_format($details['gross_margin'], 1)); ?>%</td>
                                                    <td width="100">
                                                        <div class="progress">
                                                            <div class="progress-bar bg-success" style="width: <?php echo e(min($details['gross_margin'], 100)); ?>%"></div>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Margin Laba Bersih</th>
                                                    <td class="text-end"><?php echo e(number_format($details['net_profit_margin'], 1)); ?>%</td>
                                                    <td>
                                                        <div class="progress">
                                                            <div class="progress-bar bg-primary" style="width: <?php echo e(min($details['net_profit_margin'], 100)); ?>%"></div>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Biaya Operasional</th>
                                                    <td class="text-end"><?php echo e(number_format(($details['total_operating_expense'] / $summary['total_revenue']) * 100, 1)); ?>%</td>
                                                    <td>
                                                        <div class="progress">
                                                            <div class="progress-bar bg-warning" style="width: <?php echo e(min(($details['total_operating_expense'] / $summary['total_revenue']) * 100, 100)); ?>%"></div>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>HPP</th>
                                                    <td class="text-end"><?php echo e(number_format((($details['raw_material_cost'] + $details['production_cost']) / $summary['total_revenue']) * 100, 1)); ?>%</td>
                                                    <td>
                                                        <div class="progress">
                                                            <div class="progress-bar bg-danger" style="width: <?php echo e(min((($details['raw_material_cost'] + $details['production_cost']) / $summary['total_revenue']) * 100, 100)); ?>%"></div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <h6>Perbandingan dengan Periode Sebelumnya</h6>
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>Metrik</th>
                                                    <th class="text-end">Saat Ini</th>
                                                    <th class="text-end">Sebelumnya</th>
                                                    <th class="text-end">Perubahan</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>Pendapatan</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($summary['total_revenue'], 0, ',', '.')); ?></td>
                                                    <td class="text-end">Rp <?php echo e(number_format($previousRevenue, 0, ',', '.')); ?></td>
                                                    <td class="text-end <?php echo e($summary['revenue_change'] > 0 ? 'text-success' : 'text-danger'); ?>">
                                                        <?php echo e($summary['revenue_change'] > 0 ? '+' : ''); ?><?php echo e(number_format($summary['revenue_change'], 1)); ?>%
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Laba Bersih</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($summary['net_profit'], 0, ',', '.')); ?></td>
                                                    <td class="text-end">Rp <?php echo e(number_format($previousNetProfit, 0, ',', '.')); ?></td>
                                                    <td class="text-end <?php echo e($summary['profit_change'] > 0 ? 'text-success' : 'text-danger'); ?>">
                                                        <?php echo e($summary['profit_change'] > 0 ? '+' : ''); ?><?php echo e(number_format($summary['profit_change'], 1)); ?>%
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    <span>Pendapatan Berdasarkan Produk</span>
                                </div>
                                <div class="card-body">
                                    <canvas id="revenueProductChart" height="250"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card h-100">
                                <div class="card-header">
                                    <span>Grafik Pendapatan vs Biaya</span>
                                </div>
                                <div class="card-body">
                                    <canvas id="revenueExpenseChart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header">
                                    <span>Distribusi Biaya</span>
                                </div>
                                <div class="card-body">
                                    <canvas id="costDistributionChart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <span>Laporan Laba Rugi</span>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <tbody>
                                                <tr class="bg-light">
                                                    <th colspan="2">Pendapatan</th>
                                                </tr>
                                                <tr>
                                                    <td style="padding-left: 30px;">Penjualan Produk</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['sales_revenue'], 0, ',', '.')); ?></td>
                                                </tr>
                                                <tr>
                                                    <td style="padding-left: 30px;">Pendapatan Lain-lain</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['other_revenue'], 0, ',', '.')); ?></td>
                                                </tr>
                                                <tr class="fw-bold">
                                                    <td>Total Pendapatan</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($summary['total_revenue'], 0, ',', '.')); ?></td>
                                                </tr>
                                                
                                                <tr class="bg-light">
                                                    <th colspan="2">Harga Pokok Penjualan</th>
                                                </tr>
                                                <tr>
                                                    <td style="padding-left: 30px;">Biaya Bahan Baku</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['raw_material_cost'], 0, ',', '.')); ?></td>
                                                </tr>
                                                <tr>
                                                    <td style="padding-left: 30px;">Biaya Produksi</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['production_cost'], 0, ',', '.')); ?></td>
                                                </tr>
                                                <tr class="fw-bold">
                                                    <td>Total HPP</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['raw_material_cost'] + $details['production_cost'], 0, ',', '.')); ?></td>
                                                </tr>
                                                
                                                <tr class="fw-bold bg-light">
                                                    <td>Laba Kotor</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['gross_profit'], 0, ',', '.')); ?></td>
                                                </tr>
                                                
                                                <tr class="bg-light">
                                                    <th colspan="2">Biaya Operasional</th>
                                                </tr>
                                                <tr>
                                                    <td style="padding-left: 30px;">Biaya Gaji</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['salary_expense'], 0, ',', '.')); ?></td>
                                                </tr>
                                                <tr>
                                                    <td style="padding-left: 30px;">Biaya Sewa</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['rent_expense'], 0, ',', '.')); ?></td>
                                                </tr>
                                                <tr>
                                                    <td style="padding-left: 30px;">Biaya Utilitas</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['utility_expense'], 0, ',', '.')); ?></td>
                                                </tr>
                                                <tr>
                                                    <td style="padding-left: 30px;">Biaya Pemasaran</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['marketing_expense'], 0, ',', '.')); ?></td>
                                                </tr>
                                                <tr>
                                                    <td style="padding-left: 30px;">Biaya Lain-lain</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['other_expense'], 0, ',', '.')); ?></td>
                                                </tr>
                                                <tr class="fw-bold">
                                                    <td>Total Biaya Operasional</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['total_operating_expense'], 0, ',', '.')); ?></td>
                                                </tr>
                                                
                                                <tr class="fw-bold bg-light">
                                                    <td>Laba Operasional</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['operating_profit'], 0, ',', '.')); ?></td>
                                                </tr>
                                                
                                                <?php if($details['interest_expense'] > 0): ?>
                                                <tr>
                                                    <td style="padding-left: 30px;">Biaya Bunga</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['interest_expense'], 0, ',', '.')); ?></td>
                                                </tr>
                                                <tr class="fw-bold">
                                                    <td>Laba Sebelum Pajak</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['profit_before_tax'], 0, ',', '.')); ?></td>
                                                </tr>
                                                <?php endif; ?>
                                                
                                                <tr class="fw-bold">
                                                    <td>Pajak (<?php echo e($details['tax_rate']); ?>%)</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['tax'], 0, ',', '.')); ?></td>
                                                </tr>
                                                
                                                <tr class="fw-bold fs-5 bg-light">
                                                    <td>Laba Bersih</td>
                                                    <td class="text-end">Rp <?php echo e(number_format($details['net_profit'], 0, ',', '.')); ?></td>
                                                </tr>
                                                
                                                <tr>
                                                    <td>Margin Laba Bersih</td>
                                                    <td class="text-end"><?php echo e(number_format($details['net_profit_margin'], 2)); ?>%</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not loaded!');
            document.getElementById('revenueExpenseChart').parentElement.innerHTML = '<p class="text-center text-danger p-4">Chart.js library tidak ter-load. Silakan refresh halaman.</p>';
            document.getElementById('costDistributionChart').parentElement.innerHTML = '<p class="text-center text-danger p-4">Chart.js library tidak ter-load. Silakan refresh halaman.</p>';
            document.getElementById('revenueProductChart').parentElement.innerHTML = '<p class="text-center text-danger p-4">Chart.js library tidak ter-load. Silakan refresh halaman.</p>';
            return;
        }

        console.log('Chart.js loaded successfully for financial report, version:', Chart.version);

        // Reset and initialize chart management
        if (window.financialCharts) {
            // Destroy all existing charts
            Object.keys(window.financialCharts).forEach(key => {
                if (window.financialCharts[key] && typeof window.financialCharts[key].destroy === 'function') {
                    window.financialCharts[key].destroy();
                }
            });
        }

        // Fresh start for chart instances
        window.financialCharts = {};

        console.log('Starting fresh financial chart creation...');

        // Period selection toggling
        const periodSelect = document.getElementById('period');
        const customDateFields = document.querySelectorAll('.custom-date');

        if (periodSelect) {
            periodSelect.addEventListener('change', function() {
                if (this.value === 'custom') {
                    customDateFields.forEach(field => {
                        field.style.display = '';
                    });
                } else {
                    customDateFields.forEach(field => {
                        field.style.display = 'none';
                    });
                }
            });
        }

        // Revenue vs Expense Chart - completely rebuilt
        setTimeout(function() {
            try {
                console.log('Creating Revenue vs Expense Chart...');

                // Get chart data with proper fallback
                const revExpChartData = <?php echo json_encode($revExpChartData ?? ['labels' => [], 'revenue' => [], 'expense' => []) ?>;
                console.log('Revenue Expense Chart Data:', revExpChartData);

                const canvas = document.getElementById('revenueExpenseChart');
                if (!canvas) {
                    console.error('Revenue expense chart canvas not found');
                    return;
                }

                // Check if we have valid data
                const hasData = revExpChartData &&
                               revExpChartData.labels &&
                               Array.isArray(revExpChartData.labels) &&
                               revExpChartData.labels.length > 0;

                if (hasData) {
                    window.financialCharts.revExpChart = new Chart(canvas, {
                    type: 'bar',
                    data: {
                        labels: revExpChartData.labels,
                        datasets: [{
                            label: 'Pendapatan',
                            data: revExpChartData.revenue || [],
                            backgroundColor: 'rgba(76, 175, 80, 0.7)',
                            borderColor: 'rgba(76, 175, 80, 1)',
                            borderWidth: 1
                        }, {
                            label: 'Biaya',
                            data: revExpChartData.expense || [],
                            backgroundColor: 'rgba(220, 53, 69, 0.7)',
                            borderColor: 'rgba(220, 53, 69, 1)',
                            borderWidth: 1
                        }, {
                            label: 'Laba',
                            data: revExpChartData.profit || [],
                            backgroundColor: 'rgba(139, 69, 19, 0.7)',
                            borderColor: 'rgba(139, 69, 19, 1)',
                            borderWidth: 1,
                            type: 'line'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return 'Rp ' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
                                    }
                                }
                            }
                        },
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.dataset.label + ': Rp ' + context.parsed.y.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
                                    }
                                }
                            }
                        }
                    }
                });
                    console.log('Revenue expense chart created successfully');
                } else {
                    canvas.parentElement.innerHTML = '<p class="text-center text-muted p-4">Tidak ada data keuangan untuk periode ini</p>';
                }
            } catch (error) {
                console.error('Error creating revenue expense chart:', error);
                document.getElementById('revenueExpenseChart').parentElement.innerHTML = '<p class="text-center text-danger p-4">Error loading revenue expense chart: ' + error.message + '</p>';
            }
        }, 100);

        // Cost Distribution Chart - with delay
        setTimeout(function() {
            try {
                // Destroy existing chart if it exists
                if (window.financialCharts.costChart) {
                    window.financialCharts.costChart.destroy();
                }

                const costChartData = <?php echo json_encode($costDistributionData ?? null, 15, 512) ?> || {labels: [], data: []};
                console.log('Cost Distribution Chart Data:', costChartData);

                const canvas = document.getElementById('costDistributionChart');
                if (!canvas) {
                    console.error('Cost distribution chart canvas not found');
                    return;
                }

                if (costChartData && costChartData.labels && costChartData.data && costChartData.labels.length > 0) {
                    window.financialCharts.costChart = new Chart(canvas, {
                    type: 'doughnut',
                    data: {
                        labels: costChartData.labels,
                        datasets: [{
                            data: costChartData.data,
                            backgroundColor: [
                                'rgba(139, 69, 19, 0.7)',
                                'rgba(255, 140, 0, 0.7)',
                                'rgba(220, 53, 69, 0.7)',
                                'rgba(76, 175, 80, 0.7)',
                                'rgba(33, 150, 243, 0.7)',
                                'rgba(156, 39, 176, 0.7)',
                                'rgba(255, 193, 7, 0.7)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const value = context.parsed;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = Math.round((value / total) * 100);
                                        return context.label + ': ' + percentage + '% (Rp ' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".") + ')';
                                    }
                                }
                            }
                        }
                    }
                });
                    console.log('Cost distribution chart created successfully');
                } else {
                    canvas.parentElement.innerHTML = '<p class="text-center text-muted p-4">Tidak ada data distribusi biaya untuk periode ini</p>';
                }
            } catch (error) {
                console.error('Error creating cost distribution chart:', error);
                document.getElementById('costDistributionChart').parentElement.innerHTML = '<p class="text-center text-danger p-4">Error loading cost distribution chart: ' + error.message + '</p>';
            }
        }, 200);

        // Revenue by Product Chart - completely rebuilt to prevent overload
        setTimeout(function() {
            try {
                console.log('Creating Revenue by Product Chart...');

                // Get chart data with proper fallback
                const revenueProductData = <?php echo json_encode($revenueDistributionData ?? ['labels' => [], 'data' => []], 512) ?>;
                console.log('Revenue Product Chart Data:', revenueProductData);

                const canvas = document.getElementById('revenueProductChart');
                if (!canvas) {
                    console.error('Revenue product chart canvas not found');
                    return;
                }

                // Check if we have valid data
                const hasProductData = revenueProductData &&
                                      revenueProductData.labels &&
                                      revenueProductData.data &&
                                      Array.isArray(revenueProductData.labels) &&
                                      Array.isArray(revenueProductData.data) &&
                                      revenueProductData.labels.length > 0 &&
                                      revenueProductData.data.length > 0;

                if (hasProductData) {
                    window.financialCharts.revenueProductChart = new Chart(canvas, {
                    type: 'pie',
                    data: {
                        labels: revenueProductData.labels,
                        datasets: [{
                            data: revenueProductData.data,
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.7)',
                                'rgba(54, 162, 235, 0.7)',
                                'rgba(255, 206, 86, 0.7)',
                                'rgba(75, 192, 192, 0.7)',
                                'rgba(153, 102, 255, 0.7)',
                                'rgba(255, 159, 64, 0.7)',
                                'rgba(199, 199, 199, 0.7)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const value = context.parsed;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = Math.round((value / total) * 100);
                                        return context.label + ': ' + percentage + '% (Rp ' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".") + ')';
                                    }
                                }
                            }
                        }
                    }
                });
                    console.log('Revenue product chart created successfully');
                } else {
                    canvas.parentElement.innerHTML = '<p class="text-center text-muted p-4">Tidak ada data pendapatan produk untuk periode ini</p>';
                }
            } catch (error) {
                console.error('Error creating revenue product chart:', error);
                document.getElementById('revenueProductChart').parentElement.innerHTML = '<p class="text-center text-danger p-4">Error loading revenue product chart: ' + error.message + '</p>';
            }
        }, 300);

        // Final completion log
        setTimeout(function() {
            console.log('All financial charts creation completed successfully');
        }, 400);
    });
</script>

<style>
.stats-card {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    height: 100%;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 24px;
    color: white;
}

.stats-icon.success {
    background-color: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.stats-icon.danger {
    background-color: rgba(220, 53, 69, 0.2);
    color: #DC3545;
}

.stats-icon.primary {
    background-color: rgba(13, 110, 253, 0.2);
    color: #0D6EFD;
}

.stats-info {
    flex: 1;
}

.stats-title {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 5px;
}

.stats-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-subtitle, .stats-change {
    font-size: 12px;
    color: #6c757d;
}

.text-success {
    color: #4CAF50 !important;
}

.text-danger {
    color: #DC3545 !important;
}

/* Card Header style */
.card-header {
    background-color: #f8f9fa;
    font-weight: 600;
}
</style>

<script>
function toggleFilterInputs() {
    const filterType = document.getElementById('filter_type').value;
    const monthlyFilter = document.getElementById('monthly_filter');
    const customFilterStart = document.getElementById('custom_filter_start');
    const customFilterEnd = document.getElementById('custom_filter_end');

    if (filterType === 'monthly') {
        monthlyFilter.style.display = '';
        customFilterStart.style.display = 'none';
        customFilterEnd.style.display = 'none';
    } else {
        monthlyFilter.style.display = 'none';
        customFilterStart.style.display = '';
        customFilterEnd.style.display = '';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleFilterInputs();
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/reports/financial.blade.php ENDPATH**/ ?>