<?php $__env->startSection('title', '<PERSON>por<PERSON>'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line me-2"></i><PERSON><PERSON><PERSON>
        </h1>
        <div>
            <a href="<?php echo e(route('reports.export', ['type' => 'financial', 'start_date' => $startDate, 'end_date' => $endDate])); ?>" 
               class="btn btn-success">
                <i class="fas fa-file-excel me-1"></i>Export Excel
            </a>
        </div>
    </div>

    <!-- Filter Form -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Filter Periode</h5>
        </div>
        <div class="card-body">
            <form action="<?php echo e(route('reports.financial')); ?>" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="period" class="form-label">Periode</label>
                    <select class="form-select" id="period" name="period">
                        <option value="today" <?php echo e($period == 'today' ? 'selected' : ''); ?>>Hari Ini</option>
                        <option value="yesterday" <?php echo e($period == 'yesterday' ? 'selected' : ''); ?>>Kemarin</option>
                        <option value="this_week" <?php echo e($period == 'this_week' ? 'selected' : ''); ?>>Minggu Ini</option>
                        <option value="last_week" <?php echo e($period == 'last_week' ? 'selected' : ''); ?>>Minggu Lalu</option>
                        <option value="this_month" <?php echo e($period == 'this_month' ? 'selected' : ''); ?>>Bulan Ini</option>
                        <option value="last_month" <?php echo e($period == 'last_month' ? 'selected' : ''); ?>>Bulan Lalu</option>
                        <option value="custom" <?php echo e($period == 'custom' ? 'selected' : ''); ?>>Custom</option>
                    </select>
                </div>
                <div class="col-md-3 custom-date" style="display: <?php echo e($period == 'custom' ? 'block' : 'none'); ?>">
                    <label for="start_date" class="form-label">Tanggal Mulai</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo e($startDate); ?>">
                </div>
                <div class="col-md-3 custom-date" style="display: <?php echo e($period == 'custom' ? 'block' : 'none'); ?>">
                    <label for="end_date" class="form-label">Tanggal Akhir</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo e($endDate); ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-4 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Pendapatan
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp <?php echo e(number_format($summary['total_revenue'], 0, ',', '.')); ?>

                            </div>
                            <div class="text-xs text-muted">
                                <?php echo e($reportTitle); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Total Biaya
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp <?php echo e(number_format($summary['total_expense'], 0, ',', '.')); ?>

                            </div>
                            <div class="text-xs text-muted">
                                COGS + Operasional
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Laba Bersih
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp <?php echo e(number_format($summary['net_profit'], 0, ',', '.')); ?>

                            </div>
                            <div class="text-xs text-<?php echo e($summary['profit_change'] > 0 ? 'success' : 'danger'); ?>">
                                <i class="fas fa-<?php echo e($summary['profit_change'] > 0 ? 'arrow-up' : 'arrow-down'); ?>"></i>
                                <?php echo e(number_format(abs($summary['profit_change']), 1)); ?>% dari periode sebelumnya
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Revenue vs Expense Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Pendapatan vs Biaya</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="revenueExpenseChart" height="320"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cost Distribution Chart -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Distribusi Biaya</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="costDistributionChart" height="320"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue by Product Chart -->
    <div class="row">
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Pendapatan Berdasarkan Produk</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="revenueProductChart" height="320"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Analysis -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Analisis Kinerja</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-borderless">
                            <tbody>
                                <tr>
                                    <th>Margin Laba Kotor</th>
                                    <td class="text-end"><?php echo e(number_format($details['gross_margin'], 1)); ?>%</td>
                                    <td width="100">
                                        <div class="progress">
                                            <div class="progress-bar bg-success" style="width: <?php echo e(min($details['gross_margin'], 100)); ?>%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Margin Laba Bersih</th>
                                    <td class="text-end"><?php echo e(number_format($details['net_profit_margin'], 1)); ?>%</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar bg-primary" style="width: <?php echo e(min($details['net_profit_margin'], 100)); ?>%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Biaya Operasional</th>
                                    <td class="text-end"><?php echo e(number_format($details['operating_expense_ratio'], 1)); ?>%</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar bg-warning" style="width: <?php echo e(min($details['operating_expense_ratio'], 100)); ?>%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>ROI</th>
                                    <td class="text-end"><?php echo e(number_format($details['roi'], 1)); ?>%</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar bg-info" style="width: <?php echo e(min(abs($details['roi']), 100)); ?>%"></div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// 🎯 BRAND NEW FINANCIAL CHARTS SYSTEM
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initializing NEW Financial Charts...');
    
    // Verify Chart.js
    if (typeof Chart === 'undefined') {
        console.error('❌ Chart.js not loaded');
        return;
    }
    
    console.log('✅ Chart.js ready, version:', Chart.version);
    
    // Chart container
    window.newFinancialCharts = {};
    
    // Create all charts
    createRevenueExpenseChart();
    createCostDistributionChart();
    createRevenueProductChart();
    
    // Period selection handler
    const periodSelect = document.getElementById('period');
    const customDateFields = document.querySelectorAll('.custom-date');
    
    if (periodSelect) {
        periodSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                customDateFields.forEach(field => field.style.display = 'block');
            } else {
                customDateFields.forEach(field => field.style.display = 'none');
            }
        });
    }
});

// 📈 Revenue vs Expense Chart
function createRevenueExpenseChart() {
    console.log('📊 Creating Revenue vs Expense Chart...');
    
    const canvas = document.getElementById('revenueExpenseChart');
    if (!canvas) {
        console.error('❌ Revenue expense chart canvas not found');
        return;
    }
    
    // Sample data - will be replaced with real data
    const chartData = {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Pendapatan',
            data: [12000000, 15000000, 18000000, 14000000, 16000000, 20000000],
            backgroundColor: 'rgba(40, 167, 69, 0.8)',
            borderColor: 'rgba(40, 167, 69, 1)',
            borderWidth: 2
        }, {
            label: 'Biaya',
            data: [8000000, 10000000, 12000000, 9000000, 11000000, 13000000],
            backgroundColor: 'rgba(220, 53, 69, 0.8)',
            borderColor: 'rgba(220, 53, 69, 1)',
            borderWidth: 2
        }]
    };
    
    try {
        window.newFinancialCharts.revenueExpense = new Chart(canvas, {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Pendapatan vs Biaya Bulanan'
                    },
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': Rp ' + 
                                       context.parsed.y.toLocaleString('id-ID');
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rp ' + (value / 1000000) + 'M';
                            }
                        }
                    }
                }
            }
        });
        console.log('✅ Revenue vs Expense Chart created successfully');
    } catch (error) {
        console.error('❌ Error creating revenue expense chart:', error);
        canvas.parentElement.innerHTML = '<div class="alert alert-danger">Error loading chart</div>';
    }
}

// 🍩 Cost Distribution Chart
function createCostDistributionChart() {
    console.log('📊 Creating Cost Distribution Chart...');
    
    const canvas = document.getElementById('costDistributionChart');
    if (!canvas) {
        console.error('❌ Cost distribution chart canvas not found');
        return;
    }
    
    // Sample data
    const chartData = {
        labels: ['Bahan Baku', 'Gaji Karyawan', 'Sewa', 'Utilitas', 'Pemasaran', 'Lainnya'],
        datasets: [{
            data: [45, 25, 15, 8, 5, 2],
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)', 
                'rgba(255, 206, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)',
                'rgba(255, 159, 64, 0.8)'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    };
    
    try {
        window.newFinancialCharts.costDistribution = new Chart(canvas, {
            type: 'doughnut',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Distribusi Biaya Operasional'
                    },
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + '%';
                            }
                        }
                    }
                }
            }
        });
        console.log('✅ Cost Distribution Chart created successfully');
    } catch (error) {
        console.error('❌ Error creating cost distribution chart:', error);
        canvas.parentElement.innerHTML = '<div class="alert alert-danger">Error loading chart</div>';
    }
}

// 🥧 Revenue by Product Chart  
function createRevenueProductChart() {
    console.log('📊 Creating Revenue by Product Chart...');
    
    const canvas = document.getElementById('revenueProductChart');
    if (!canvas) {
        console.error('❌ Revenue product chart canvas not found');
        return;
    }
    
    // Sample data
    const chartData = {
        labels: ['Ubi Bakar Original', 'Ubi Bakar Keju', 'Ubi Bakar Coklat', 'Minuman', 'Snack'],
        datasets: [{
            data: [35, 28, 20, 12, 5],
            backgroundColor: [
                'rgba(139, 69, 19, 0.8)',
                'rgba(255, 140, 0, 0.8)',
                'rgba(210, 180, 140, 0.8)',
                'rgba(70, 130, 180, 0.8)',
                'rgba(255, 20, 147, 0.8)'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    };
    
    try {
        window.newFinancialCharts.revenueProduct = new Chart(canvas, {
            type: 'pie',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Pendapatan Berdasarkan Produk'
                    },
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + '%';
                            }
                        }
                    }
                }
            }
        });
        console.log('✅ Revenue by Product Chart created successfully');
    } catch (error) {
        console.error('❌ Error creating revenue product chart:', error);
        canvas.parentElement.innerHTML = '<div class="alert alert-danger">Error loading chart</div>';
    }
}

console.log('🎉 All NEW Financial Charts initialized successfully!');
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/reports/financial.blade.php ENDPATH**/ ?>