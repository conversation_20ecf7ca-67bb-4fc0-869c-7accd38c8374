

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-chart-line"></i>
        <span><PERSON><PERSON><PERSON></span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <span>Filter Laporan</span>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('reports.sales')); ?>" method="GET" class="row g-3">
                        <!-- Filter Type Selection -->
                        <div class="col-md-3">
                            <label for="filter_type" class="form-label">Jenis Filter</label>
                            <select class="form-select" id="filter_type" name="filter_type" onchange="toggleSalesFilterInputs()">
                                <option value="monthly" <?php echo e(($filterType ?? 'monthly') == 'monthly' ? 'selected' : ''); ?>>Per Bulan</option>
                                <option value="custom" <?php echo e(($filterType ?? 'monthly') == 'custom' ? 'selected' : ''); ?>>Rentang Tanggal</option>
                            </select>
                        </div>

                        <!-- Monthly Filter -->
                        <div class="col-md-3" id="sales_monthly_filter" style="<?php echo e(($filterType ?? 'monthly') == 'monthly' ? '' : 'display: none;'); ?>">
                            <label for="selected_month" class="form-label">Pilih Bulan</label>
                            <input type="month" class="form-control" id="selected_month" name="selected_month"
                                   value="<?php echo e($selectedMonth ?? date('Y-m')); ?>">
                        </div>

                        <!-- Custom Date Range Filter -->
                        <div class="col-md-3" id="sales_custom_filter_start" style="<?php echo e(($filterType ?? 'monthly') == 'custom' ? '' : 'display: none;'); ?>">
                            <label for="start_date" class="form-label">Tanggal Awal</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo e($startDate); ?>">
                        </div>

                        <div class="col-md-3" id="sales_custom_filter_end" style="<?php echo e(($filterType ?? 'monthly') == 'custom' ? '' : 'display: none;'); ?>">
                            <label for="end_date" class="form-label">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo e($endDate); ?>">
                        </div>

                        <!-- Submit Button -->
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid gap-2 d-md-flex">
                                <button type="submit" class="btn btn-primary flex-fill">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="<?php echo e(route('reports.sales')); ?>" class="btn btn-outline-secondary flex-fill">
                                    <i class="fas fa-refresh"></i> Reset
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>



    <div class="row mb-4">
        <div class="col-md-4 mb-4">
            <div class="stats-card">
                <div class="stats-icon primary">
                    <i class="fas fa-cash-register"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Total Transaksi</div>
                    <h3 class="stats-value"><?php echo e($summary['total_transactions']); ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="stats-card">
                <div class="stats-icon success">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Total Pendapatan</div>
                    <h3 class="stats-value">Rp <?php echo e(number_format($summary['total_sales'], 0, ',', '.')); ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="stats-card">
                <div class="stats-icon warning">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Rata-rata per Transaksi</div>
                    <h3 class="stats-value">Rp <?php echo e(number_format($summary['average_transaction'], 0, ',', '.')); ?></h3>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <span>Grafik Penjualan <?php echo e($reportTitle); ?></span>
                </div>
                <div class="card-body">
                    <canvas id="salesChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <span>Produk Terlaris</span>
                </div>
                <div class="card-body">
                    <canvas id="productChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Detail Transaksi</span>
                    <a href="<?php echo e(route('reports.export', ['type' => 'sales', 'start_date' => request('start_date'), 'end_date' => request('end_date')])); ?>" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> Export Excel
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>Tanggal</th>
                                    <th>No. Invoice</th>
                                    <th>Jumlah Item</th>
                                    <th>Total</th>
                                    <th>Metode Pembayaran</th>
                                    <th>Kasir</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($transaction->created_at->format('d M Y H:i')); ?></td>
                                    <td><?php echo e($transaction->invoice_number); ?></td>
                                    <td><?php echo e($transaction->items_count); ?> item</td>
                                    <td>Rp <?php echo e(number_format($transaction->total_amount, 0, ',', '.')); ?></td>
                                    <td><?php echo e($transaction->payment_method); ?></td>
                                    <td><?php echo e($transaction->user->name ?? 'Admin'); ?></td>
                                    <td>
                                        <a href="<?php echo e(route('transactions.show', $transaction)); ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center">Tidak ada data transaksi</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4">
                        <?php echo e($transactions->appends(request()->query())->links('custom.pagination')); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not loaded!');
            document.getElementById('salesChart').parentElement.innerHTML = '<p class="text-center text-danger p-4">Chart.js library tidak ter-load. Silakan refresh halaman.</p>';
            document.getElementById('productChart').parentElement.innerHTML = '<p class="text-center text-danger p-4">Chart.js library tidak ter-load. Silakan refresh halaman.</p>';
            return;
        }

        console.log('Chart.js loaded successfully, version:', Chart.version);

        // Period selection toggling
        const periodSelect = document.getElementById('period');
        const customDateFields = document.querySelectorAll('.custom-date');

        if (periodSelect) {
            periodSelect.addEventListener('change', function() {
                if (this.value === 'custom') {
                    customDateFields.forEach(field => {
                        field.style.display = '';
                    });
                } else {
                    customDateFields.forEach(field => {
                        field.style.display = 'none';
                    });
                    // Auto submit form when period changes (except custom)
                    this.form.submit();
                }
            });
        }
        
        // Sales Chart - with delay to ensure Chart.js is ready
        setTimeout(function() {
            try {
                const salesChartData = <?php echo json_encode($salesChartData ?? ['labels' => [], 'data' => []], 512) ?>;
                console.log('Sales Chart Data:', salesChartData);

                const salesCanvas = document.getElementById('salesChart');
                if (!salesCanvas) {
                    console.error('Sales chart canvas not found');
                    return;
                }

                if (salesChartData && salesChartData.labels && salesChartData.data && salesChartData.labels.length > 0) {
                    const salesChart = new Chart(salesCanvas, {
                        type: 'line',
                        data: {
                            labels: salesChartData.labels,
                            datasets: [{
                                label: 'Penjualan (Rp)',
                                data: salesChartData.data,
                                backgroundColor: 'rgba(139, 69, 19, 0.2)',
                                borderColor: 'rgba(139, 69, 19, 1)',
                                borderWidth: 2,
                                tension: 0.3,
                                fill: true
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        callback: function(value) {
                                            return 'Rp ' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
                                        }
                                    }
                                }
                            },
                            plugins: {
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return 'Penjualan: Rp ' + context.parsed.y.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
                                        }
                                    }
                                }
                            }
                        }
                    });
                    console.log('Sales chart created successfully');
                } else {
                    salesCanvas.parentElement.innerHTML = '<p class="text-center text-muted p-4">Tidak ada data penjualan untuk periode ini</p>';
                }
            } catch (error) {
                console.error('Error creating sales chart:', error);
                const salesCanvas = document.getElementById('salesChart');
                if (salesCanvas) {
                    salesCanvas.parentElement.innerHTML = '<p class="text-center text-danger p-4">Error loading sales chart: ' + error.message + '</p>';
                }
            }
        }, 100);
        
        // Product Chart - with delay to ensure Chart.js is ready
        setTimeout(function() {
            try {
                const productChartData = <?php echo json_encode($topProductsChart ?? ['labels' => [], 'data' => []], 512) ?>;
                console.log('Product Chart Data:', productChartData);

                const productCanvas = document.getElementById('productChart');
                if (!productCanvas) {
                    console.error('Product chart canvas not found');
                    return;
                }

                if (productChartData && productChartData.labels && productChartData.data && productChartData.labels.length > 0) {
                    const productChart = new Chart(productCanvas, {
                        type: 'doughnut',
                        data: {
                            labels: productChartData.labels,
                            datasets: [{
                                data: productChartData.data,
                                backgroundColor: [
                                    'rgba(139, 69, 19, 0.7)',
                                    'rgba(255, 140, 0, 0.7)',
                                    'rgba(210, 105, 30, 0.7)',
                                    'rgba(160, 82, 45, 0.7)',
                                    'rgba(205, 133, 63, 0.7)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            const value = context.parsed;
                                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                            const percentage = Math.round((value / total) * 100);
                                            return context.label + ': ' + percentage + '% (' + value + ' terjual)';
                                        }
                                    }
                                }
                            }
                        }
                    });
                    console.log('Product chart created successfully');
                } else {
                    productCanvas.parentElement.innerHTML = '<p class="text-center text-muted p-4">Tidak ada data produk untuk periode ini</p>';
                }
            } catch (error) {
                console.error('Error creating product chart:', error);
                const productCanvas = document.getElementById('productChart');
                if (productCanvas) {
                    productCanvas.parentElement.innerHTML = '<p class="text-center text-danger p-4">Error loading product chart: ' + error.message + '</p>';
                }
            }
        }, 200);
        } catch (error) {
            console.error('Error creating product chart:', error);
            document.getElementById('productChart').parentElement.innerHTML = '<p class="text-center text-danger p-4">Error loading product chart: ' + error.message + '</p>';
        }
    });

    // Function to toggle filter inputs for sales report
    function toggleSalesFilterInputs() {
        const filterType = document.getElementById('filter_type').value;
        const monthlyFilter = document.getElementById('sales_monthly_filter');
        const customFilterStart = document.getElementById('sales_custom_filter_start');
        const customFilterEnd = document.getElementById('sales_custom_filter_end');

        if (filterType === 'monthly') {
            monthlyFilter.style.display = '';
            customFilterStart.style.display = 'none';
            customFilterEnd.style.display = 'none';
        } else {
            monthlyFilter.style.display = 'none';
            customFilterStart.style.display = '';
            customFilterEnd.style.display = '';
        }
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        toggleSalesFilterInputs();
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\FINAL!\FINAL !!!!!\ubi-bakar-cilembu - Copy\resources\views/reports/sales.blade.php ENDPATH**/ ?>