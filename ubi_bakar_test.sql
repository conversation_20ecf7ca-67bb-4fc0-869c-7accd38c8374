-- Test file untuk memastikan HeidiSQL dapat membaca file SQL
-- Jika file ini bisa dibaca, maka masalahnya ada di file utama

SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;

CREATE DATABASE IF NOT EXISTS `ubi_bakar_test` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `ubi_bakar_test`;

-- Test table
CREATE TABLE `test_table` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Test data
INSERT INTO `test_table` (`id`, `name`) VALUES
(1, 'Test Data 1'),
(2, 'Test Data 2'),
(3, 'Test Data 3');

SET FOREIGN_KEY_CHECKS = 1;
COMMIT;

-- End of test file
