<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Traits\Auditable;

class TransactionItem extends Model
{
    use HasFactory, Auditable;

    protected $table = 'transaction_items';

    protected $fillable = [
        'transaction_id',
        'product_id',
        'processed_inventory_id',
        'product_name',
        'price',
        'quantity',
        'discount',
        'subtotal'
    ];

    /**
     * Get the transaction that this item belongs to.
     */
    public function transaction(): BelongsTo
    {
        return $this->belongsTo(Transaction::class);
    }

    /**
     * Get the product that this item represents.
     * This could be either a ProcessedInventory or OtherProduct.
     */
    public function product(): BelongsTo
    {
        // If processed_inventory_id is set, return ProcessedInventory relationship
        if ($this->processed_inventory_id) {
            return $this->belongsTo(ProcessedInventory::class, 'processed_inventory_id');
        }
        
        // Otherwise, return OtherProduct relationship
        return $this->belongsTo(OtherProduct::class, 'product_id');
    }

    /**
     * Get the processed inventory that this item represents (if applicable).
     */
    public function processedInventory(): BelongsTo
    {
        return $this->belongsTo(ProcessedInventory::class, 'processed_inventory_id');
    }
    
    /**
     * Get the other product that this item represents (if applicable).
     */
    public function otherProduct(): BelongsTo
    {
        // Only return relationship if this item is not a processed inventory
        if (!$this->processed_inventory_id) {
            return $this->belongsTo(OtherProduct::class, 'product_id');
        }
        
        // Return null relationship if this is a processed inventory item
        return $this->belongsTo(OtherProduct::class, 'product_id')->whereNull('id');
    }
}
