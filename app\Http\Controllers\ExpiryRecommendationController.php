<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ProcessedInventory;
use App\Models\Distribution;
use App\Exports\ExpiryRecommendationExport;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;

class ExpiryRecommendationController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of the recommendations.
     */
    public function index()
    {
        // Update expiry tracking data for all processed inventory items
        $recommendedItems = ProcessedInventory::getExpiryTrackingList();
        
        // Summary stats
        $highPriorityCount = $recommendedItems->where('priority_level', 'Tinggi')->count();
        $mediumPriorityCount = $recommendedItems->where('priority_level', 'Sedang')->count();
        $lowPriorityCount = $recommendedItems->where('priority_level', 'Rendah')->count();
        $totalPotentialLoss = $recommendedItems->where('priority_level', 'Tinggi')
                                               ->sum(function($item) {
                                                   return $item->current_stock * $item->cost_per_unit;
                                               });
        
        // Get available markets for distribution
        $markets = Distribution::select('market_name')
                               ->distinct()
                               ->orderBy('market_name')
                               ->pluck('market_name');
        
        return view('inventory.expiry-recommendations', compact(
            'recommendedItems',
            'highPriorityCount',
            'mediumPriorityCount',
            'lowPriorityCount',
            'totalPotentialLoss',
            'markets'
        ));
    }
    
    /**
     * Update recommendation for a specific item
     */
    public function updateRecommendation($id)
    {
        $item = ProcessedInventory::findOrFail($id);
        $item->updateExpiryTracking();
        
        return redirect()->back()->with('success', 'Rekomendasi berhasil diperbarui');
    }
    
    /**
     * Update all recommendations
     */
    public function updateAllRecommendations()
    {
        $items = ProcessedInventory::where('is_active', true)
                                   ->where('current_stock', '>', 0)
                                   ->where('expiry_date', '!=', null)
                                   ->get();
                                   
        foreach($items as $item) {
            $item->updateExpiryTracking();
        }
        
        return redirect()->back()->with('success', 'Semua rekomendasi berhasil diperbarui');
    }
    
    /**
     * Export recommendations to Excel
     */
    public function exportExcel()
    {
        $recommendations = ProcessedInventory::where('is_active', true)
            ->where('current_stock', '>', 0)
            ->whereRaw('expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)')
            ->orderBy('expiry_date', 'asc')
            ->get();

        $filename = 'expiry_recommendations_' . date('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($recommendations) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fwrite($file, "\xEF\xBB\xBF");

            // Add headers
            fputcsv($file, [
                'Nama Produk',
                'Batch Number',
                'Stok Saat Ini',
                'Tanggal Kadaluarsa',
                'Hari Tersisa',
                'Status Prioritas',
                'Rekomendasi'
            ]);

            // Add data
            foreach ($recommendations as $item) {
                $daysLeft = \Carbon\Carbon::parse($item->expiry_date)->diffInDays(now(), false);
                $priority = $daysLeft <= 1 ? 'URGENT' : ($daysLeft <= 3 ? 'HIGH' : 'MEDIUM');
                $recommendation = $daysLeft <= 1 ? 'Distribusi segera atau diskon besar' :
                                ($daysLeft <= 3 ? 'Distribusi prioritas atau promo' : 'Monitor dan rencanakan distribusi');

                fputcsv($file, [
                    $item->name,
                    $item->batch_number,
                    $item->current_stock,
                    \Carbon\Carbon::parse($item->expiry_date)->format('d/m/Y'),
                    $daysLeft . ' hari',
                    $priority,
                    $recommendation
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
    
    /**
     * Export recommendations to PDF
     */
    public function exportPdf()
    {
        $recommendedItems = ProcessedInventory::getExpiryTrackingList();
        
        $pdf = PDF::loadView('exports.expiry-recommendations-pdf', [
            'recommendedItems' => $recommendedItems,
            'date' => now()->format('d M Y')
        ]);
        
        return $pdf->download('rekomendasi-penjualan-ubi-matang-' . now()->format('Y-m-d') . '.pdf');
    }
    
    /**
     * Generate distribution report for expiring items
     */
    public function distributionReport(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        // Get ALL distributions in the date range (not just expiring items)
        // This will show all distributions, making the report more useful
        $distributions = Distribution::whereBetween('distribution_date', [$startDate, $endDate])
            ->with(['items.processedInventory', 'items.otherProduct', 'user'])
            ->orderBy('distribution_date', 'desc')
            ->get();

        // If you want to filter only expiring items, uncomment below:
        /*
        $distributions = Distribution::whereBetween('distribution_date', [$startDate, $endDate])
            ->with(['items.processedInventory' => function($query) {
                $query->whereRaw('expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)');
            }])
            ->whereHas('items.processedInventory', function($query) {
                $query->whereRaw('expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)');
            })
            ->orderBy('distribution_date', 'desc')
            ->get();
        */

        // Calculate statistics for ALL distributions
        $totalDistributions = $distributions->count();
        $totalItems = $distributions->sum(function($distribution) {
            return $distribution->items->sum('quantity');
        });

        $totalValue = $distributions->sum(function($distribution) {
            return $distribution->items->sum('total_price');
        });

        // Get market performance data
        $marketPerformance = $distributions->groupBy('market_name')->map(function($items, $market) {
            return [
                'market' => $market,
                'distributions' => $items->count(),
                'total_items' => $items->sum(function($distribution) {
                    return $distribution->items->where('processedInventory.expiry_date', '<=', now()->addDays(7))->sum('quantity');
                }),
                'total_value' => $items->sum(function($distribution) {
                    return $distribution->items->where('processedInventory.expiry_date', '<=', now()->addDays(7))->sum('total_price');
                })
            ];
        })->values();

        return view('inventory.distribution-report', compact(
            'distributions',
            'startDate',
            'endDate',
            'totalDistributions',
            'totalItems',
            'totalValue',
            'marketPerformance'
        ));
    }





    private function getSalesData($startDate, $endDate, $marketFilter = null)
    {
        $query = \App\Models\TransactionItem::select([
            'transaction_items.product_name',
            'transaction_items.quantity',
            'transaction_items.price',
            'transaction_items.subtotal',
            'transactions.created_at',
            'transactions.customer_name',
            'transactions.payment_method'
        ])
        ->join('transactions', 'transaction_items.transaction_id', '=', 'transactions.id')
        ->where('transactions.status', 'completed')
        ->whereBetween('transactions.created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);

        if ($marketFilter) {
            $query->where('transactions.customer_name', 'like', '%' . $marketFilter . '%');
        }

        return $query->orderBy('transactions.created_at', 'desc')->get();
    }

    private function exportSalesExcel($salesData, $startDate, $endDate)
    {
        $filename = 'sales_report_' . $startDate . '_to_' . $endDate . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($salesData) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fwrite($file, "\xEF\xBB\xBF");

            // Add headers
            fputcsv($file, [
                'Tanggal',
                'Nama Produk',
                'Quantity',
                'Harga Satuan',
                'Subtotal',
                'Customer',
                'Metode Pembayaran'
            ]);

            // Add data
            foreach ($salesData as $row) {
                fputcsv($file, [
                    \Carbon\Carbon::parse($row->created_at)->format('d/m/Y H:i'),
                    $row->product_name,
                    $row->quantity,
                    'Rp ' . number_format($row->price, 0, ',', '.'),
                    'Rp ' . number_format($row->subtotal, 0, ',', '.'),
                    $row->customer_name ?: '-',
                    ucfirst($row->payment_method)
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Show form to create distribution for expiring item
     */
    public function createDistribution($id)
    {
        $item = ProcessedInventory::findOrFail($id);

        // Get available markets
        $markets = Distribution::select('market_name')
                               ->distinct()
                               ->orderBy('market_name')
                               ->pluck('market_name');

        return view('inventory.create-distribution', compact('item', 'markets'));
    }

    /**
     * Store distribution for expiring item - COMPLETELY REWRITTEN
     */
    public function storeDistribution(Request $request)
    {
        // Validasi input
        $validated = $request->validate([
            'processed_inventory_id' => 'required|exists:processed_inventory,id',
            'destination' => 'required|string|max:255',
            'quantity' => 'required|integer|min:1',
            'distribution_date' => 'required|date|after_or_equal:today',
            'notes' => 'nullable|string|max:500'
        ]);

        try {
            DB::beginTransaction();

            // Ambil data produk
            $product = ProcessedInventory::find($validated['processed_inventory_id']);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Produk tidak ditemukan!'
                ], 404);
            }

            // Cek stok tersedia
            if ($validated['quantity'] > $product->current_stock) {
                return response()->json([
                    'success' => false,
                    'message' => 'Stok tidak mencukupi! Tersedia: ' . $product->current_stock . ' unit'
                ], 400);
            }

            // Generate nomor distribusi unik
            $distributionNumber = 'DIST-' . date('Ymd') . '-' . str_pad((Distribution::whereDate('created_at', today())->count() + 1), 4, '0', STR_PAD_LEFT);

            // Buat record distribusi
            $distribution = Distribution::create([
                'distribution_number' => $distributionNumber,
                'user_id' => auth()->id(),
                'market_name' => $validated['destination'],
                'distribution_date' => $validated['distribution_date'],
                'notes' => $validated['notes'],
                'status' => 'planned'
            ]);

            // Buat item distribusi
            $distributionItem = $distribution->items()->create([
                'processed_inventory_id' => $product->id,
                'other_product_id' => null,
                'quantity' => $validated['quantity'],
                'price_per_item' => $product->selling_price ?? 0,
                'total_price' => $validated['quantity'] * ($product->selling_price ?? 0)
            ]);

            // Update stok produk
            $product->decrement('current_stock', $validated['quantity']);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Distribusi berhasil dibuat!',
                'data' => [
                    'distribution_number' => $distributionNumber,
                    'destination' => $validated['destination'],
                    'quantity' => $validated['quantity'],
                    'date' => Carbon::parse($validated['distribution_date'])->format('d/m/Y')
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            \Log::error('Distribution Creation Failed:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'input' => $validated ?? $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gagal membuat distribusi: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show sales report for distributed items
     */
    public function salesReport(Request $request)
    {
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));

        $distributions = Distribution::with(['items.processedInventory', 'user'])
                                   ->whereBetween('distribution_date', [$startDate, $endDate])
                                   ->where('is_urgent', true)
                                   ->orderBy('distribution_date', 'desc')
                                   ->get();

        $summary = [
            'total_distributions' => $distributions->count(),
            'total_items' => $distributions->sum(function($dist) {
                return $dist->items->sum('quantity');
            }),
            'total_value' => $distributions->sum(function($dist) {
                return $dist->items->sum('subtotal');
            })
        ];

        return view('inventory.sales-report', compact('distributions', 'summary', 'startDate', 'endDate'));
    }
}
